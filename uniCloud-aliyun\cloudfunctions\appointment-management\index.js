'use strict';

const uniID = require('uni-id-common');

// 通用 checkToken 函数
async function checkUserToken(token, context) {
  const uniIdIns = uniID.createInstance({ context });
  const payload = await uniIdIns.checkToken(token);

  if (payload.errCode === 0) {
    return {
      code: 0,
      uid: payload.uid,
      userInfo: payload.userInfo
    };
  } else {
    return {
      code: payload.errCode || 401,
      message: payload.errMsg || '身份验证失败'
    };
  }
}

exports.main = async (event, context) => {
  const { action, data } = event;
  
  try {
    switch (action) {
      case 'createAppointment':
        return await createAppointment(event, context);
      case 'updateAppointment':
        return await updateAppointment(event, context);
      case 'cancelAppointment':
        return await cancelAppointment(event, context);
      case 'getAppointmentList':
        return await getAppointmentList(event, context);
      case 'getMyAppointments':
        return await getAppointmentList(event, context);
      case 'getPublisherAppointments':
        return await getPublisherAppointments(event, context);
      default:
        return {
          code: 400,
          message: '无效的操作'
        };
    }
  } catch (error) {
    console.error('预约管理云函数执行错误:', error);
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message
    };
  }
};

// 创建预约
async function createAppointment(event, context) {
  const payload = await checkUserToken(event.uniIdToken, context);
  if (payload.code !== 0) {
    return payload;
  }
  
  const { house_id, appointment_date, contact_phone, message } = event.data;
  
  if (!house_id || !appointment_date || !contact_phone) {
    return {
      code: 400,
      message: '房源ID、预约时间和联系电话不能为空'
    };
  }
  
  try {
    const db = uniCloud.database();
    
    // 检查房源是否存在
    const houseRes = await db.collection('houses').doc(house_id).get();
    if (houseRes.data.length === 0) {
      return {
        code: 404,
        message: '房源不存在'
      };
    }
    
    const house = houseRes.data[0];
    
    // 检查是否是自己的房源
    if (house.publisher_id === payload.uid) {
      return {
        code: 400,
        message: '不能预约自己的房源'
      };
    }
    
    // 检查预约时间是否合理（不能是过去的时间）
    const appointmentTime = new Date(appointment_date);
    const now = new Date();
    if (appointmentTime <= now) {
      return {
        code: 400,
        message: '预约时间不能是过去的时间'
      };
    }
    
    // 检查是否已有相同时间的预约
    const existingRes = await db.collection('appointments').where({
      user_id: payload.uid,
      house_id: house_id,
      appointment_date: appointmentTime,
      status: db.command.in(['pending', 'confirmed'])
    }).get();
    
    if (existingRes.data.length > 0) {
      return {
        code: 400,
        message: '该时间段已有预约'
      };
    }
    
    // 创建预约记录
    const appointment = {
      user_id: payload.uid,
      house_id: house_id,
      publisher_id: house.publisher_id,
      appointment_date: appointmentTime,
      contact_phone: contact_phone,
      message: message || '',
      status: 'pending',
      create_date: new Date(),
      update_date: new Date()
    };
    
    const result = await db.collection('appointments').add(appointment);
    
    // 发送通知给发布者
    await db.collection('messages').add({
      from_user_id: payload.uid,
      to_user_id: house.publisher_id,
      type: 'appointment',
      title: '新的看房预约',
      content: `有用户预约看房：${house.title}，预约时间：${appointment_date}`,
      related_id: result.id,
      is_read: false,
      create_date: new Date()
    });
    
    return {
      code: 0,
      message: '预约成功',
      data: {
        appointment_id: result.id
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '预约失败',
      error: error.message
    };
  }
}

// 更新预约状态
async function updateAppointment(event, context) {
  const payload = await checkUserToken(event.uniIdToken, context);
  if (payload.code !== 0) {
    return payload;
  }
  
  const { appointment_id, status } = event.data;
  
  if (!appointment_id || !status) {
    return {
      code: 400,
      message: '预约ID和状态不能为空'
    };
  }
  
  const validStatus = ['pending', 'confirmed', 'cancelled', 'completed'];
  if (!validStatus.includes(status)) {
    return {
      code: 400,
      message: '无效的状态值'
    };
  }
  
  try {
    const db = uniCloud.database();
    
    // 检查预约是否存在
    const appointmentRes = await db.collection('appointments').doc(appointment_id).get();
    if (appointmentRes.data.length === 0) {
      return {
        code: 404,
        message: '预约不存在'
      };
    }
    
    const appointment = appointmentRes.data[0];
    
    // 检查权限（只有预约者或发布者可以更新）
    if (appointment.user_id !== payload.uid && appointment.publisher_id !== payload.uid) {
      return {
        code: 403,
        message: '无权限操作此预约'
      };
    }
    
    // 更新预约状态
    await db.collection('appointments').doc(appointment_id).update({
      status: status,
      update_date: new Date()
    });
    
    // 发送状态更新通知
    const isPublisher = appointment.publisher_id === payload.uid;
    const targetUserId = isPublisher ? appointment.user_id : appointment.publisher_id;
    
    let notificationContent = '';
    switch (status) {
      case 'confirmed':
        notificationContent = '您的看房预约已确认';
        break;
      case 'cancelled':
        notificationContent = '看房预约已取消';
        break;
      case 'completed':
        notificationContent = '看房已完成';
        break;
    }
    
    if (notificationContent) {
      await db.collection('messages').add({
        from_user_id: payload.uid,
        to_user_id: targetUserId,
        type: 'appointment',
        title: '预约状态更新',
        content: notificationContent,
        related_id: appointment_id,
        is_read: false,
        create_date: new Date()
      });
    }
    
    return {
      code: 0,
      message: '状态更新成功'
    };
  } catch (error) {
    return {
      code: 500,
      message: '状态更新失败',
      error: error.message
    };
  }
}

// 取消预约
async function cancelAppointment(event, context) {
  return await updateAppointment({
    ...event,
    data: {
      ...event.data,
      status: 'cancelled'
    }
  }, context);
}

// 获取用户预约列表
async function getAppointmentList(event, context) {
  const payload = await checkUserToken(event.uniIdToken, context);
  if (payload.code !== 0) {
    return payload;
  }
  
  const { page = 1, pageSize = 10, status, countOnly = false } = event.data;

  try {
    const db = uniCloud.database();

    let query = db.collection('appointments').where({
      user_id: payload.uid
    });

    if (status) {
      query = query.where({
        status: status
      });
    }

    // 如果只需要数量，直接返回count结果
    if (countOnly) {
      const countResult = await query.count();
      return {
        code: 0,
        message: '获取成功',
        data: {
          total: countResult.total
        }
      };
    }

    const skip = (page - 1) * pageSize;
    const result = await query.skip(skip).limit(pageSize).orderBy('create_date', 'desc').get();
    
    // 获取房源信息
    const houseIds = result.data.map(item => item.house_id);
    let houses = [];
    
    if (houseIds.length > 0) {
      const houseRes = await db.collection('houses').where({
        _id: db.command.in(houseIds)
      }).get();
      houses = houseRes.data;
    }
    
    // 组合数据
    const list = result.data.map(appointment => {
      const house = houses.find(h => h._id === appointment.house_id);
      return {
        ...appointment,
        house: house || null
      };
    });
    
    // 获取总数
    const countResult = await query.count();
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        list: list,
        total: countResult.total,
        page: page,
        pageSize: pageSize
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取失败',
      error: error.message
    };
  }
}

// 获取发布者的预约列表
async function getPublisherAppointments(event, context) {
  const payload = await checkUserToken(event.uniIdToken, context);
  if (payload.code !== 0) {
    return payload;
  }
  
  const { page = 1, pageSize = 10, status } = event.data;
  
  try {
    const db = uniCloud.database();
    
    let query = db.collection('appointments').where({
      publisher_id: payload.uid
    });
    
    if (status) {
      query = query.where({
        status: status
      });
    }
    
    const skip = (page - 1) * pageSize;
    const result = await query.skip(skip).limit(pageSize).orderBy('create_date', 'desc').get();
    
    // 获取房源和用户信息
    const houseIds = result.data.map(item => item.house_id);
    const userIds = result.data.map(item => item.user_id);
    
    let houses = [];
    let users = [];
    
    if (houseIds.length > 0) {
      const houseRes = await db.collection('houses').where({
        _id: db.command.in(houseIds)
      }).get();
      houses = houseRes.data;
    }
    
    if (userIds.length > 0) {
      const userRes = await db.collection('uni-id-users').where({
        _id: db.command.in(userIds)
      }).field({
        nickname: true,
        avatar: true,
        mobile: true
      }).get();
      users = userRes.data;
    }
    
    // 组合数据
    const list = result.data.map(appointment => {
      const house = houses.find(h => h._id === appointment.house_id);
      const user = users.find(u => u._id === appointment.user_id);
      return {
        ...appointment,
        house: house || null,
        user: user || null
      };
    });
    
    // 获取总数
    const countResult = await query.count();
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        list: list,
        total: countResult.total,
        page: page,
        pageSize: pageSize
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取失败',
      error: error.message
    };
  }
}
