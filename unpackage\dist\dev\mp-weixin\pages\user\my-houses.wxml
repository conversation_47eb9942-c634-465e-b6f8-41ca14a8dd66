<view class="my-houses-container data-v-ddfcefee"><view class="status-tabs data-v-ddfcefee"><block wx:for="{{statusTabs}}" wx:for-item="tab" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['switchTab',[index]]]]]}}" class="{{['tab-item','data-v-ddfcefee',(currentTab===index)?'active':'']}}" bindtap="__e"><text class="tab-text data-v-ddfcefee">{{tab.label}}</text><block wx:if="{{tab.count>0}}"><text class="tab-count data-v-ddfcefee">{{tab.count}}</text></block></view></block></view><scroll-view class="house-list data-v-ddfcefee" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><block wx:for="{{$root.l0}}" wx:for-item="house" wx:for-index="__i0__" wx:key="_id"><view class="house-item data-v-ddfcefee"><view data-event-opts="{{[['tap',[['toHouseDetail',['$0'],[[['houseList','_id',house.$orig._id,'_id']]]]]]]}}" class="house-image data-v-ddfcefee" bindtap="__e"><image src="{{house.$orig.images&&house.$orig.images[0]||'/static/default-house.png'}}" mode="aspectFill" class="data-v-ddfcefee"></image><view class="{{['house-status','data-v-ddfcefee',house.$orig.status]}}">{{house.m0}}</view></view><view data-event-opts="{{[['tap',[['toHouseDetail',['$0'],[[['houseList','_id',house.$orig._id,'_id']]]]]]]}}" class="house-info data-v-ddfcefee" bindtap="__e"><text class="house-title data-v-ddfcefee">{{house.$orig.title}}</text><view class="house-tags data-v-ddfcefee"><text class="tag data-v-ddfcefee">{{house.m1}}</text><block wx:if="{{house.$orig.room_count}}"><text class="tag data-v-ddfcefee">{{house.$orig.room_count+"室"}}</text></block><block wx:if="{{house.$orig.hall_count}}"><text class="tag data-v-ddfcefee">{{house.$orig.hall_count+"厅"}}</text></block><block wx:if="{{house.$orig.area}}"><text class="tag data-v-ddfcefee">{{house.$orig.area+"㎡"}}</text></block></view><view class="house-location data-v-ddfcefee"><uni-icons vue-id="{{'e1a00bc6-1-'+__i0__}}" type="location" size="12" color="#999" class="data-v-ddfcefee" bind:__l="__l"></uni-icons><text class="location-text data-v-ddfcefee">{{house.$orig.location.district+" "+house.$orig.location.address}}</text></view><view class="house-bottom data-v-ddfcefee"><view class="price-info data-v-ddfcefee"><text class="price data-v-ddfcefee">{{"¥"+house.$orig.price}}</text><text class="price-unit data-v-ddfcefee">/月</text></view><view class="house-stats data-v-ddfcefee"><text class="stat-item data-v-ddfcefee"><uni-icons vue-id="{{'e1a00bc6-2-'+__i0__}}" type="eye" size="12" color="#999" class="data-v-ddfcefee" bind:__l="__l"></uni-icons>{{''+(house.$orig.view_count||0)+''}}</text><text class="stat-item data-v-ddfcefee"><uni-icons vue-id="{{'e1a00bc6-3-'+__i0__}}" type="heart" size="12" color="#999" class="data-v-ddfcefee" bind:__l="__l"></uni-icons>{{''+(house.$orig.favorite_count||0)+''}}</text></view></view><text class="publish-time data-v-ddfcefee">{{"发布时间："+house.m2}}</text></view><view class="house-actions data-v-ddfcefee"><block wx:if="{{house.$orig.status!=='rented'}}"><button data-event-opts="{{[['tap',[['editHouse',['$0'],[[['houseList','_id',house.$orig._id,'_id']]]]]]]}}" class="action-btn edit data-v-ddfcefee" bindtap="__e">编辑</button></block><block wx:if="{{house.$orig.status!=='rented'}}"><button data-event-opts="{{[['tap',[['toggleStatus',['$0'],[[['houseList','_id',house.$orig._id]]]]]]]}}" class="action-btn status data-v-ddfcefee" bindtap="__e">{{''+(house.$orig.status==='available'?'下线':'上线')+''}}</button></block><button data-event-opts="{{[['tap',[['deleteHouse',['$0'],[[['houseList','_id',house.$orig._id,'_id']]]]]]]}}" class="action-btn delete data-v-ddfcefee" bindtap="__e">删除</button></view></view></block><block wx:if="{{$root.g0>0}}"><view class="load-status data-v-ddfcefee"><block wx:if="{{loading}}"><text class="data-v-ddfcefee">加载中...</text></block><block wx:else><block wx:if="{{noMore}}"><text class="data-v-ddfcefee">没有更多了</text></block></block></view></block><block wx:if="{{$root.g1}}"><view class="empty-state data-v-ddfcefee"><image src="/static/empty-house.png" mode="aspectFit" class="data-v-ddfcefee"></image><text class="empty-text data-v-ddfcefee">暂无房源发布</text><button data-event-opts="{{[['tap',[['toPublish',['$event']]]]]}}" class="publish-btn data-v-ddfcefee" bindtap="__e">立即发布</button></view></block></scroll-view><view data-event-opts="{{[['tap',[['toPublish',['$event']]]]]}}" class="floating-btn data-v-ddfcefee" bindtap="__e"><uni-icons vue-id="e1a00bc6-4" type="plus" size="24" color="#fff" class="data-v-ddfcefee" bind:__l="__l"></uni-icons></view></view>