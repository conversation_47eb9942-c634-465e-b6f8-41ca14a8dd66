{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端/pages/house/list.vue?be94", "webpack:///D:/web/project/前端/pages/house/list.vue?5a8c", "webpack:///D:/web/project/前端/pages/house/list.vue?3250", "webpack:///D:/web/project/前端/pages/house/list.vue?42a7", "uni-app:///pages/house/list.vue", "webpack:///D:/web/project/前端/pages/house/list.vue?dfc0", "webpack:///D:/web/project/前端/pages/house/list.vue?6119"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "houseList", "loading", "refreshing", "noMore", "page", "pageSize", "selectedType", "value", "label", "selectedPrice", "selected<PERSON>ort", "showTypeOptions", "showPriceOptions", "showSortOptions", "customPrice", "min", "max", "typeOptions", "HOUSE_TYPES", "sortOptions", "computed", "canPublish", "methods", "getTypeText", "loadHouseList", "refresh", "params", "action", "sort", "request", "result", "console", "loadMore", "onRefresh", "toggleTypeFilter", "togglePriceFilter", "toggleSortFilter", "selectType", "onPriceInput", "updatePriceLabel", "resetPrice", "confirmPrice", "uni", "title", "icon", "selectSort", "closeAllFilters", "toSearch", "url", "debugAllHouses", "toHouseDetail", "toPublish", "applyTypeFilter", "duration", "applyPriceFilter", "applySortFilter", "toggleFavorite", "onLoad", "onShow", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACqC;;;AAGxF;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;AAA+nB,CAAgB,6nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACuKnpB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;QAAAC;QAAAC;MAAA;MACAC;QAAAF;QAAAC;MAAA;MACAE;QAAAH;QAAAC;MAAA;MAEA;MACAG;MACAC;MACAC;MAEA;MACAC;QACAC;QACAC;MACA;MAEA;MACAC,cACA;QAAAV;QAAAC;MAAA,2CACAU,qBACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QAAA;MAAA;MACA;IACA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAEA;kBACA;kBACA;gBACA;gBAAA;gBAGAC;kBACAC;kBACA5B;oBACAK;oBACAC;oBACAuB;kBACA;gBACA,GAEA;gBACA;kBACAF;gBACA;gBAEA;kBAAA,wBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA,OAEAG;cAAA;gBAAAC;gBAEA;kBAAA,eACAA;kBAEA;oBACA;kBACA;oBACA;kBACA;kBAEA;kBACA;gBACA;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QAAAxB;QAAAC;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAwB;MACA;MACA;MACA;QAAAjC;QAAAC;MAAA;MACA;MACA;IACA;IAEA;IACAiC;MACA;QAAA1B;QAAAC;;MAEA;MACA;QACA0B;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;QACA;MACA;MAEA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACAL;QACAM;MACA;IACA;IAEA;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEApB;kBACAF;gBACA;cAAA;gBAFAG;gBAGAC;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAmB;MACAR;QACAM;MACA;IACA;IAEA;IACAG;MACA;QACAT;UACAC;UACAC;QACA;QACAF;UACAM;QACA;QACA;MACA;MAEAN;QACAM;MACA;IACA;IAEA;IACAI;MACA;QAAA;MAAA;MACA;QACA;QACArB;;QAEA;QACAW;UACAC;UACAC;UACAS;QACA;MACA;QACAtB;MACA;IACA;IAEA;IACAuB;MACA;MACAvB;IACA;IAEA;IACAwB;MACA;QAAA;MAAA;MACA;QACA;QACAxB;MACA;IACA;IAEA;IACAyB;MACA;MACAd;QACAC;QACAC;MACA;IACA;EACA;EAEAa;IACA;IACA;MACA;IACA;IAEA;IACA;EACA;EAEAC;IACA;IACA;IACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;;MAEA;MACAhB;MACA;MACA;MACA;IACA;;IAEA;IACA;MACA;IACA;EACA;EAEAiB;IACA;IACAjB;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/fA;AAAA;AAAA;AAAA;AAAs7B,CAAgB,g5BAAG,EAAC,C;;;;;;;;;;;ACA18B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/house/list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/house/list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list.vue?vue&type=template&id=145608e6&scoped=true&\"\nvar renderjs\nimport script from \"./list.vue?vue&type=script&lang=js&\"\nexport * from \"./list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list.vue?vue&type=style&index=0&id=145608e6&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"145608e6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/house/list.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=template&id=145608e6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.houseList, function (house, __i2__) {\n    var $orig = _vm.__get_orig(house)\n    var m0 = _vm.getTypeText(house.type)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var g0 = _vm.houseList.length\n  var g1 = !_vm.loading && _vm.houseList.length === 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"house-list-container\">\n    <!-- 筛选栏 -->\n    <view class=\"filter-bar\">\n      <view class=\"filter-item\" @click=\"toggleTypeFilter\" :class=\"{ active: showTypeOptions }\">\n        <text class=\"filter-text\">{{ selectedType.label }}</text>\n        <uni-icons :type=\"showTypeOptions ? 'arrowup' : 'arrowdown'\" size=\"12\" color=\"#666\"></uni-icons>\n      </view>\n      <view class=\"filter-item\" @click=\"togglePriceFilter\" :class=\"{ active: showPriceOptions }\">\n        <text class=\"filter-text\">{{ selectedPrice.label }}</text>\n        <uni-icons :type=\"showPriceOptions ? 'arrowup' : 'arrowdown'\" size=\"12\" color=\"#666\"></uni-icons>\n      </view>\n      <view class=\"filter-item\" @click=\"toggleSortFilter\" :class=\"{ active: showSortOptions }\">\n        <text class=\"filter-text\">{{ selectedSort.label }}</text>\n        <uni-icons :type=\"showSortOptions ? 'arrowup' : 'arrowdown'\" size=\"12\" color=\"#666\"></uni-icons>\n      </view>\n      <view class=\"filter-item\" @click=\"toSearch\">\n        <uni-icons type=\"search\" size=\"16\" color=\"#666\"></uni-icons>\n      </view>\n    </view>\n\n    <!-- 筛选选项展开区域 -->\n    <view class=\"filter-options-container\" v-if=\"showTypeOptions || showPriceOptions || showSortOptions\">\n      <!-- 房源类型选项 -->\n      <view class=\"filter-options\" v-if=\"showTypeOptions\">\n        <view class=\"options-title\">房源类型</view>\n        <view class=\"options-grid\">\n          <view\n            class=\"option-item\"\n            v-for=\"option in typeOptions\"\n            :key=\"option.value\"\n            :class=\"{ selected: selectedType.value === option.value }\"\n            @click=\"selectType(option)\"\n          >\n            <text class=\"option-text\">{{ option.label }}</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 价格区间选项 -->\n      <view class=\"filter-options\" v-if=\"showPriceOptions\">\n        <view class=\"options-title\">价格区间</view>\n        <view class=\"price-input-container\">\n          <view class=\"price-input-group\">\n            <text class=\"input-label\">最低价</text>\n            <input\n              class=\"price-input\"\n              type=\"number\"\n              placeholder=\"不限\"\n              v-model=\"customPrice.min\"\n              @input=\"onPriceInput\"\n            />\n            <text class=\"price-unit\">元/月</text>\n          </view>\n          <view class=\"price-separator\">-</view>\n          <view class=\"price-input-group\">\n            <text class=\"input-label\">最高价</text>\n            <input\n              class=\"price-input\"\n              type=\"number\"\n              placeholder=\"不限\"\n              v-model=\"customPrice.max\"\n              @input=\"onPriceInput\"\n            />\n            <text class=\"price-unit\">元/月</text>\n          </view>\n        </view>\n        <view class=\"price-actions\">\n          <button class=\"price-btn reset-btn\" @click=\"resetPrice\">重置</button>\n          <button class=\"price-btn confirm-btn\" @click=\"confirmPrice\">确定</button>\n        </view>\n      </view>\n\n      <!-- 排序方式选项 -->\n      <view class=\"filter-options\" v-if=\"showSortOptions\">\n        <view class=\"options-title\">排序方式</view>\n        <view class=\"options-list\">\n          <view\n            class=\"option-item-list\"\n            v-for=\"option in sortOptions\"\n            :key=\"option.value\"\n            :class=\"{ selected: selectedSort.value === option.value }\"\n            @click=\"selectSort(option)\"\n          >\n            <text class=\"option-text\">{{ option.label }}</text>\n            <uni-icons v-if=\"selectedSort.value === option.value\" type=\"checkmarkempty\" size=\"16\" color=\"#007aff\"></uni-icons>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 房源列表 -->\n    <scroll-view\n      class=\"house-list\"\n      scroll-y\n      @scrolltolower=\"loadMore\"\n      refresher-enabled\n      @refresherrefresh=\"onRefresh\"\n      :refresher-triggered=\"refreshing\"\n      @click=\"closeAllFilters\"\n    >\n      <view class=\"house-item\" v-for=\"house in houseList\" :key=\"house._id\" @click=\"toHouseDetail(house._id)\">\n        <view class=\"house-image\">\n          <image\n            :src=\"house.images && house.images[0] || '/static/default-house.png'\"\n            mode=\"aspectFill\"\n          ></image>\n          <view class=\"house-type\">{{ getTypeText(house.type) }}</view>\n          <view class=\"favorite-btn\" @click.stop=\"toggleFavorite(house)\">\n            <uni-icons type=\"heart\" size=\"20\" color=\"#fff\"></uni-icons>\n          </view>\n        </view>\n        <view class=\"house-info\">\n          <text class=\"house-title\">{{ house.title }}</text>\n          <text class=\"house-desc\">{{ house.description }}</text>\n          <view class=\"house-tags\">\n            <text class=\"tag\" v-if=\"house.room_count\">{{ house.room_count }}室</text>\n            <text class=\"tag\" v-if=\"house.hall_count\">{{ house.hall_count }}厅</text>\n            <text class=\"tag\" v-if=\"house.area\">{{ house.area }}㎡</text>\n            <text class=\"tag\" v-if=\"house.orientation\">{{ house.orientation }}</text>\n          </view>\n          <view class=\"house-location\">\n            <uni-icons type=\"location\" size=\"12\" color=\"#999\"></uni-icons>\n            <text class=\"location-text\">{{ house.location.district }} {{ house.location.address }}</text>\n          </view>\n          <view class=\"house-bottom\">\n            <view class=\"price-info\">\n              <text class=\"price\">¥{{ house.price }}</text>\n              <text class=\"price-unit\">/月</text>\n            </view>\n            <view class=\"house-stats\">\n              <text class=\"stat-item\">\n                <uni-icons type=\"eye\" size=\"12\" color=\"#999\"></uni-icons>\n                {{ house.view_count || 0 }}\n              </text>\n              <text class=\"stat-item\">\n                <uni-icons type=\"heart\" size=\"12\" color=\"#999\"></uni-icons>\n                {{ house.favorite_count || 0 }}\n              </text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 加载状态 -->\n      <view class=\"load-status\" v-if=\"houseList.length > 0\">\n        <text v-if=\"loading\">加载中...</text>\n        <text v-else-if=\"noMore\">没有更多了</text>\n      </view>\n      \n      <!-- 空状态 -->\n      <view class=\"empty-state\" v-if=\"!loading && houseList.length === 0\">\n        <image src=\"/static/empty-house.png\" mode=\"aspectFit\"></image>\n        <text class=\"empty-text\">暂无房源信息</text>\n      </view>\n    </scroll-view>\n    \n    <!-- 发布按钮 -->\n    <view class=\"publish-btn\" @click=\"toPublish\" v-if=\"canPublish\">\n      <uni-icons type=\"plus\" size=\"24\" color=\"#fff\"></uni-icons>\n    </view>\n    \n\n  </view>\n</template>\n\n<script>\nimport request from '@/utils/request.js'\nimport { checkLogin } from '@/utils/common.js'\nimport { HOUSE_TYPES, SORT_OPTIONS } from '@/common/config.js'\n\nexport default {\n  data() {\n    return {\n      houseList: [],\n      loading: false,\n      refreshing: false,\n      noMore: false,\n      page: 1,\n      pageSize: 10,\n      \n      // 筛选条件\n      selectedType: { value: '', label: '房源类型' },\n      selectedPrice: { value: '', label: '价格区间' },\n      selectedSort: { value: 'publish_date_desc', label: '最新发布' },\n\n      // 筛选选项展开状态\n      showTypeOptions: false,\n      showPriceOptions: false,\n      showSortOptions: false,\n\n      // 自定义价格区间\n      customPrice: {\n        min: '',\n        max: ''\n      },\n\n      // 筛选选项\n      typeOptions: [\n        { value: '', label: '全部类型' },\n        ...HOUSE_TYPES\n      ],\n      sortOptions: SORT_OPTIONS\n    }\n  },\n  computed: {\n    canPublish() {\n      return checkLogin()\n    }\n  },\n  methods: {\n    // 获取房源类型文本\n    getTypeText(type) {\n      const typeItem = HOUSE_TYPES.find(item => item.value === type)\n      return typeItem ? typeItem.label : type\n    },\n    \n    // 加载房源列表\n    async loadHouseList(refresh = false) {\n      if (this.loading) return\n      \n      this.loading = true\n      \n      if (refresh) {\n        this.page = 1\n        this.noMore = false\n      }\n      \n      try {\n        const params = {\n          action: 'getHouseList',\n          data: {\n            page: this.page,\n            pageSize: this.pageSize,\n            sort: this.selectedSort.value\n          }\n        }\n        \n        // 添加筛选条件\n        if (this.selectedType.value) {\n          params.data.type = this.selectedType.value\n        }\n\n        if (this.selectedPrice.value) {\n          const [minPrice, maxPrice] = this.selectedPrice.value.split('-').map(Number)\n          if (minPrice >= 0) params.data.minPrice = minPrice\n          if (maxPrice > 0 && maxPrice < 999999) params.data.maxPrice = maxPrice\n        }\n\n        const result = await request.callFunction('house-management', params)\n\n        if (result.code === 0) {\n          const { list, total } = result.data\n\n          if (refresh) {\n            this.houseList = list\n          } else {\n            this.houseList.push(...list)\n          }\n\n          this.page++\n          this.noMore = this.houseList.length >= total\n        } else {\n          console.error('房源列表请求失败:', result)\n        }\n      } catch (error) {\n        console.error('加载房源列表失败:', error)\n      } finally {\n        this.loading = false\n        this.refreshing = false\n      }\n    },\n    \n    // 加载更多\n    loadMore() {\n      if (!this.noMore && !this.loading) {\n        this.loadHouseList()\n      }\n    },\n    \n    // 下拉刷新\n    onRefresh() {\n      this.refreshing = true\n      this.loadHouseList(true)\n    },\n    \n    // 切换类型筛选\n    toggleTypeFilter() {\n      this.showTypeOptions = !this.showTypeOptions\n      this.showPriceOptions = false\n      this.showSortOptions = false\n    },\n\n    // 切换价格筛选\n    togglePriceFilter() {\n      this.showPriceOptions = !this.showPriceOptions\n      this.showTypeOptions = false\n      this.showSortOptions = false\n    },\n\n    // 切换排序筛选\n    toggleSortFilter() {\n      this.showSortOptions = !this.showSortOptions\n      this.showTypeOptions = false\n      this.showPriceOptions = false\n    },\n\n    // 选择房源类型\n    selectType(option) {\n      this.selectedType = option\n      this.showTypeOptions = false\n      this.loadHouseList(true)\n    },\n\n    // 价格输入处理\n    onPriceInput() {\n      // 实时更新价格显示标签\n      this.updatePriceLabel()\n    },\n\n    // 更新价格标签\n    updatePriceLabel() {\n      const { min, max } = this.customPrice\n      if (!min && !max) {\n        this.selectedPrice.label = '价格区间'\n      } else if (min && max) {\n        this.selectedPrice.label = `${min}-${max}元`\n      } else if (min) {\n        this.selectedPrice.label = `${min}元起`\n      } else if (max) {\n        this.selectedPrice.label = `${max}元内`\n      }\n    },\n\n    // 重置价格\n    resetPrice() {\n      this.customPrice.min = ''\n      this.customPrice.max = ''\n      this.selectedPrice = { value: '', label: '价格区间' }\n      this.showPriceOptions = false\n      this.loadHouseList(true)\n    },\n\n    // 确认价格筛选\n    confirmPrice() {\n      const { min, max } = this.customPrice\n\n      // 验证输入\n      if (min && max && parseInt(min) > parseInt(max)) {\n        uni.showToast({\n          title: '最低价不能大于最高价',\n          icon: 'none'\n        })\n        return\n      }\n\n      // 设置价格筛选值\n      if (min || max) {\n        this.selectedPrice.value = `${min || 0}-${max || 999999}`\n      } else {\n        this.selectedPrice.value = ''\n      }\n\n      this.updatePriceLabel()\n      this.showPriceOptions = false\n      this.loadHouseList(true)\n    },\n\n    // 选择排序方式\n    selectSort(option) {\n      this.selectedSort = option\n      this.showSortOptions = false\n      this.loadHouseList(true)\n    },\n\n    // 关闭所有筛选选项\n    closeAllFilters() {\n      this.showTypeOptions = false\n      this.showPriceOptions = false\n      this.showSortOptions = false\n    },\n\n    // 跳转到搜索页\n    toSearch() {\n      uni.navigateTo({\n        url: '/pages/search/search'\n      })\n    },\n\n    // 临时调试：查看所有房源\n    async debugAllHouses() {\n      try {\n        const result = await request.callFunction('house-management', {\n          action: 'debugGetAllHouses'\n        })\n        console.log('所有房源数据:', result)\n      } catch (error) {\n        console.error('调试查询失败:', error)\n      }\n    },\n    \n    // 跳转到房源详情\n    toHouseDetail(houseId) {\n      uni.navigateTo({\n        url: `/pages/house/detail?id=${houseId}`\n      })\n    },\n    \n    // 跳转到发布页\n    toPublish() {\n      if (!checkLogin()) {\n        uni.showToast({\n          title: '请先登录',\n          icon: 'none'\n        })\n        uni.navigateTo({\n          url: '/pages/login/login'\n        })\n        return\n      }\n\n      uni.navigateTo({\n        url: '/pages/house/publish'\n      })\n    },\n\n    // 应用房源类型筛选\n    applyTypeFilter(typeValue) {\n      const typeOption = this.typeOptions.find(option => option.value === typeValue)\n      if (typeOption) {\n        this.selectedType = typeOption\n        console.log('房源列表应用类型筛选:', typeOption)\n\n        // 显示筛选成功提示\n        uni.showToast({\n          title: `已筛选${typeOption.label}房源`,\n          icon: 'success',\n          duration: 2000\n        })\n      } else {\n        console.warn('未找到房源类型选项:', typeValue)\n      }\n    },\n\n    // 应用价格筛选\n    applyPriceFilter(priceValue) {\n      // 这里可以根据需要实现价格筛选逻辑\n      console.log('应用价格筛选:', priceValue)\n    },\n\n    // 应用排序筛选\n    applySortFilter(sortValue) {\n      const sortOption = this.sortOptions.find(option => option.value === sortValue)\n      if (sortOption) {\n        this.selectedSort = sortOption\n        console.log('应用排序筛选:', sortOption)\n      }\n    },\n\n    // 切换收藏状态\n    toggleFavorite(house) {\n      // 这里可以实现收藏功能\n      uni.showToast({\n        title: '收藏功能开发中',\n        icon: 'none'\n      })\n    }\n  },\n  \n  onLoad(options) {\n    // 处理 URL 参数\n    if (options.type) {\n      this.applyTypeFilter(options.type)\n    }\n\n    this.debugAllHouses() // 调试：查看所有房源\n    this.loadHouseList(true)\n  },\n\n  onShow() {\n    // 检查是否有从首页传递的筛选参数\n    const houseListFilter = uni.getStorageSync('houseListFilter')\n    if (houseListFilter) {\n      // 应用筛选参数\n      if (houseListFilter.type) {\n        this.applyTypeFilter(houseListFilter.type)\n      }\n      if (houseListFilter.price) {\n        this.applyPriceFilter(houseListFilter.price)\n      }\n      if (houseListFilter.sort) {\n        this.applySortFilter(houseListFilter.sort)\n      }\n\n      // 清除存储的参数，避免重复应用\n      uni.removeStorageSync('houseListFilter')\n      // 重新加载列表\n      this.loadHouseList(true)\n      return\n    }\n\n    // 从发布页返回时刷新列表\n    if (this.houseList.length > 0) {\n      this.loadHouseList(true)\n    }\n  },\n  \n  onPullDownRefresh() {\n    this.onRefresh()\n    uni.stopPullDownRefresh()\n  }\n}\n</script>\n\n<style scoped>\n.house-list-container {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: #f8f9fa;\n  overflow-x: hidden;\n}\n\n.filter-bar {\n  background: #fff;\n  padding: 20rpx 16rpx;\n  display: flex;\n  border-bottom: 1rpx solid #e9ecef;\n  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);\n  position: sticky;\n  top: 0;\n  z-index: 100;\n}\n\n.filter-item {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 16rpx 12rpx;\n  border-radius: 12rpx;\n  transition: all 0.3s ease;\n  position: relative;\n}\n\n.filter-item.active {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: #fff;\n  transform: translateY(-2rpx);\n  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);\n}\n\n.filter-item.active .filter-text {\n  color: #fff;\n}\n\n.filter-text {\n  font-size: 28rpx;\n  color: #333;\n  margin-right: 8rpx;\n  font-weight: 500;\n}\n\n.house-list {\n  flex: 1;\n  padding: 20rpx 20rpx;\n  box-sizing: border-box;\n}\n\n.house-item {\n  background: #fff;\n  border-radius: 20rpx;\n  margin-bottom: 20rpx;\n  overflow: hidden;\n  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.06);\n  transition: all 0.3s ease;\n  position: relative;\n  width: 100%;\n  box-sizing: border-box;\n}\n\n.house-item:active {\n  transform: translateY(-2rpx);\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n}\n\n.house-image {\n  position: relative;\n  height: 320rpx;\n  overflow: hidden;\n}\n\n.house-image image {\n  width: 100%;\n  height: 100%;\n  transition: transform 0.3s ease;\n}\n\n.house-item:active .house-image image {\n  transform: scale(1.05);\n}\n\n.house-type {\n  position: absolute;\n  top: 16rpx;\n  left: 16rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: #fff;\n  padding: 12rpx 20rpx;\n  border-radius: 20rpx;\n  font-size: 24rpx;\n  font-weight: 500;\n  backdrop-filter: blur(10rpx);\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);\n}\n\n.favorite-btn {\n  position: absolute;\n  top: 16rpx;\n  right: 16rpx;\n  width: 48rpx;\n  height: 48rpx;\n  background: rgba(0, 0, 0, 0.4);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  backdrop-filter: blur(10rpx);\n  transition: all 0.3s ease;\n}\n\n.favorite-btn:active {\n  transform: scale(0.9);\n  background: rgba(255, 107, 107, 0.8);\n}\n\n.house-info {\n  padding: 20rpx;\n  overflow: hidden;\n  box-sizing: border-box;\n}\n\n.house-title {\n  display: block;\n  font-size: 34rpx;\n  font-weight: 600;\n  color: #1a1a1a;\n  margin-bottom: 12rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  line-height: 1.4;\n}\n\n.house-desc {\n  display: block;\n  font-size: 26rpx;\n  color: #666;\n  margin-bottom: 20rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  line-height: 1.5;\n}\n\n.house-tags {\n  display: flex;\n  gap: 8rpx;\n  margin-bottom: 16rpx;\n  flex-wrap: wrap;\n}\n\n.tag {\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  color: #495057;\n  padding: 8rpx 12rpx;\n  border-radius: 14rpx;\n  font-size: 20rpx;\n  font-weight: 500;\n  border: 1rpx solid #e9ecef;\n  transition: all 0.3s ease;\n  flex-shrink: 0;\n}\n\n.tag:first-child {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: #fff;\n  border-color: #667eea;\n}\n\n.house-location {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20rpx;\n  padding: 10rpx 12rpx;\n  background: #f8f9fa;\n  border-radius: 10rpx;\n  border-left: 3rpx solid #667eea;\n}\n\n.location-text {\n  font-size: 24rpx;\n  color: #666;\n  margin-left: 8rpx;\n  flex: 1;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.house-bottom {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding-top: 16rpx;\n  border-top: 1rpx solid #f0f0f0;\n}\n\n.price-info {\n  display: flex;\n  align-items: baseline;\n}\n\n.price {\n  font-size: 40rpx;\n  font-weight: 700;\n  color: #667eea;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n}\n\n.price-unit {\n  font-size: 24rpx;\n  color: #999;\n  margin-left: 8rpx;\n  font-weight: 500;\n}\n\n.house-stats {\n  display: flex;\n  gap: 16rpx;\n  flex-wrap: wrap;\n}\n\n.stat-item {\n  font-size: 20rpx;\n  color: #999;\n  display: flex;\n  align-items: center;\n  gap: 4rpx;\n  padding: 4rpx 8rpx;\n  background: #f8f9fa;\n  border-radius: 8rpx;\n  transition: all 0.3s ease;\n  flex-shrink: 0;\n  white-space: nowrap;\n}\n\n.stat-item:active {\n  background: #e9ecef;\n}\n\n.load-status {\n  text-align: center;\n  padding: 40rpx;\n  color: #999;\n  font-size: 28rpx;\n  background: #f8f9fa;\n  margin: 20rpx;\n  border-radius: 16rpx;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 120rpx 40rpx;\n  background: #fff;\n  margin: 20rpx;\n  border-radius: 24rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.empty-state image {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 40rpx;\n  opacity: 0.6;\n}\n\n.empty-text {\n  display: block;\n  font-size: 28rpx;\n  color: #999;\n  margin-bottom: 12rpx;\n}\n\n.publish-btn {\n  position: fixed;\n  right: 40rpx;\n  bottom: 120rpx;\n  width: 120rpx;\n  height: 120rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.4);\n  transition: all 0.3s ease;\n  z-index: 100;\n}\n\n.publish-btn:active {\n  transform: scale(0.95) translateY(-2rpx);\n  box-shadow: 0 16rpx 50rpx rgba(102, 126, 234, 0.5);\n}\n\n/* 筛选选项展开容器 */\n.filter-options-container {\n  background: #fff;\n  border-bottom: 1rpx solid #e9ecef;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n  position: relative;\n  z-index: 99;\n}\n\n.filter-options {\n  padding: 32rpx 24rpx;\n}\n\n.options-title {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #1a1a1a;\n  margin-bottom: 24rpx;\n  position: relative;\n  padding-left: 16rpx;\n}\n\n.options-title::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 4rpx;\n  height: 20rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 2rpx;\n}\n\n/* 网格布局（用于类型和价格） */\n.options-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16rpx;\n}\n\n.option-item {\n  flex: 1;\n  min-width: 0;\n  padding: 20rpx 24rpx;\n  background: #f8f9fa;\n  border: 2rpx solid #e9ecef;\n  border-radius: 16rpx;\n  text-align: center;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.option-item::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.5s ease;\n}\n\n.option-item:active::before {\n  left: 100%;\n}\n\n.option-item.selected {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-color: #667eea;\n  color: #fff;\n  transform: translateY(-2rpx);\n  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);\n}\n\n.option-item .option-text {\n  font-size: 26rpx;\n  color: #333;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.option-item.selected .option-text {\n  color: #fff;\n}\n\n/* 列表布局（用于排序） */\n.options-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.option-item-list {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 25rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.option-item-list:last-child {\n  border-bottom: none;\n}\n\n.option-item-list.selected {\n  background: #f0f8ff;\n  margin: 0 -40rpx;\n  padding: 25rpx 40rpx;\n  border-radius: 8rpx;\n}\n\n.option-item-list .option-text {\n  font-size: 28rpx;\n  color: #333;\n}\n\n.option-item-list.selected .option-text {\n  color: #007aff;\n  font-weight: 500;\n}\n\n/* 筛选栏激活状态 */\n.filter-item.active {\n  background: #f0f8ff;\n}\n\n.filter-item.active .filter-text {\n  color: #007aff;\n}\n\n/* 价格输入样式 */\n.price-input-container {\n  display: flex;\n  align-items: center;\n  gap: 20rpx;\n  margin-bottom: 30rpx;\n}\n\n.price-input-group {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 10rpx;\n}\n\n.input-label {\n  font-size: 24rpx;\n  color: #666;\n  margin-bottom: 8rpx;\n}\n\n.price-input {\n  height: 80rpx;\n  padding: 0 20rpx;\n  border: 2rpx solid #e0e0e0;\n  border-radius: 8rpx;\n  font-size: 28rpx;\n  background: #fff;\n  text-align: center;\n}\n\n.price-input:focus {\n  border-color: #007aff;\n}\n\n.price-unit {\n  font-size: 24rpx;\n  color: #666;\n  text-align: center;\n  margin-top: 5rpx;\n}\n\n.price-separator {\n  font-size: 32rpx;\n  color: #666;\n  margin-top: 30rpx;\n  font-weight: bold;\n}\n\n.price-actions {\n  display: flex;\n  gap: 20rpx;\n  margin-top: 20rpx;\n}\n\n.price-btn {\n  flex: 1;\n  height: 80rpx;\n  border-radius: 8rpx;\n  font-size: 28rpx;\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.reset-btn {\n  background: #f5f5f5;\n  color: #666;\n}\n\n.confirm-btn {\n  background: #007aff;\n  color: #fff;\n}\n\n.price-btn:active {\n  opacity: 0.8;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=0&id=145608e6&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=0&id=145608e6&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751999925\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}