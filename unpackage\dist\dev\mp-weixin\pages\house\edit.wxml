<view class="edit-container data-v-239e3052"><block wx:if="{{houseInfo._id}}"><form data-event-opts="{{[['submit',[['handleSubmit',['$event']]]]]}}" bindsubmit="__e" class="data-v-239e3052"><view class="form-section data-v-239e3052"><view class="section-title data-v-239e3052">基本信息</view><view class="form-item data-v-239e3052"><text class="label data-v-239e3052">房源标题 *</text><input class="input data-v-239e3052" type="text" placeholder="请输入房源标题" maxlength="50" data-event-opts="{{[['input',[['__set_model',['$0','title','$event',[]],['form']]]]]}}" value="{{form.title}}" bindinput="__e"/></view><view class="form-item data-v-239e3052"><text class="label data-v-239e3052">房源描述</text><textarea class="textarea data-v-239e3052" placeholder="请详细描述房源情况，如周边环境、交通等" maxlength="500" data-event-opts="{{[['input',[['__set_model',['$0','description','$event',[]],['form']]]]]}}" value="{{form.description}}" bindinput="__e"></textarea></view><view class="form-item data-v-239e3052"><text class="label data-v-239e3052">房源类型 *</text><picker mode="selector" range="{{houseTypes}}" range-key="label" value="{{typeIndex}}" data-event-opts="{{[['change',[['onTypeChange',['$event']]]]]}}" bindchange="__e" class="data-v-239e3052"><view class="picker data-v-239e3052">{{''+(form.type?$root.m0:'请选择房源类型')+''}}<uni-icons vue-id="50285af4-1" type="arrowright" size="16" color="#ccc" class="data-v-239e3052" bind:__l="__l"></uni-icons></view></picker></view></view><view class="form-section data-v-239e3052"><view class="section-title data-v-239e3052">房屋信息</view><view class="form-row data-v-239e3052"><view class="form-item half data-v-239e3052"><text class="label data-v-239e3052">租金(元/月) *</text><input class="input data-v-239e3052" type="number" placeholder="租金" data-event-opts="{{[['input',[['__set_model',['$0','price','$event',[]],['form']]]]]}}" value="{{form.price}}" bindinput="__e"/></view><view class="form-item half data-v-239e3052"><text class="label data-v-239e3052">押金(元)</text><input class="input data-v-239e3052" type="number" placeholder="押金" data-event-opts="{{[['input',[['__set_model',['$0','deposit','$event',[]],['form']]]]]}}" value="{{form.deposit}}" bindinput="__e"/></view></view><view class="form-row data-v-239e3052"><view class="form-item half data-v-239e3052"><text class="label data-v-239e3052">面积(㎡)</text><input class="input data-v-239e3052" type="number" placeholder="面积" data-event-opts="{{[['input',[['__set_model',['$0','area','$event',[]],['form']]]]]}}" value="{{form.area}}" bindinput="__e"/></view><view class="form-item half data-v-239e3052"><text class="label data-v-239e3052">朝向</text><picker mode="selector" range="{{orientations}}" value="{{orientationIndex}}" data-event-opts="{{[['change',[['onOrientationChange',['$event']]]]]}}" bindchange="__e" class="data-v-239e3052"><view class="picker data-v-239e3052">{{''+(form.orientation||'请选择')+''}}<uni-icons vue-id="50285af4-2" type="arrowright" size="16" color="#ccc" class="data-v-239e3052" bind:__l="__l"></uni-icons></view></picker></view></view><view class="form-row data-v-239e3052"><view class="form-item third data-v-239e3052"><text class="label data-v-239e3052">房间数</text><input class="input data-v-239e3052" type="number" placeholder="房间" data-event-opts="{{[['input',[['__set_model',['$0','room_count','$event',[]],['form']]]]]}}" value="{{form.room_count}}" bindinput="__e"/></view><view class="form-item third data-v-239e3052"><text class="label data-v-239e3052">客厅数</text><input class="input data-v-239e3052" type="number" placeholder="客厅" data-event-opts="{{[['input',[['__set_model',['$0','hall_count','$event',[]],['form']]]]]}}" value="{{form.hall_count}}" bindinput="__e"/></view><view class="form-item third data-v-239e3052"><text class="label data-v-239e3052">卫生间</text><input class="input data-v-239e3052" type="number" placeholder="卫生间" data-event-opts="{{[['input',[['__set_model',['$0','bathroom_count','$event',[]],['form']]]]]}}" value="{{form.bathroom_count}}" bindinput="__e"/></view></view><view class="form-item data-v-239e3052"><text class="label data-v-239e3052">装修情况</text><picker mode="selector" range="{{decorationTypes}}" value="{{decorationIndex}}" data-event-opts="{{[['change',[['onDecorationChange',['$event']]]]]}}" bindchange="__e" class="data-v-239e3052"><view class="picker data-v-239e3052">{{''+(form.decoration||'请选择装修情况')+''}}<uni-icons vue-id="50285af4-3" type="arrowright" size="16" color="#ccc" class="data-v-239e3052" bind:__l="__l"></uni-icons></view></picker></view></view><view class="form-section data-v-239e3052"><view class="section-title data-v-239e3052">房屋设施</view><view class="facilities-grid data-v-239e3052"><block wx:for="{{$root.l0}}" wx:for-item="facility" wx:for-index="__i0__" wx:key="value"><view data-event-opts="{{[['tap',[['toggleFacility',['$0'],[[['facilitiesOptions','value',facility.$orig.value,'value']]]]]]]}}" class="{{['facility-item','data-v-239e3052',(facility.g0)?'active':'']}}" bindtap="__e"><text class="facility-text data-v-239e3052">{{facility.$orig.label}}</text></view></block></view></view><view class="form-section data-v-239e3052"><view class="section-title data-v-239e3052">联系方式</view><view class="form-item data-v-239e3052"><text class="label data-v-239e3052">联系人 *</text><input class="input data-v-239e3052" type="text" placeholder="请输入联系人姓名" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['form.contact']]]]]}}" value="{{form.contact.name}}" bindinput="__e"/></view><view class="form-item data-v-239e3052"><text class="label data-v-239e3052">联系电话 *</text><input class="input data-v-239e3052" type="number" placeholder="请输入联系电话" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['form.contact']]]]]}}" value="{{form.contact.phone}}" bindinput="__e"/></view><view class="form-item data-v-239e3052"><text class="label data-v-239e3052">微信号</text><input class="input data-v-239e3052" type="text" placeholder="请输入微信号" data-event-opts="{{[['input',[['__set_model',['$0','wechat','$event',[]],['form.contact']]]]]}}" value="{{form.contact.wechat}}" bindinput="__e"/></view></view><view class="submit-section data-v-239e3052"><button class="submit-btn data-v-239e3052" disabled="{{!canSubmit}}" data-event-opts="{{[['tap',[['handleSubmit',['$event']]]]]}}" bindtap="__e">{{''+(loading?'保存中...':'保存修改')+''}}</button></view></form></block><block wx:else><block wx:if="{{loading}}"><view class="loading-container data-v-239e3052"><text class="loading-text data-v-239e3052">加载中...</text></view></block><block wx:else><view class="error-container data-v-239e3052"><text class="error-text data-v-239e3052">房源不存在或无权限编辑</text><button data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="back-btn data-v-239e3052" bindtap="__e">返回</button></view></block></block></view>