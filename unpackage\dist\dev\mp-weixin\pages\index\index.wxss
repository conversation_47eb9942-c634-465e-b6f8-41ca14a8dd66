
.index-container.data-v-57280228 {
  background: #f8fafc;
  min-height: 100vh;
}

/* 头部区域 */
.header.data-v-57280228 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30rpx;
  padding-bottom: 100rpx;
  position: relative;
  overflow: hidden;
  border-radius: 0 0 40rpx 40rpx;
}
.header.data-v-57280228::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
  pointer-events: none;
}
.header-content.data-v-57280228 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40rpx;
  position: relative;
  z-index: 1;
}
.greeting-section.data-v-57280228 {
  flex: 1;
  padding-right: 20rpx;
}
.greeting-text.data-v-57280228 {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #fff;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  line-height: 1.2;
}
.greeting-sub.data-v-57280228 {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.85);
  font-weight: 400;
  line-height: 1.3;
}
.location-btn.data-v-57280228 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 25rpx;
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}
.location-btn.data-v-57280228:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  background: rgba(255, 255, 255, 0.25);
}
.location-text.data-v-57280228 {
  font-size: 26rpx;
  color: #fff;
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}
.search-section.data-v-57280228 {
  position: relative;
  z-index: 1;
}
.search-bar.data-v-57280228 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 28rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  min-height: 88rpx;
}
.search-bar.data-v-57280228:active {
  -webkit-transform: translateY(2rpx);
          transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}
.search-placeholder.data-v-57280228 {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  font-weight: 400;
}
.search-btn.data-v-57280228 {
  background: linear-gradient(135deg, #007aff 0%, #5856d6 100%);
  border-radius: 22rpx;
  padding: 16rpx 28rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
  transition: all 0.3s ease;
}
.search-btn.data-v-57280228:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.4);
}
.search-btn-text.data-v-57280228 {
  font-size: 28rpx;
  color: #fff;
  font-weight: 600;
}

/* 轮播图区域 */
.banner-section.data-v-57280228 {
  margin: 30rpx;
  margin-top: -80rpx;
  position: relative;
  z-index: 2;
}
.banner-swiper.data-v-57280228 {
  height: 360rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.12), 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}
.banner-item.data-v-57280228 {
  position: relative;
  height: 100%;
  overflow: hidden;
}
.banner-image.data-v-57280228 {
  width: 100%;
  height: 100%;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.banner-overlay.data-v-57280228 {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 40rpx 30rpx 30rpx;
  color: #fff;
}
.banner-title.data-v-57280228 {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}
.banner-desc.data-v-57280228 {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
  font-weight: 400;
}

/* 快捷入口区域 */
.quick-section.data-v-57280228 {
  margin: 50rpx 30rpx;
}
.section-header.data-v-57280228 {
  margin-bottom: 30rpx;
}
.section-title.data-v-57280228 {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 8rpx;
}
.section-subtitle.data-v-57280228 {
  display: block;
  font-size: 26rpx;
  color: #718096;
  font-weight: 400;
}
.quick-grid.data-v-57280228 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}
.quick-item.data-v-57280228 {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f7fafc;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.quick-item.data-v-57280228::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2);
  -webkit-transform: scaleX(0);
          transform: scaleX(0);
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.quick-item.data-v-57280228:active {
  -webkit-transform: translateY(4rpx);
          transform: translateY(4rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.quick-item.data-v-57280228:active::before {
  -webkit-transform: scaleX(1);
          transform: scaleX(1);
}
.quick-icon.data-v-57280228 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20rpx;
  position: relative;
}
.quick-icon.whole.data-v-57280228 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.quick-icon.shared.data-v-57280228 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.quick-icon.single.data-v-57280228 {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.quick-icon.map.data-v-57280228 {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}
.icon-emoji.data-v-57280228 {
  font-size: 36rpx;
}
.quick-title.data-v-57280228 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8rpx;
}
.quick-desc.data-v-57280228 {
  display: block;
  font-size: 24rpx;
  color: #718096;
  font-weight: 400;
}

/* 内容区域通用样式 */
.recommend-section.data-v-57280228, .latest-section.data-v-57280228 {
  margin: 50rpx 30rpx;
}
.content-header.data-v-57280228 {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 35rpx;
}
.header-left.data-v-57280228 {
  flex: 1;
}
.content-title.data-v-57280228 {
  display: block;
  font-size: 34rpx;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 10rpx;
  line-height: 1.2;
}
.content-subtitle.data-v-57280228 {
  display: block;
  font-size: 26rpx;
  color: #718096;
  font-weight: 400;
  line-height: 1.3;
}
.more-btn.data-v-57280228 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: #f7fafc;
  border-radius: 20rpx;
  border: 1rpx solid #e2e8f0;
  transition: all 0.3s ease;
}
.more-btn.data-v-57280228:active {
  background: #edf2f7;
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.more-text.data-v-57280228 {
  font-size: 26rpx;
  color: #007aff;
  font-weight: 500;
}

/* 加载状态 */
.loading-container.data-v-57280228 {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
}
.loading-content.data-v-57280228 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}
.loading-spinner.data-v-57280228 {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f4f6;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  -webkit-animation: spin-data-v-57280228 1s linear infinite;
          animation: spin-data-v-57280228 1s linear infinite;
}
.loading-text.data-v-57280228 {
  font-size: 26rpx;
  color: #9ca3af;
}
@-webkit-keyframes spin-data-v-57280228 {
0% { -webkit-transform: rotate(0deg); transform: rotate(0deg);
}
100% { -webkit-transform: rotate(360deg); transform: rotate(360deg);
}
}
@keyframes spin-data-v-57280228 {
0% { -webkit-transform: rotate(0deg); transform: rotate(0deg);
}
100% { -webkit-transform: rotate(360deg); transform: rotate(360deg);
}
}

/* 推荐房源样式 */
.recommend-scroll.data-v-57280228 {
  margin: 0 -30rpx;
  padding: 0 30rpx;
}
.recommend-list.data-v-57280228 {
  display: flex;
  gap: 20rpx;
  padding-bottom: 20rpx;
}
.recommend-card.data-v-57280228 {
  width: 280rpx;
  flex-shrink: 0;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f7fafc;
  transition: all 0.3s ease;
}
.recommend-card.data-v-57280228:active {
  -webkit-transform: translateY(4rpx);
          transform: translateY(4rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.card-image.data-v-57280228 {
  position: relative;
  height: 200rpx;
  overflow: hidden;
}
.house-image.data-v-57280228 {
  width: 100%;
  height: 100%;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.recommend-card:active .house-image.data-v-57280228 {
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
}
.image-overlay.data-v-57280228 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 20rpx;
}
.house-type-tag.data-v-57280228 {
  background: rgba(0, 122, 255, 0.9);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 600;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.favorite-btn.data-v-57280228 {
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.card-content.data-v-57280228 {
  padding: 24rpx;
}
.card-title.data-v-57280228 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.card-tags.data-v-57280228 {
  display: flex;
  gap: 8rpx;
  margin-bottom: 12rpx;
}
.tag.data-v-57280228 {
  background: #f7fafc;
  color: #4a5568;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  font-weight: 500;
}
.card-location.data-v-57280228 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
}
.location-text.data-v-57280228 {
  font-size: 24rpx;
  color: #718096;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.card-price.data-v-57280228 {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}
.price.data-v-57280228 {
  font-size: 32rpx;
  font-weight: 700;
  color: #e53e3e;
}
.price-unit.data-v-57280228 {
  font-size: 24rpx;
  color: #718096;
  font-weight: 400;
}

/* 最新房源样式 */
.latest-list.data-v-57280228 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.latest-card.data-v-57280228 {
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f7fafc;
  transition: all 0.3s ease;
  display: flex;
}
.latest-card.data-v-57280228:active {
  -webkit-transform: translateY(2rpx);
          transform: translateY(2rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.card-image-container.data-v-57280228 {
  position: relative;
  width: 240rpx;
  height: 180rpx;
  flex-shrink: 0;
}
.card-image.data-v-57280228 {
  width: 100%;
  height: 100%;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.latest-card:active .card-image.data-v-57280228 {
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
}
.image-badges.data-v-57280228 {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.type-badge.data-v-57280228 {
  background: rgba(0, 122, 255, 0.9);
  color: #fff;
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
  font-size: 20rpx;
  font-weight: 600;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.new-badge.data-v-57280228 {
  background: rgba(239, 68, 68, 0.9);
  color: #fff;
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
  font-size: 20rpx;
  font-weight: 600;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.card-info.data-v-57280228 {
  flex: 1;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
}
.info-header.data-v-57280228 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}
.info-title.data-v-57280228 {
  flex: 1;
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  line-height: 1.4;
  margin-right: 16rpx;
}
.favorite-icon.data-v-57280228 {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.info-desc.data-v-57280228 {
  font-size: 24rpx;
  color: #718096;
  line-height: 1.5;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.info-tags.data-v-57280228 {
  display: flex;
  gap: 8rpx;
  margin-bottom: 12rpx;
}
.info-tag.data-v-57280228 {
  background: #f7fafc;
  color: #4a5568;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  font-weight: 500;
}
.info-location.data-v-57280228 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
}
.location-detail.data-v-57280228 {
  font-size: 24rpx;
  color: #718096;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}
.info-bottom.data-v-57280228 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}
.price-section.data-v-57280228 {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}
.price-amount.data-v-57280228 {
  font-size: 32rpx;
  font-weight: 700;
  color: #e53e3e;
}
.price-period.data-v-57280228 {
  font-size: 24rpx;
  color: #718096;
  font-weight: 400;
}
.stats-section.data-v-57280228 {
  display: flex;
  gap: 16rpx;
}
.stat-item.data-v-57280228 {
  display: flex;
  align-items: center;
  gap: 6rpx;
}
.stat-text.data-v-57280228 {
  font-size: 22rpx;
  color: #9ca3af;
}

/* 空状态样式 */
.empty-state.data-v-57280228 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  text-align: center;
}
.empty-icon.data-v-57280228 {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}
.empty-title.data-v-57280228 {
  font-size: 28rpx;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 12rpx;
}
.empty-desc.data-v-57280228 {
  font-size: 24rpx;
  color: #9ca3af;
  line-height: 1.5;
}

/* 底部间距 */
.bottom-spacing.data-v-57280228 {
  height: 40rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
.quick-grid.data-v-57280228 {
    grid-template-columns: 1fr;
}
.latest-card.data-v-57280228 {
    flex-direction: column;
}
.card-image-container.data-v-57280228 {
    width: 100%;
    height: 200rpx;
}
}

