{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端/pages/house/publish.vue?61ca", "webpack:///D:/web/project/前端/pages/house/publish.vue?349c", "webpack:///D:/web/project/前端/pages/house/publish.vue?e29d", "webpack:///D:/web/project/前端/pages/house/publish.vue?80e7", "uni-app:///pages/house/publish.vue", "webpack:///D:/web/project/前端/pages/house/publish.vue?a5e8", "webpack:///D:/web/project/前端/pages/house/publish.vue?25cd"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "form", "title", "description", "type", "price", "deposit", "area", "room_count", "hall_count", "bathroom_count", "floor", "total_floors", "orientation", "decoration", "facilities", "images", "location", "province", "city", "district", "address", "longitude", "latitude", "contact", "name", "phone", "wechat", "loading", "houseTypes", "orientations", "decorationTypes", "facilitiesOptions", "typeIndex", "orientationIndex", "decorationIndex", "computed", "canSubmit", "methods", "getTypeLabel", "onTypeChange", "console", "index", "selectedType", "formType", "onOrientationChange", "onDecorationChange", "toggleFacility", "chooseImage", "uni", "count", "sizeType", "sourceType", "success", "removeImage", "chooseLocation", "icon", "fail", "errorMsg", "parseLocationInfo", "handleSubmit", "token", "submitData", "selectedTypeInfo", "request", "action", "result", "setTimeout", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACqC;;;AAG3F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9BA;AAAA;AAAA;AAAA;AAAkoB,CAAgB,goBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACmStpB;AACA;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;QACAC;UACAC;UACAC;UACAC;QACA;MACA;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;QAAAnC;QAAAE;QAAAC;QAAAY;QAAAO;MACA,uBACApB,QACAC,SACAY,2BACAO,uBACAA,wBACA;IACA;EACA;EACAc;IACA;IACAC;MACA;QAAA;MAAA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MAEAC;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;UAAA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACAN;QACAI;UACAZ;UAEA,uDACA;YACApB;YACAC;YACAC;YACAE;UAAA,EACA;;UAEA;UACA;UAEAwB;YACA/C;YACAsD;UACA;QACA;QACAC;UACAhB;UAEA;UACA;YACAiB;UACA;YACAA;UACA;UAEAT;YACA/C;YACAsD;UACA;QACA;MACA;IACA;IAEA;IACAG;MACA;QACA;QACA;QACA;QACA;QAEA;UACA;QACA;QACA;UACA;QACA;QACA;UACA;QACA;QAEAlB;MACA;QACAA;MACA;IACA;IAEA;IACAmB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAnB;gBACAA;gBACAA;gBACAA;;gBAEA;gBACAoB;gBACApB;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACAA;gBACAQ;kBACA/C;kBACAsD;gBACA;gBAAA;cAAA;gBAAA,IAKA;kBAAA;kBAAA;gBAAA;gBACAP;kBACA/C;kBACAsD;gBACA;gBAAA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBACAP;kBACA/C;kBACAsD;gBACA;gBAAA;cAAA;gBAAA,IAKA;kBAAA;kBAAA;gBAAA;gBACAP;kBACA/C;kBACAsD;gBACA;gBAAA;cAAA;gBAIA;gBAAA;gBAGAf;gBAEAqB,6CACA;kBACAzD;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBAAA;gBAGA6B;gBACAA;kBACAR;kBACAW;kBACAf;kBACAkC;gBACA;gBAAA;gBAAA,OAEAC;kBACAC;kBACAjE;gBACA;cAAA;gBAHAkE;gBAKAzB;gBAEA;kBACAQ;oBACA/C;oBACAsD;kBACA;kBAEAW;oBACAlB;kBACA;gBACA;kBACAA;oBACA/C;oBACAsD;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAf;gBACAQ;kBACA/C;kBACAsD;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;EAEAY;IACA;IACA;MACA;MACA;MACA3B;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACllBA;AAAA;AAAA;AAAA;AAAy7B,CAAgB,m5BAAG,EAAC,C;;;;;;;;;;;ACA78B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/house/publish.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/house/publish.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./publish.vue?vue&type=template&id=62878d77&scoped=true&\"\nvar renderjs\nimport script from \"./publish.vue?vue&type=script&lang=js&\"\nexport * from \"./publish.vue?vue&type=script&lang=js&\"\nimport style0 from \"./publish.vue?vue&type=style&index=0&id=62878d77&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"62878d77\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/house/publish.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./publish.vue?vue&type=template&id=62878d77&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.form.type ? _vm.getTypeLabel(_vm.form.type) : null\n  var l0 = _vm.__map(_vm.facilitiesOptions, function (facility, __i0__) {\n    var $orig = _vm.__get_orig(facility)\n    var g0 = _vm.form.facilities.includes(facility.value)\n    var g1 = _vm.form.facilities.includes(facility.value)\n    return {\n      $orig: $orig,\n      g0: g0,\n      g1: g1,\n    }\n  })\n  var g2 = _vm.form.images.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        l0: l0,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./publish.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./publish.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"publish-container\">\n    <!-- 内容区域 -->\n    <scroll-view class=\"content\" scroll-y>\n      <form @submit=\"handleSubmit\">\n      <!-- 基本信息 -->\n      <view class=\"form-section\">\n        <view class=\"section-title\">基本信息</view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">房源标题 *</text>\n          <input \n            class=\"input\" \n            type=\"text\" \n            placeholder=\"请输入房源标题\" \n            v-model=\"form.title\"\n            maxlength=\"50\"\n          />\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">房源描述</text>\n          <textarea \n            class=\"textarea\" \n            placeholder=\"请详细描述房源情况，如周边环境、交通等\" \n            v-model=\"form.description\"\n            maxlength=\"500\"\n          ></textarea>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">房源类型 *</text>\n          <picker \n            mode=\"selector\" \n            :range=\"houseTypes\" \n            range-key=\"label\"\n            :value=\"typeIndex\"\n            @change=\"onTypeChange\"\n          >\n            <view class=\"picker\">\n              {{ form.type ? getTypeLabel(form.type) : '请选择房源类型' }}\n              <uni-icons type=\"arrowright\" size=\"16\" color=\"#ccc\"></uni-icons>\n            </view>\n          </picker>\n        </view>\n      </view>\n      \n      <!-- 房屋信息 -->\n      <view class=\"form-section\">\n        <view class=\"section-title\">房屋信息</view>\n        \n        <view class=\"form-row\">\n          <view class=\"form-item half\">\n            <text class=\"label\">租金(元/月) *</text>\n            <input \n              class=\"input\" \n              type=\"number\" \n              placeholder=\"租金\" \n              v-model=\"form.price\"\n            />\n          </view>\n          <view class=\"form-item half\">\n            <text class=\"label\">押金(元)</text>\n            <input \n              class=\"input\" \n              type=\"number\" \n              placeholder=\"押金\" \n              v-model=\"form.deposit\"\n            />\n          </view>\n        </view>\n        \n        <view class=\"form-row\">\n          <view class=\"form-item half\">\n            <text class=\"label\">面积(㎡)</text>\n            <input \n              class=\"input\" \n              type=\"number\" \n              placeholder=\"面积\" \n              v-model=\"form.area\"\n            />\n          </view>\n          <view class=\"form-item half\">\n            <text class=\"label\">朝向</text>\n            <picker \n              mode=\"selector\" \n              :range=\"orientations\"\n              :value=\"orientationIndex\"\n              @change=\"onOrientationChange\"\n            >\n              <view class=\"picker\">\n                {{ form.orientation || '请选择' }}\n                <uni-icons type=\"arrowright\" size=\"16\" color=\"#ccc\"></uni-icons>\n              </view>\n            </picker>\n          </view>\n        </view>\n        \n        <view class=\"form-row\">\n          <view class=\"form-item third\">\n            <text class=\"label\">房间数</text>\n            <input \n              class=\"input\" \n              type=\"number\" \n              placeholder=\"房间\" \n              v-model=\"form.room_count\"\n            />\n          </view>\n          <view class=\"form-item third\">\n            <text class=\"label\">客厅数</text>\n            <input \n              class=\"input\" \n              type=\"number\" \n              placeholder=\"客厅\" \n              v-model=\"form.hall_count\"\n            />\n          </view>\n          <view class=\"form-item third\">\n            <text class=\"label\">卫生间</text>\n            <input \n              class=\"input\" \n              type=\"number\" \n              placeholder=\"卫生间\" \n              v-model=\"form.bathroom_count\"\n            />\n          </view>\n        </view>\n        \n        <view class=\"form-row\">\n          <view class=\"form-item half\">\n            <text class=\"label\">楼层</text>\n            <input \n              class=\"input\" \n              type=\"number\" \n              placeholder=\"楼层\" \n              v-model=\"form.floor\"\n            />\n          </view>\n          <view class=\"form-item half\">\n            <text class=\"label\">总楼层</text>\n            <input \n              class=\"input\" \n              type=\"number\" \n              placeholder=\"总楼层\" \n              v-model=\"form.total_floors\"\n            />\n          </view>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">装修情况</text>\n          <picker \n            mode=\"selector\" \n            :range=\"decorationTypes\"\n            :value=\"decorationIndex\"\n            @change=\"onDecorationChange\"\n          >\n            <view class=\"picker\">\n              {{ form.decoration || '请选择装修情况' }}\n              <uni-icons type=\"arrowright\" size=\"16\" color=\"#ccc\"></uni-icons>\n            </view>\n          </picker>\n        </view>\n      </view>\n      \n      <!-- 房屋设施 -->\n      <view class=\"form-section\">\n        <view class=\"section-title\">房屋设施</view>\n        <view class=\"facilities-grid\">\n          <view\n            class=\"facility-item\"\n            v-for=\"facility in facilitiesOptions\"\n            :key=\"facility.value\"\n            :class=\"{ active: form.facilities.includes(facility.value) }\"\n            @click=\"toggleFacility(facility.value)\"\n          >\n            <view class=\"facility-icon\">{{ facility.icon || '📦' }}</view>\n            <text class=\"facility-text\">{{ facility.label }}</text>\n            <view class=\"facility-check\" v-if=\"form.facilities.includes(facility.value)\">\n              <uni-icons type=\"checkmarkempty\" size=\"16\" color=\"#fff\"></uni-icons>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 房源图片 -->\n      <view class=\"form-section\">\n        <view class=\"section-title\">房源图片</view>\n        <view class=\"image-upload\">\n          <view class=\"image-list\">\n            <view \n              class=\"image-item\" \n              v-for=\"(image, index) in form.images\" \n              :key=\"index\"\n            >\n              <image :src=\"image\" mode=\"aspectFill\"></image>\n              <view class=\"image-delete\" @click=\"removeImage(index)\">\n                <uni-icons type=\"close\" size=\"16\" color=\"#fff\"></uni-icons>\n              </view>\n            </view>\n            <view \n              class=\"image-add\" \n              v-if=\"form.images.length < 9\"\n              @click=\"chooseImage\"\n            >\n              <uni-icons type=\"plus\" size=\"32\" color=\"#ccc\"></uni-icons>\n              <text class=\"add-text\">添加图片</text>\n            </view>\n          </view>\n          <text class=\"image-tip\">最多上传9张图片，第一张为封面图</text>\n        </view>\n      </view>\n      \n      <!-- 位置信息 -->\n      <view class=\"form-section\">\n        <view class=\"section-title\">位置信息</view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">详细地址 *</text>\n          <input \n            class=\"input\" \n            type=\"text\" \n            placeholder=\"请输入详细地址\" \n            v-model=\"form.location.address\"\n          />\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">选择位置</text>\n          <view class=\"location-picker\" @click=\"chooseLocation\">\n            <view class=\"location-content\">\n              <text class=\"location-text\" :class=\"{ 'placeholder': !form.location.address }\">\n                {{ form.location.address || '点击选择位置' }}\n              </text>\n              <text v-if=\"form.location.name\" class=\"location-name\">{{ form.location.name }}</text>\n            </view>\n            <uni-icons type=\"location\" size=\"16\" color=\"#007aff\"></uni-icons>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 联系方式 -->\n      <view class=\"form-section\">\n        <view class=\"section-title\">联系方式</view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">联系人 *</text>\n          <input \n            class=\"input\" \n            type=\"text\" \n            placeholder=\"请输入联系人姓名\" \n            v-model=\"form.contact.name\"\n          />\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">联系电话 *</text>\n          <input \n            class=\"input\" \n            type=\"number\" \n            placeholder=\"请输入联系电话\" \n            v-model=\"form.contact.phone\"\n          />\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">微信号</text>\n          <input \n            class=\"input\" \n            type=\"text\" \n            placeholder=\"请输入微信号\" \n            v-model=\"form.contact.wechat\"\n          />\n        </view>\n      </view>\n      \n      <!-- 提交按钮 -->\n      <view class=\"submit-section\">\n        <button class=\"submit-btn\" @click=\"handleSubmit\" :disabled=\"!canSubmit\">\n          {{ loading ? '发布中...' : '发布房源' }}\n        </button>\n        <view class=\"debug-info\" style=\"margin-top: 20rpx; font-size: 24rpx; color: #666;\">\n          调试信息: canSubmit={{ canSubmit }}, loading={{ loading }}\n        </view>\n      </view>\n    </form>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nimport request from '@/utils/request.js'\nimport { validatePhone } from '@/utils/common.js'\nimport { HOUSE_TYPES, ORIENTATIONS, DECORATION_TYPES, FACILITIES } from '@/common/config.js'\n\nexport default {\n  data() {\n    return {\n      form: {\n        title: '',\n        description: '',\n        type: '',\n        price: '',\n        deposit: '',\n        area: '',\n        room_count: '',\n        hall_count: '',\n        bathroom_count: '',\n        floor: '',\n        total_floors: '',\n        orientation: '',\n        decoration: '',\n        facilities: [],\n        images: [],\n        location: {\n          province: '',\n          city: '',\n          district: '',\n          address: '',\n          longitude: '',\n          latitude: ''\n        },\n        contact: {\n          name: '',\n          phone: '',\n          wechat: ''\n        }\n      },\n      loading: false,\n      \n      // 选择器数据\n      houseTypes: HOUSE_TYPES,\n      orientations: ORIENTATIONS,\n      decorationTypes: DECORATION_TYPES,\n      facilitiesOptions: FACILITIES,\n      \n      // 选择器索引\n      typeIndex: 0,\n      orientationIndex: 0,\n      decorationIndex: 0\n    }\n  },\n  computed: {\n    canSubmit() {\n      const { title, type, price, location, contact } = this.form\n      return title.trim() && \n             type && \n             price && \n             location.address.trim() && \n             contact.name.trim() && \n             contact.phone.trim() && \n             !this.loading\n    }\n  },\n  methods: {\n    // 获取类型标签\n    getTypeLabel(value) {\n      const type = this.houseTypes.find(item => item.value === value)\n      return type ? type.label : value\n    },\n    \n    // 房源类型选择\n    onTypeChange(e) {\n      const index = e.detail.value\n      this.typeIndex = index\n      this.form.type = this.houseTypes[index].value\n\n      console.log('类型选择变化:', {\n        index: index,\n        selectedType: this.houseTypes[index],\n        formType: this.form.type\n      })\n    },\n    \n    // 朝向选择\n    onOrientationChange(e) {\n      const index = e.detail.value\n      this.orientationIndex = index\n      this.form.orientation = this.orientations[index]\n    },\n    \n    // 装修情况选择\n    onDecorationChange(e) {\n      const index = e.detail.value\n      this.decorationIndex = index\n      this.form.decoration = this.decorationTypes[index]\n    },\n    \n    // 切换设施\n    toggleFacility(value) {\n      const index = this.form.facilities.indexOf(value)\n      if (index > -1) {\n        this.form.facilities.splice(index, 1)\n      } else {\n        this.form.facilities.push(value)\n      }\n    },\n    \n    // 选择图片\n    chooseImage() {\n      const remainCount = 9 - this.form.images.length\n      uni.chooseImage({\n        count: remainCount,\n        sizeType: ['compressed'],\n        sourceType: ['album', 'camera'],\n        success: (res) => {\n          this.form.images.push(...res.tempFilePaths)\n        }\n      })\n    },\n    \n    // 删除图片\n    removeImage(index) {\n      this.form.images.splice(index, 1)\n    },\n    \n    // 选择位置\n    chooseLocation() {\n      // 直接调用选择位置API\n      uni.chooseLocation({\n            success: (res) => {\n              console.log('选择位置成功:', res)\n\n              this.form.location = {\n                ...this.form.location,\n                address: res.address,\n                longitude: res.longitude,\n                latitude: res.latitude,\n                name: res.name || ''\n              }\n\n              // 解析城市和区县信息\n              this.parseLocationInfo(res.address)\n\n              uni.showToast({\n                title: '位置选择成功',\n                icon: 'success'\n              })\n            },\n            fail: (error) => {\n              console.error('选择位置失败:', error)\n\n              let errorMsg = '选择位置失败'\n              if (error.errMsg.includes('cancel')) {\n                errorMsg = '已取消选择位置'\n              } else if (error.errMsg.includes('auth')) {\n                errorMsg = '需要位置权限'\n              }\n\n              uni.showToast({\n                title: errorMsg,\n                icon: 'none'\n              })\n            }\n          })\n    },\n\n    // 解析位置信息\n    parseLocationInfo(address) {\n      try {\n        // 更精确的地址解析\n        const provinceMatch = address.match(/(.+?省)/)\n        const cityMatch = address.match(/(.+?市)/)\n        const districtMatch = address.match(/(.+?[区县])/)\n\n        if (provinceMatch) {\n          this.form.location.province = provinceMatch[1]\n        }\n        if (cityMatch) {\n          this.form.location.city = cityMatch[1]\n        }\n        if (districtMatch) {\n          this.form.location.district = districtMatch[1]\n        }\n\n        console.log('解析后的位置信息:', this.form.location)\n      } catch (error) {\n        console.error('解析位置信息失败:', error)\n      }\n    },\n    \n    // 提交表单\n    async handleSubmit() {\n      console.log('=== 开始提交表单 ===')\n      console.log('canSubmit:', this.canSubmit)\n      console.log('loading:', this.loading)\n      console.log('表单数据:', this.form)\n\n      // 检查用户登录状态\n      const token = uni.getStorageSync('uni_id_token')\n      console.log('用户token:', token ? '已登录' : '未登录')\n\n      if (!this.canSubmit) {\n        console.log('表单验证不通过')\n        uni.showToast({\n          title: '请填写完整信息',\n          icon: 'none'\n        })\n        return\n      }\n\n      // 表单验证\n      if (!validatePhone(this.form.contact.phone)) {\n        uni.showToast({\n          title: '联系电话格式不正确',\n          icon: 'none'\n        })\n        return\n      }\n\n      if (this.form.price <= 0) {\n        uni.showToast({\n          title: '租金必须大于0',\n          icon: 'none'\n        })\n        return\n      }\n\n      // 检查位置信息\n      if (!this.form.location.address) {\n        uni.showToast({\n          title: '请选择房源位置',\n          icon: 'none'\n        })\n        return\n      }\n\n      this.loading = true\n\n      try {\n        console.log('准备调用云函数...')\n\n        const submitData = {\n          ...this.form,\n          price: Number(this.form.price),\n          deposit: Number(this.form.deposit) || 0,\n          area: Number(this.form.area) || 0,\n          room_count: Number(this.form.room_count) || 0,\n          hall_count: Number(this.form.hall_count) || 0,\n          bathroom_count: Number(this.form.bathroom_count) || 0,\n          floor: Number(this.form.floor) || 0,\n          total_floors: Number(this.form.total_floors) || 0\n        }\n\n        console.log('提交数据:', submitData)\n        console.log('房源类型详细信息:', {\n          typeIndex: this.typeIndex,\n          formType: this.form.type,\n          houseTypes: this.houseTypes,\n          selectedTypeInfo: this.houseTypes[this.typeIndex]\n        })\n\n        const result = await request.callFunction('house-management', {\n          action: 'publishHouse',\n          data: submitData\n        })\n\n        console.log('云函数返回结果:', result)\n\n        if (result.code === 0) {\n          uni.showToast({\n            title: '发布成功',\n            icon: 'success'\n          })\n\n          setTimeout(() => {\n            uni.navigateBack()\n          }, 1500)\n        } else {\n          uni.showToast({\n            title: result.message || '发布失败',\n            icon: 'none'\n          })\n        }\n      } catch (error) {\n        console.error('发布失败:', error)\n        uni.showToast({\n          title: '发布失败，请重试',\n          icon: 'none'\n        })\n      } finally {\n        this.loading = false\n      }\n    }\n  },\n\n  onLoad() {\n    // 设置默认房源类型为整租\n    if (!this.form.type && this.houseTypes.length > 0) {\n      this.form.type = this.houseTypes[0].value // 'whole'\n      this.typeIndex = 0\n      console.log('设置默认房源类型:', this.form.type)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.publish-container {\n  background: #f8f9fa;\n  min-height: 100vh;\n}\n\n\n\n/* 内容区域 */\n.content {\n  background: #f8f9fa;\n  min-height: 100vh;\n  padding: 20rpx 0 120rpx;\n}\n\n.form-section {\n  background: #fff;\n  margin: 0 30rpx 40rpx;\n  border-radius: 24rpx;\n  padding: 40rpx 30rpx;\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.06);\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 40rpx;\n  padding-bottom: 30rpx;\n  border-bottom: 2rpx solid #f5f7fa;\n  position: relative;\n}\n\n.section-title::before {\n  content: '';\n  position: absolute;\n  bottom: -2rpx;\n  left: 0;\n  width: 60rpx;\n  height: 4rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 2rpx;\n}\n\n.form-item {\n  margin-bottom: 40rpx; /* 增加表单项之间的间距 */\n}\n\n.form-row {\n  display: flex;\n  gap: 24rpx; /* 增加列之间的间距 */\n  margin-bottom: 40rpx; /* 增加行之间的间距 */\n}\n\n.form-item.half {\n  flex: 1;\n}\n\n.form-item.third {\n  flex: 1;\n}\n\n.label {\n  display: block;\n  font-size: 28rpx;\n  color: #333;\n  margin-bottom: 20rpx;\n  font-weight: 500;\n  line-height: 1.4; /* 增加行高确保文字显示完整 */\n  white-space: nowrap; /* 防止标签文字换行 */\n}\n\n.input, .textarea {\n  width: 100%;\n  background: #f8f9fa;\n  border: 2rpx solid #e9ecef;\n  border-radius: 16rpx;\n  padding: 28rpx 24rpx; /* 增加上下内边距 */\n  font-size: 28rpx;\n  color: #333;\n  box-sizing: border-box;\n  transition: all 0.3s ease;\n  min-height: 96rpx; /* 确保输入框有足够高度 */\n  line-height: 1.4;\n}\n\n.input:focus, .textarea:focus {\n  border-color: #667eea;\n  background: #fff;\n  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);\n  outline: none;\n}\n\n.textarea {\n  min-height: 180rpx; /* 增加文本域的最小高度 */\n  resize: none;\n  line-height: 1.6;\n  padding: 28rpx 24rpx; /* 确保文本域也有足够的内边距 */\n}\n\n.picker {\n  background: #f8f9fa;\n  border: 2rpx solid #e9ecef;\n  border-radius: 16rpx;\n  padding: 28rpx 24rpx; /* 增加上下内边距 */\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: 28rpx;\n  color: #333;\n  transition: all 0.3s ease;\n  min-height: 96rpx; /* 增加最小高度 */\n  box-sizing: border-box;\n  line-height: 1.4;\n}\n\n.picker:active {\n  border-color: #667eea;\n  background: #fff;\n  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);\n}\n\n.facilities-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 20rpx;\n}\n\n.facility-item {\n  background: #f8f9fa;\n  border: 2rpx solid #e9ecef;\n  border-radius: 16rpx;\n  padding: 24rpx;\n  font-size: 26rpx;\n  color: #666;\n  text-align: center;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 120rpx;\n}\n\n.facility-item.active {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-color: #667eea;\n  color: #fff;\n  transform: translateY(-2rpx);\n  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);\n}\n\n.facility-icon {\n  font-size: 32rpx;\n  margin-bottom: 12rpx;\n}\n\n.facility-text {\n  font-size: 26rpx;\n  font-weight: 500;\n}\n\n.facility-item.active .facility-text {\n  color: #fff;\n}\n\n.facility-check {\n  position: absolute;\n  top: 8rpx;\n  right: 8rpx;\n  width: 32rpx;\n  height: 32rpx;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.image-upload {\n  margin-top: 30rpx;\n}\n\n.image-list {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 20rpx;\n  margin-bottom: 30rpx;\n}\n\n.image-item {\n  position: relative;\n  aspect-ratio: 1;\n  border-radius: 16rpx;\n  overflow: hidden;\n  background: #f8f9fa;\n}\n\n.image-item image {\n  width: 100%;\n  height: 100%;\n}\n\n.image-delete {\n  position: absolute;\n  top: 8rpx;\n  right: 8rpx;\n  width: 48rpx;\n  height: 48rpx;\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  backdrop-filter: blur(10rpx);\n}\n\n.image-add {\n  aspect-ratio: 1;\n  background: #f8f9fa;\n  border: 2rpx dashed #e9ecef;\n  border-radius: 16rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n}\n\n.image-add:active {\n  border-color: #667eea;\n  background: rgba(102, 126, 234, 0.05);\n}\n\n.add-text {\n  font-size: 24rpx;\n  color: #667eea;\n  margin-top: 12rpx;\n  font-weight: 500;\n}\n\n.image-tip {\n  display: block;\n  font-size: 24rpx;\n  color: #667eea;\n  margin-top: 20rpx;\n  background: #f0f9ff;\n  padding: 16rpx 20rpx;\n  border-radius: 12rpx;\n  border-left: 4rpx solid #667eea;\n}\n\n.location-picker {\n  background: #f8f9fa;\n  border: 2rpx solid #e9ecef;\n  border-radius: 16rpx;\n  padding: 28rpx 24rpx; /* 增加上下内边距 */\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  transition: all 0.3s ease;\n  min-height: 96rpx; /* 增加最小高度 */\n  box-sizing: border-box;\n}\n\n.location-picker:active {\n  border-color: #667eea;\n  background: #fff;\n  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);\n}\n\n.location-content {\n  flex: 1;\n  display: flex;\n  align-items: center;\n}\n\n.location-content::before {\n  content: '📍';\n  font-size: 32rpx;\n  margin-right: 16rpx;\n}\n\n.location-text {\n  font-size: 28rpx;\n  color: #333;\n  display: block;\n  line-height: 1.4; /* 增加行高 */\n  word-break: break-all; /* 允许长地址换行 */\n}\n\n.location-text.placeholder {\n  color: #999;\n}\n\n.location-name {\n  font-size: 24rpx;\n  color: #666;\n  margin-top: 8rpx;\n  display: block;\n  line-height: 1.4;\n}\n\n.submit-section {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: #fff;\n  padding: 30rpx;\n  border-top: 1rpx solid #f0f0f0;\n  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(10rpx);\n}\n\n.submit-btn {\n  width: 100%;\n  height: 96rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: #fff;\n  border: none;\n  border-radius: 24rpx;\n  font-size: 32rpx;\n  font-weight: bold;\n  box-shadow: 0 8rpx 30rpx rgba(102, 126, 234, 0.3);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.submit-btn::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.5s ease;\n}\n\n.submit-btn:active::before {\n  left: 100%;\n}\n\n.submit-btn:active {\n  transform: translateY(-2rpx);\n  box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.4);\n}\n\n.submit-btn:disabled {\n  background: #e9ecef;\n  color: #999;\n  box-shadow: none;\n  transform: none;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./publish.vue?vue&type=style&index=0&id=62878d77&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./publish.vue?vue&type=style&index=0&id=62878d77&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751999933\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}