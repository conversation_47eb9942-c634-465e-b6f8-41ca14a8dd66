{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端/pages/user/messages.vue?b745", "webpack:///D:/web/project/前端/pages/user/messages.vue?2578", "webpack:///D:/web/project/前端/pages/user/messages.vue?c9e1", "webpack:///D:/web/project/前端/pages/user/messages.vue?ef1e", "uni-app:///pages/user/messages.vue", "webpack:///D:/web/project/前端/pages/user/messages.vue?bf2a", "webpack:///D:/web/project/前端/pages/user/messages.vue?010a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "currentTab", "typeTabs", "label", "value", "count", "messageList", "unreadCount", "loading", "refreshing", "noMore", "page", "pageSize", "methods", "formatRelativeTime", "getTypeText", "switchTab", "loadMessageList", "refresh", "params", "action", "currentType", "request", "result", "console", "loadUnreadCount", "counts", "loadMore", "onRefresh", "readMessage", "message", "message_ids", "handleMessageAction", "uni", "url", "markAllAsRead", "title", "content", "success", "res", "unreadIds", "filter", "map", "msg", "tab", "icon", "updateTabCounts", "onLoad", "onShow", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACqC;;;AAG5F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChCA;AAAA;AAAA;AAAA;AAAmoB,CAAgB,ioBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC4FvpB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC,WACA;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAEA;kBACA;kBACA;gBACA;gBAAA;gBAGAC;kBACAC;kBACApB;oBACAW;oBACAC;kBACA;gBACA,GAEA;gBACAS;gBACA;kBACAF;gBACA;gBAAA;gBAAA,OAEAG;cAAA;gBAAAC;gBAEA;kBAAA,eACAA;kBAEA;oBACA;kBACA;oBACA;kBACA;kBAEA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAH;kBACAF;gBACA;cAAA;gBAFAG;gBAIA;kBACAG;kBACA;;kBAEA;kBACA;kBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAF;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAG;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IAEAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;gBAAA,OAEAR;kBACAF;kBACApB;oBACA+B;kBACA;gBACA;cAAA;gBALAR;gBAOA;kBACAO;kBACA;;kBAEA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAN;cAAA;gBAIA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAQ;MACA;QACA;UACA;UACAC;YACAC;UACA;UACA;QACA;UACA;UACA;YACAD;cACAC;YACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEAF;kBACAG;kBACAC;kBACAC;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAC;gCAAA;gCAAA;8BAAA;8BAAA;8BAEA;8BACAC,+BACAC;gCAAA;8BAAA,GACAC;gCAAA;8BAAA;8BAAA,MAEAF;gCAAA;gCAAA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA,OAEAlB;gCACAF;gCACApB;kCACA+B;gCACA;8BACA;4BAAA;8BALAR;8BAOA;gCACA;gCACA;kCACA;oCACAoB;kCACA;gCACA;gCAEA;;gCAEA;gCACA;kCACAC;gCACA;gCAEAX;kCACAG;kCACAS;gCACA;8BACA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAEArB;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAGA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAsB;MACA;MACA;;MAEA;MACA;QAAA;MAAA;MACA;QACA;MACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;IACAhB;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3VA;AAAA;AAAA;AAAA;AAA07B,CAAgB,o5BAAG,EAAC,C;;;;;;;;;;;ACA98B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/messages.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/user/messages.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./messages.vue?vue&type=template&id=bcbfc132&scoped=true&\"\nvar renderjs\nimport script from \"./messages.vue?vue&type=script&lang=js&\"\nexport * from \"./messages.vue?vue&type=script&lang=js&\"\nimport style0 from \"./messages.vue?vue&type=style&index=0&id=bcbfc132&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"bcbfc132\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/messages.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./messages.vue?vue&type=template&id=bcbfc132&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.messageList.length\n  var l0 = _vm.__map(_vm.messageList, function (message, __i0__) {\n    var $orig = _vm.__get_orig(message)\n    var m0 = _vm.getTypeText(message.type)\n    var m1 = _vm.formatRelativeTime(message.create_date)\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n    }\n  })\n  var g1 = _vm.messageList.length\n  var g2 = !_vm.loading && _vm.messageList.length === 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./messages.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./messages.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"messages-container\">\n    <!-- 消息类型筛选 -->\n    <view class=\"type-tabs\">\n      <view \n        class=\"tab-item\" \n        v-for=\"(tab, index) in typeTabs\" \n        :key=\"index\"\n        :class=\"{ active: currentTab === index }\"\n        @click=\"switchTab(index)\"\n      >\n        <text class=\"tab-text\">{{ tab.label }}</text>\n        <text class=\"tab-count\" v-if=\"tab.count > 0\">{{ tab.count }}</text>\n      </view>\n    </view>\n    \n    <!-- 操作栏 -->\n    <view class=\"action-bar\" v-if=\"messageList.length > 0\">\n      <view class=\"action-left\">\n        <text class=\"unread-count\">{{ unreadCount }}条未读</text>\n      </view>\n      <view class=\"action-right\">\n        <text class=\"mark-all-read\" @click=\"markAllAsRead\" v-if=\"unreadCount > 0\">全部已读</text>\n      </view>\n    </view>\n    \n    <!-- 消息列表 -->\n    <scroll-view \n      class=\"message-list\" \n      scroll-y \n      @scrolltolower=\"loadMore\"\n      refresher-enabled\n      @refresherrefresh=\"onRefresh\"\n      :refresher-triggered=\"refreshing\"\n    >\n      <view \n        class=\"message-item\" \n        v-for=\"message in messageList\" \n        :key=\"message._id\"\n        :class=\"{ unread: !message.is_read }\"\n        @click=\"readMessage(message)\"\n      >\n        <view class=\"message-header\">\n          <view class=\"message-type\" :class=\"message.type\">\n            {{ getTypeText(message.type) }}\n          </view>\n          <text class=\"message-time\">{{ formatRelativeTime(message.create_date) }}</text>\n        </view>\n        \n        <view class=\"message-content\">\n          <text class=\"message-title\">{{ message.title }}</text>\n          <text class=\"message-text\">{{ message.content }}</text>\n        </view>\n        \n        <view class=\"message-footer\" v-if=\"message.from_user\">\n          <view class=\"sender-info\">\n            <image \n              class=\"sender-avatar\" \n              :src=\"message.from_user.avatar || '/static/default-avatar.png'\" \n              mode=\"aspectFill\"\n            ></image>\n            <text class=\"sender-name\">{{ message.from_user.nickname || '系统消息' }}</text>\n          </view>\n          <view class=\"read-status\">\n            <uni-icons \n              v-if=\"!message.is_read\" \n              type=\"circle\" \n              size=\"8\" \n              color=\"#ff4757\"\n            ></uni-icons>\n            <text v-else class=\"read-text\">已读</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 加载状态 -->\n      <view class=\"load-status\" v-if=\"messageList.length > 0\">\n        <text v-if=\"loading\">加载中...</text>\n        <text v-else-if=\"noMore\">没有更多了</text>\n      </view>\n      \n      <!-- 空状态 -->\n      <view class=\"empty-state\" v-if=\"!loading && messageList.length === 0\">\n        <image src=\"/static/empty-message.png\" mode=\"aspectFit\"></image>\n        <text class=\"empty-text\">暂无消息</text>\n        <text class=\"empty-tip\">消息通知会在这里显示</text>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nimport request from '@/utils/request.js'\nimport { formatRelativeTime } from '@/utils/common.js'\nimport { MESSAGE_TYPES } from '@/common/config.js'\n\nexport default {\n  data() {\n    return {\n      currentTab: 0,\n      typeTabs: [\n        { label: '全部', value: '', count: 0 },\n        { label: '系统', value: 'system', count: 0 },\n        { label: '预约', value: 'appointment', count: 0 },\n        { label: '联系', value: 'contact', count: 0 }\n      ],\n      messageList: [],\n      unreadCount: 0,\n      loading: false,\n      refreshing: false,\n      noMore: false,\n      page: 1,\n      pageSize: 10\n    }\n  },\n  methods: {\n    formatRelativeTime,\n    \n    // 获取消息类型文本\n    getTypeText(type) {\n      return MESSAGE_TYPES[type] || type\n    },\n    \n    // 切换标签\n    switchTab(index) {\n      this.currentTab = index\n      this.loadMessageList(true)\n    },\n    \n    // 加载消息列表\n    async loadMessageList(refresh = false) {\n      if (this.loading) return\n      \n      this.loading = true\n      \n      if (refresh) {\n        this.page = 1\n        this.noMore = false\n      }\n      \n      try {\n        const params = {\n          action: 'getMessageList',\n          data: {\n            page: this.page,\n            pageSize: this.pageSize\n          }\n        }\n        \n        // 添加类型筛选\n        const currentType = this.typeTabs[this.currentTab].value\n        if (currentType) {\n          params.data.type = currentType\n        }\n        \n        const result = await request.callFunction('message-management', params)\n        \n        if (result.code === 0) {\n          const { list, total } = result.data\n          \n          if (refresh) {\n            this.messageList = list\n          } else {\n            this.messageList.push(...list)\n          }\n          \n          this.page++\n          this.noMore = this.messageList.length >= total\n        }\n      } catch (error) {\n        console.error('加载消息列表失败:', error)\n      } finally {\n        this.loading = false\n        this.refreshing = false\n      }\n    },\n    \n    // 加载未读消息数量\n    async loadUnreadCount() {\n      try {\n        const result = await request.callFunction('message-management', {\n          action: 'getUnreadCount'\n        })\n        \n        if (result.code === 0) {\n          const counts = result.data\n          this.unreadCount = counts.total\n          \n          // 更新标签计数\n          this.typeTabs[0].count = counts.total\n          this.typeTabs[1].count = counts.system\n          this.typeTabs[2].count = counts.appointment\n          this.typeTabs[3].count = counts.contact\n        }\n      } catch (error) {\n        console.error('加载未读数量失败:', error)\n      }\n    },\n    \n    // 加载更多\n    loadMore() {\n      if (!this.noMore && !this.loading) {\n        this.loadMessageList()\n      }\n    },\n    \n    // 下拉刷新\n    onRefresh() {\n      this.refreshing = true\n      this.loadMessageList(true)\n      this.loadUnreadCount()\n    },\n    \n    // 阅读消息\n    async readMessage(message) {\n      // 如果未读，标记为已读\n      if (!message.is_read) {\n        try {\n          const result = await request.callFunction('message-management', {\n            action: 'markAsRead',\n            data: {\n              message_ids: [message._id]\n            }\n          })\n          \n          if (result.code === 0) {\n            message.is_read = true\n            this.unreadCount = Math.max(0, this.unreadCount - 1)\n            \n            // 更新标签计数\n            this.updateTabCounts(-1, message.type)\n          }\n        } catch (error) {\n          console.error('标记已读失败:', error)\n        }\n      }\n      \n      // 如果有关联内容，跳转到相应页面\n      if (message.related_id) {\n        this.handleMessageAction(message)\n      }\n    },\n    \n    // 处理消息点击动作\n    handleMessageAction(message) {\n      switch (message.type) {\n        case 'appointment':\n          // 跳转到预约详情或预约列表\n          uni.navigateTo({\n            url: '/pages/user/appointments'\n          })\n          break\n        case 'system':\n          // 根据内容判断跳转\n          if (message.content.includes('房源')) {\n            uni.navigateTo({\n              url: `/pages/house/detail?id=${message.related_id}`\n            })\n          }\n          break\n        case 'contact':\n          // 跳转到联系相关页面\n          break\n        default:\n          break\n      }\n    },\n    \n    // 全部标记为已读\n    async markAllAsRead() {\n      if (this.unreadCount === 0) return\n      \n      uni.showModal({\n        title: '提示',\n        content: '确定要将所有消息标记为已读吗？',\n        success: async (res) => {\n          if (res.confirm) {\n            try {\n              // 获取所有未读消息ID\n              const unreadIds = this.messageList\n                .filter(msg => !msg.is_read)\n                .map(msg => msg._id)\n              \n              if (unreadIds.length === 0) return\n              \n              const result = await request.callFunction('message-management', {\n                action: 'markAsRead',\n                data: {\n                  message_ids: unreadIds\n                }\n              })\n              \n              if (result.code === 0) {\n                // 更新本地数据\n                this.messageList.forEach(msg => {\n                  if (!msg.is_read) {\n                    msg.is_read = true\n                  }\n                })\n                \n                this.unreadCount = 0\n                \n                // 重置标签计数\n                this.typeTabs.forEach(tab => {\n                  tab.count = 0\n                })\n                \n                uni.showToast({\n                  title: '已全部标记为已读',\n                  icon: 'success'\n                })\n              }\n            } catch (error) {\n              console.error('批量标记已读失败:', error)\n            }\n          }\n        }\n      })\n    },\n    \n    // 更新标签计数\n    updateTabCounts(delta, messageType) {\n      // 更新总数\n      this.typeTabs[0].count = Math.max(0, this.typeTabs[0].count + delta)\n      \n      // 更新对应类型计数\n      const typeIndex = this.typeTabs.findIndex(tab => tab.value === messageType)\n      if (typeIndex > 0) {\n        this.typeTabs[typeIndex].count = Math.max(0, this.typeTabs[typeIndex].count + delta)\n      }\n    }\n  },\n  \n  onLoad() {\n    this.loadMessageList(true)\n    this.loadUnreadCount()\n  },\n  \n  onShow() {\n    // 从其他页面返回时刷新\n    this.loadUnreadCount()\n  },\n  \n  onPullDownRefresh() {\n    this.onRefresh()\n    uni.stopPullDownRefresh()\n  }\n}\n</script>\n\n<style scoped>\n.messages-container {\n  height: 100vh;\n  background: #f8f9fa;\n  display: flex;\n  flex-direction: column;\n}\n\n.type-tabs {\n  background: #fff;\n  display: flex;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.tab-item {\n  flex: 1;\n  padding: 30rpx 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8rpx;\n  position: relative;\n}\n\n.tab-item.active {\n  color: #007aff;\n}\n\n.tab-item.active::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 60rpx;\n  height: 4rpx;\n  background: #007aff;\n  border-radius: 2rpx;\n}\n\n.tab-text {\n  font-size: 28rpx;\n  color: #333;\n}\n\n.tab-item.active .tab-text {\n  color: #007aff;\n  font-weight: 500;\n}\n\n.tab-count {\n  background: #ff4757;\n  color: #fff;\n  font-size: 20rpx;\n  padding: 4rpx 8rpx;\n  border-radius: 10rpx;\n  min-width: 32rpx;\n  text-align: center;\n}\n\n.action-bar {\n  background: #fff;\n  padding: 20rpx 30rpx;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.unread-count {\n  font-size: 26rpx;\n  color: #666;\n}\n\n.mark-all-read {\n  font-size: 26rpx;\n  color: #007aff;\n}\n\n.message-list {\n  flex: 1;\n  padding: 20rpx;\n}\n\n.message-item {\n  background: #fff;\n  border-radius: 20rpx;\n  margin-bottom: 20rpx;\n  padding: 30rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n  position: relative;\n}\n\n.message-item.unread {\n  border-left: 6rpx solid #007aff;\n}\n\n.message-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 15rpx;\n}\n\n.message-type {\n  padding: 6rpx 12rpx;\n  border-radius: 12rpx;\n  font-size: 22rpx;\n  color: #fff;\n}\n\n.message-type.system {\n  background: #007aff;\n}\n\n.message-type.appointment {\n  background: #10c560;\n}\n\n.message-type.contact {\n  background: #ff9800;\n}\n\n.message-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.message-content {\n  margin-bottom: 20rpx;\n}\n\n.message-title {\n  display: block;\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 10rpx;\n}\n\n.message-text {\n  display: block;\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.5;\n}\n\n.message-footer {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.sender-info {\n  display: flex;\n  align-items: center;\n}\n\n.sender-avatar {\n  width: 40rpx;\n  height: 40rpx;\n  border-radius: 20rpx;\n  margin-right: 12rpx;\n}\n\n.sender-name {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.read-status {\n  display: flex;\n  align-items: center;\n}\n\n.read-text {\n  font-size: 22rpx;\n  color: #ccc;\n}\n\n.load-status {\n  text-align: center;\n  padding: 40rpx;\n  color: #999;\n  font-size: 28rpx;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 100rpx 40rpx;\n}\n\n.empty-state image {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 40rpx;\n}\n\n.empty-text {\n  display: block;\n  font-size: 28rpx;\n  color: #999;\n  margin-bottom: 10rpx;\n}\n\n.empty-tip {\n  display: block;\n  font-size: 24rpx;\n  color: #ccc;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./messages.vue?vue&type=style&index=0&id=bcbfc132&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./messages.vue?vue&type=style&index=0&id=bcbfc132&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751999944\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}