// 网络请求工具类
class Request {
  constructor() {
    this.baseUrl = '';
    this.timeout = 10000;
    this.header = {
      'Content-Type': 'application/json'
    };
  }

  // 请求拦截器
  interceptor = {
    request: (config) => {
      // 添加token
      const token = uni.getStorageSync('uni_id_token');
      if (token) {
        config.header.Authorization = `Bearer ${token}`;
      }
      
      // 显示loading
      if (config.loading !== false) {
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
      }
      
      return config;
    },
    response: (response) => {
      // 隐藏loading
      uni.hideLoading();
      
      const { statusCode, data } = response;
      
      // HTTP状态码检查
      if (statusCode !== 200) {
        this.showError('网络请求失败');
        return Promise.reject(response);
      }
      
      // 业务状态码检查
      if (data.code !== 0) {
        // token失效，跳转登录
        if (data.code === 401) {
          uni.removeStorageSync('uni_id_token');
          uni.removeStorageSync('userInfo');
          uni.reLaunch({
            url: '/pages/login/login'
          });
          return Promise.reject(data);
        }
        
        this.showError(data.message || '请求失败');
        return Promise.reject(data);
      }
      
      return data;
    }
  };

  // 显示错误信息
  showError(message) {
    uni.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  }

  // 通用请求方法
  request(options) {
    const config = {
      url: this.baseUrl + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: { ...this.header, ...options.header },
      timeout: options.timeout || this.timeout,
      loading: options.loading
    };

    // 请求拦截
    const interceptedConfig = this.interceptor.request(config);

    return new Promise((resolve, reject) => {
      uni.request({
        ...interceptedConfig,
        success: (response) => {
          this.interceptor.response(response)
            .then(resolve)
            .catch(reject);
        },
        fail: (error) => {
          uni.hideLoading();
          this.showError('网络连接失败');
          reject(error);
        }
      });
    });
  }

  // GET请求
  get(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'GET',
      data,
      ...options
    });
  }

  // POST请求
  post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    });
  }

  // PUT请求
  put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    });
  }

  // DELETE请求
  delete(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      data,
      ...options
    });
  }

  // 云函数调用
  callFunction(name, data = {}, options = {}) {
    const { showLoading = true, showError = true } = options;

    return new Promise((resolve, reject) => {
      if (showLoading) {
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
      }

      uniCloud.callFunction({
        name,
        data: {
          ...data,
          uniIdToken: uni.getStorageSync('uni_id_token')
        },
        success: (res) => {
          if (showLoading) {
            uni.hideLoading();
          }

          const { result } = res;

          if (result.code === 0) {
            resolve(result);
          } else {
            // token失效处理
            if (result.code === 401) {
              uni.removeStorageSync('uni_id_token');
              uni.removeStorageSync('userInfo');
              uni.reLaunch({
                url: '/pages/login/login'
              });
            } else if (showError) {
              this.showError(result.message || '请求失败');
            }
            reject(result);
          }
        },
        fail: (error) => {
          if (showLoading) {
            uni.hideLoading();
          }

          if (showError) {
            this.showError('服务器连接失败');
          }
          reject(error);
        }
      });
    });
  }
}

export default new Request();
