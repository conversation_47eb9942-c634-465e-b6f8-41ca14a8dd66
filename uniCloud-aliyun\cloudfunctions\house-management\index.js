'use strict';

const uniID = require('uni-id-common');

// 通用 checkToken 函数
async function checkUserToken(token, context) {
  const uniIdIns = uniID.createInstance({ context });
  const payload = await uniIdIns.checkToken(token);

  if (payload.errCode === 0) {
    return {
      code: 0,
      uid: payload.uid,
      userInfo: payload.userInfo
    };
  } else {
    return {
      code: payload.errCode || 401,
      message: payload.errMsg || '身份验证失败'
    };
  }
}

exports.main = async (event, context) => {
  const { action, data } = event;
  
  try {
    switch (action) {
      case 'publishHouse':
        return await publishHouse(event, context);
      case 'updateHouse':
        return await updateHouse(event, context);
      case 'deleteHouse':
        return await deleteHouse(event, context);
      case 'getHouseDetail':
        return await getHouseDetail(event, context);
      case 'getHouseList':
        return await getHouseList(event, context);
      case 'searchHouses':
        return await searchHouses(event, context);
      case 'getMyHouses':
        return await getMyHouses(event, context);
      case 'updateHouseStatus':
        return await updateHouseStatus(event, context);
      default:
        return {
          code: 400,
          message: '无效的操作'
        };
    }
  } catch (error) {
    console.error('房源管理云函数执行错误:', error);
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message
    };
  }
};

// 发布房源
async function publishHouse(event, context) {
  const payload = await checkUserToken(event.uniIdToken, context);
  if (payload.code !== 0) {
    return payload;
  }
  
  const houseData = event.data;

  // 调试信息
  console.log('云函数接收到的房源数据:', houseData);
  console.log('房源类型信息:', {
    type: houseData.type,
    typeType: typeof houseData.type
  });

  // 参数验证
  if (!houseData.title || !houseData.price || !houseData.location) {
    return {
      code: 400,
      message: '房源标题、价格和位置信息不能为空'
    };
  }
  
  try {
    const db = uniCloud.database();
    const now = new Date();
    
    // 计算过期时间（30天后）
    const expireDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    
    const house = {
      ...houseData,
      publisher_id: payload.uid,
      status: 'available',
      view_count: 0,
      favorite_count: 0,
      is_verified: false,
      publish_date: now,
      update_date: now,
      expire_date: expireDate
    };

    console.log('准备保存到数据库的房源数据:', house);
    console.log('最终房源类型:', house.type);

    const result = await db.collection('houses').add(house);
    
    return {
      code: 0,
      message: '发布成功',
      data: {
        house_id: result.id
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '发布失败',
      error: error.message
    };
  }
}

// 更新房源
async function updateHouse(event, context) {
  const payload = await checkUserToken(event.uniIdToken, context);
  if (payload.code !== 0) {
    return payload;
  }
  
  const { house_id, ...updateData } = event.data;
  
  if (!house_id) {
    return {
      code: 400,
      message: '房源ID不能为空'
    };
  }
  
  try {
    const db = uniCloud.database();
    
    // 检查房源是否存在且属于当前用户
    const houseRes = await db.collection('houses').doc(house_id).get();
    if (houseRes.data.length === 0) {
      return {
        code: 404,
        message: '房源不存在'
      };
    }
    
    const house = houseRes.data[0];
    if (house.publisher_id !== payload.uid) {
      return {
        code: 403,
        message: '无权限操作此房源'
      };
    }
    
    // 更新房源信息
    updateData.update_date = new Date();
    await db.collection('houses').doc(house_id).update(updateData);
    
    return {
      code: 0,
      message: '更新成功'
    };
  } catch (error) {
    return {
      code: 500,
      message: '更新失败',
      error: error.message
    };
  }
}

// 删除房源
async function deleteHouse(event, context) {
  const payload = await checkUserToken(event.uniIdToken, context);
  if (payload.code !== 0) {
    return payload;
  }
  
  const { house_id } = event.data;
  
  if (!house_id) {
    return {
      code: 400,
      message: '房源ID不能为空'
    };
  }
  
  try {
    const db = uniCloud.database();
    
    // 检查房源是否存在且属于当前用户
    const houseRes = await db.collection('houses').doc(house_id).get();
    if (houseRes.data.length === 0) {
      return {
        code: 404,
        message: '房源不存在'
      };
    }
    
    const house = houseRes.data[0];
    if (house.publisher_id !== payload.uid) {
      return {
        code: 403,
        message: '无权限操作此房源'
      };
    }
    
    // 删除房源
    await db.collection('houses').doc(house_id).remove();
    
    // 删除相关的收藏记录
    await db.collection('favorites').where({
      house_id: house_id
    }).remove();
    
    // 删除相关的预约记录
    await db.collection('appointments').where({
      house_id: house_id
    }).remove();
    
    return {
      code: 0,
      message: '删除成功'
    };
  } catch (error) {
    return {
      code: 500,
      message: '删除失败',
      error: error.message
    };
  }
}

// 获取房源详情
async function getHouseDetail(event, context) {
  const { house_id } = event.data;
  
  if (!house_id) {
    return {
      code: 400,
      message: '房源ID不能为空'
    };
  }
  
  try {
    const db = uniCloud.database();
    
    // 获取房源详情
    const houseRes = await db.collection('houses').doc(house_id).get();
    if (houseRes.data.length === 0) {
      return {
        code: 404,
        message: '房源不存在'
      };
    }
    
    const house = houseRes.data[0];
    
    // 增加浏览次数
    await db.collection('houses').doc(house_id).update({
      view_count: db.command.inc(1)
    });
    
    // 获取发布者信息
    const publisherRes = await db.collection('uni-id-users').doc(house.publisher_id).field({
      nickname: true,
      avatar: true,
      role: true,
      current_role: true
    }).get();

    if (publisherRes.data.length > 0) {
      house.publisher_info = publisherRes.data[0];
    }
    
    return {
      code: 0,
      message: '获取成功',
      data: house
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取失败',
      error: error.message
    };
  }
}

// 获取房源列表
async function getHouseList(event, context) {
  const { page = 1, pageSize = 10, city, district, type, minPrice, maxPrice, sort = 'publish_date_desc' } = event.data;

  try {
    const db = uniCloud.database();

    let query = db.collection('houses').where({
      status: 'available',
      is_verified: true
    });

    // 地区筛选
    if (city) {
      query = query.where({
        'location.city': city
      });
    }
    if (district) {
      query = query.where({
        'location.district': district
      });
    }

    // 房源类型筛选
    if (type) {
      query = query.where({
        type: type
      });
    }

    // 价格筛选
    if (minPrice !== undefined || maxPrice !== undefined) {
      const priceCondition = {};
      if (minPrice !== undefined && minPrice !== null) priceCondition[db.command.gte] = minPrice;
      if (maxPrice !== undefined && maxPrice !== null) priceCondition[db.command.lte] = maxPrice;
      query = query.where({
        price: priceCondition
      });
    }

    // 排序
    const sortMap = {
      'publish_date_desc': { publish_date: -1 },
      'price_asc': { price: 1 },
      'price_desc': { price: -1 },
      'view_count_desc': { view_count: -1 },
      'favorite_count_desc': { favorite_count: -1 }
    };

    if (sortMap[sort]) {
      query = query.orderBy(Object.keys(sortMap[sort])[0], Object.values(sortMap[sort])[0] === 1 ? 'asc' : 'desc');
    }

    // 分页
    const skip = (page - 1) * pageSize;
    const result = await query.skip(skip).limit(pageSize).get();

    // 获取总数
    const countResult = await query.count();

    console.log('查询结果:', {
      count: result.data.length,
      total: countResult.total,
      firstItem: result.data[0] ? {
        title: result.data[0].title,
        price: result.data[0].price
      } : null
    });

    return {
      code: 0,
      message: '获取成功',
      data: {
        list: result.data,
        total: countResult.total,
        page: page,
        pageSize: pageSize
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取失败',
      error: error.message
    };
  }
}

// 搜索房源
async function searchHouses(event, context) {
  const { keyword, page = 1, pageSize = 10 } = event.data;

  if (!keyword) {
    return {
      code: 400,
      message: '搜索关键词不能为空'
    };
  }

  try {
    const db = uniCloud.database();
    const regex = new RegExp(keyword, 'i');

    const query = db.collection('houses').where(db.command.and([
      {
        status: 'available',
        is_verified: true
      },
      db.command.or([
        { title: regex },
        { description: regex },
        { 'location.address': regex }
      ])
    ]));

    const skip = (page - 1) * pageSize;
    const result = await query.skip(skip).limit(pageSize).orderBy('publish_date', 'desc').get();

    const countResult = await query.count();

    return {
      code: 0,
      message: '搜索成功',
      data: {
        list: result.data,
        total: countResult.total,
        page: page,
        pageSize: pageSize
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '搜索失败',
      error: error.message
    };
  }
}

// 获取我的房源
async function getMyHouses(event, context) {
  const payload = await checkUserToken(event.uniIdToken, context);
  if (payload.code !== 0) {
    return payload;
  }

  const { page = 1, pageSize = 10, status, countOnly = false } = event.data;

  try {
    const db = uniCloud.database();
    let query = db.collection('houses').where({
      publisher_id: payload.uid
    });

    if (status) {
      query = query.where({
        status: status
      });
    }

    // 如果只需要数量，直接返回count结果
    if (countOnly) {
      const countResult = await query.count();
      return {
        code: 0,
        message: '获取成功',
        data: {
          total: countResult.total
        }
      };
    }

    const skip = (page - 1) * pageSize;
    const result = await query.skip(skip).limit(pageSize).orderBy('publish_date', 'desc').get();

    const countResult = await query.count();

    return {
      code: 0,
      message: '获取成功',
      data: {
        list: result.data,
        total: countResult.total,
        page: page,
        pageSize: pageSize
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取失败',
      error: error.message
    };
  }
}

// 更新房源状态
async function updateHouseStatus(event, context) {
  const payload = await checkUserToken(event.uniIdToken, context);
  if (payload.code !== 0) {
    return payload;
  }

  const { house_id, status } = event.data;

  if (!house_id || !status) {
    return {
      code: 400,
      message: '房源ID和状态不能为空'
    };
  }

  const validStatus = ['available', 'rented', 'offline'];
  if (!validStatus.includes(status)) {
    return {
      code: 400,
      message: '无效的状态值'
    };
  }

  try {
    const db = uniCloud.database();

    // 检查房源是否存在且属于当前用户
    const houseRes = await db.collection('houses').doc(house_id).get();
    if (houseRes.data.length === 0) {
      return {
        code: 404,
        message: '房源不存在'
      };
    }

    const house = houseRes.data[0];
    if (house.publisher_id !== payload.uid) {
      return {
        code: 403,
        message: '无权限操作此房源'
      };
    }

    await db.collection('houses').doc(house_id).update({
      status: status,
      update_date: new Date()
    });

    return {
      code: 0,
      message: '状态更新成功'
    };
  } catch (error) {
    return {
      code: 500,
      message: '状态更新失败',
      error: error.message
    };
  }
}


