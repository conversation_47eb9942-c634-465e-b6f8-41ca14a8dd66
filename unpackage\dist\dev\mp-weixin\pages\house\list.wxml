<view class="house-list-container data-v-145608e6"><view class="filter-bar data-v-145608e6"><view data-event-opts="{{[['tap',[['toggleTypeFilter',['$event']]]]]}}" class="{{['filter-item','data-v-145608e6',(showTypeOptions)?'active':'']}}" bindtap="__e"><text class="filter-text data-v-145608e6">{{selectedType.label}}</text><uni-icons vue-id="766b94e0-1" type="{{showTypeOptions?'arrowup':'arrowdown'}}" size="12" color="#666" class="data-v-145608e6" bind:__l="__l"></uni-icons></view><view data-event-opts="{{[['tap',[['togglePriceFilter',['$event']]]]]}}" class="{{['filter-item','data-v-145608e6',(showPriceOptions)?'active':'']}}" bindtap="__e"><text class="filter-text data-v-145608e6">{{selectedPrice.label}}</text><uni-icons vue-id="766b94e0-2" type="{{showPriceOptions?'arrowup':'arrowdown'}}" size="12" color="#666" class="data-v-145608e6" bind:__l="__l"></uni-icons></view><view data-event-opts="{{[['tap',[['toggleSortFilter',['$event']]]]]}}" class="{{['filter-item','data-v-145608e6',(showSortOptions)?'active':'']}}" bindtap="__e"><text class="filter-text data-v-145608e6">{{selectedSort.label}}</text><uni-icons vue-id="766b94e0-3" type="{{showSortOptions?'arrowup':'arrowdown'}}" size="12" color="#666" class="data-v-145608e6" bind:__l="__l"></uni-icons></view><view data-event-opts="{{[['tap',[['toSearch',['$event']]]]]}}" class="filter-item data-v-145608e6" bindtap="__e"><uni-icons vue-id="766b94e0-4" type="search" size="16" color="#666" class="data-v-145608e6" bind:__l="__l"></uni-icons></view></view><block wx:if="{{showTypeOptions||showPriceOptions||showSortOptions}}"><view class="filter-options-container data-v-145608e6"><block wx:if="{{showTypeOptions}}"><view class="filter-options data-v-145608e6"><view class="options-title data-v-145608e6">房源类型</view><view class="options-grid data-v-145608e6"><block wx:for="{{typeOptions}}" wx:for-item="option" wx:for-index="__i0__" wx:key="value"><view data-event-opts="{{[['tap',[['selectType',['$0'],[[['typeOptions','value',option.value]]]]]]]}}" class="{{['option-item','data-v-145608e6',(selectedType.value===option.value)?'selected':'']}}" bindtap="__e"><text class="option-text data-v-145608e6">{{option.label}}</text></view></block></view></view></block><block wx:if="{{showPriceOptions}}"><view class="filter-options data-v-145608e6"><view class="options-title data-v-145608e6">价格区间</view><view class="price-input-container data-v-145608e6"><view class="price-input-group data-v-145608e6"><text class="input-label data-v-145608e6">最低价</text><input class="price-input data-v-145608e6" type="number" placeholder="不限" data-event-opts="{{[['input',[['__set_model',['$0','min','$event',[]],['customPrice']],['onPriceInput',['$event']]]]]}}" value="{{customPrice.min}}" bindinput="__e"/><text class="price-unit data-v-145608e6">元/月</text></view><view class="price-separator data-v-145608e6">-</view><view class="price-input-group data-v-145608e6"><text class="input-label data-v-145608e6">最高价</text><input class="price-input data-v-145608e6" type="number" placeholder="不限" data-event-opts="{{[['input',[['__set_model',['$0','max','$event',[]],['customPrice']],['onPriceInput',['$event']]]]]}}" value="{{customPrice.max}}" bindinput="__e"/><text class="price-unit data-v-145608e6">元/月</text></view></view><view class="price-actions data-v-145608e6"><button data-event-opts="{{[['tap',[['resetPrice',['$event']]]]]}}" class="price-btn reset-btn data-v-145608e6" bindtap="__e">重置</button><button data-event-opts="{{[['tap',[['confirmPrice',['$event']]]]]}}" class="price-btn confirm-btn data-v-145608e6" bindtap="__e">确定</button></view></view></block><block wx:if="{{showSortOptions}}"><view class="filter-options data-v-145608e6"><view class="options-title data-v-145608e6">排序方式</view><view class="options-list data-v-145608e6"><block wx:for="{{sortOptions}}" wx:for-item="option" wx:for-index="__i1__" wx:key="value"><view data-event-opts="{{[['tap',[['selectSort',['$0'],[[['sortOptions','value',option.value]]]]]]]}}" class="{{['option-item-list','data-v-145608e6',(selectedSort.value===option.value)?'selected':'']}}" bindtap="__e"><text class="option-text data-v-145608e6">{{option.label}}</text><block wx:if="{{selectedSort.value===option.value}}"><uni-icons vue-id="{{'766b94e0-5-'+__i1__}}" type="checkmarkempty" size="16" color="#007aff" class="data-v-145608e6" bind:__l="__l"></uni-icons></block></view></block></view></view></block></view></block><scroll-view class="house-list data-v-145608e6" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]],['tap',[['closeAllFilters',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e" bindtap="__e"><block wx:for="{{$root.l0}}" wx:for-item="house" wx:for-index="__i2__" wx:key="_id"><view data-event-opts="{{[['tap',[['toHouseDetail',['$0'],[[['houseList','_id',house.$orig._id,'_id']]]]]]]}}" class="house-item data-v-145608e6" bindtap="__e"><view class="house-image data-v-145608e6"><image src="{{house.$orig.images&&house.$orig.images[0]||'/static/default-house.png'}}" mode="aspectFill" class="data-v-145608e6"></image><view class="house-type data-v-145608e6">{{house.m0}}</view><view data-event-opts="{{[['tap',[['toggleFavorite',['$0'],[[['houseList','_id',house.$orig._id]]]]]]]}}" class="favorite-btn data-v-145608e6" catchtap="__e"><uni-icons vue-id="{{'766b94e0-6-'+__i2__}}" type="heart" size="20" color="#fff" class="data-v-145608e6" bind:__l="__l"></uni-icons></view></view><view class="house-info data-v-145608e6"><text class="house-title data-v-145608e6">{{house.$orig.title}}</text><text class="house-desc data-v-145608e6">{{house.$orig.description}}</text><view class="house-tags data-v-145608e6"><block wx:if="{{house.$orig.room_count}}"><text class="tag data-v-145608e6">{{house.$orig.room_count+"室"}}</text></block><block wx:if="{{house.$orig.hall_count}}"><text class="tag data-v-145608e6">{{house.$orig.hall_count+"厅"}}</text></block><block wx:if="{{house.$orig.area}}"><text class="tag data-v-145608e6">{{house.$orig.area+"㎡"}}</text></block><block wx:if="{{house.$orig.orientation}}"><text class="tag data-v-145608e6">{{house.$orig.orientation}}</text></block></view><view class="house-location data-v-145608e6"><uni-icons vue-id="{{'766b94e0-7-'+__i2__}}" type="location" size="12" color="#999" class="data-v-145608e6" bind:__l="__l"></uni-icons><text class="location-text data-v-145608e6">{{house.$orig.location.district+" "+house.$orig.location.address}}</text></view><view class="house-bottom data-v-145608e6"><view class="price-info data-v-145608e6"><text class="price data-v-145608e6">{{"¥"+house.$orig.price}}</text><text class="price-unit data-v-145608e6">/月</text></view><view class="house-stats data-v-145608e6"><text class="stat-item data-v-145608e6"><uni-icons vue-id="{{'766b94e0-8-'+__i2__}}" type="eye" size="12" color="#999" class="data-v-145608e6" bind:__l="__l"></uni-icons>{{''+(house.$orig.view_count||0)+''}}</text><text class="stat-item data-v-145608e6"><uni-icons vue-id="{{'766b94e0-9-'+__i2__}}" type="heart" size="12" color="#999" class="data-v-145608e6" bind:__l="__l"></uni-icons>{{''+(house.$orig.favorite_count||0)+''}}</text></view></view></view></view></block><block wx:if="{{$root.g0>0}}"><view class="load-status data-v-145608e6"><block wx:if="{{loading}}"><text class="data-v-145608e6">加载中...</text></block><block wx:else><block wx:if="{{noMore}}"><text class="data-v-145608e6">没有更多了</text></block></block></view></block><block wx:if="{{$root.g1}}"><view class="empty-state data-v-145608e6"><image src="/static/empty-house.png" mode="aspectFit" class="data-v-145608e6"></image><text class="empty-text data-v-145608e6">暂无房源信息</text></view></block></scroll-view><block wx:if="{{canPublish}}"><view data-event-opts="{{[['tap',[['toPublish',['$event']]]]]}}" class="publish-btn data-v-145608e6" bindtap="__e"><uni-icons vue-id="766b94e0-10" type="plus" size="24" color="#fff" class="data-v-145608e6" bind:__l="__l"></uni-icons></view></block></view>