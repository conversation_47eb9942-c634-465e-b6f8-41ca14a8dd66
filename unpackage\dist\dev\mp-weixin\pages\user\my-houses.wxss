
.my-houses-container.data-v-ddfcefee {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}
.status-tabs.data-v-ddfcefee {
  background: #fff;
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}
.tab-item.data-v-ddfcefee {
  flex: 1;
  padding: 30rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  position: relative;
}
.tab-item.active.data-v-ddfcefee {
  color: #007aff;
}
.tab-item.active.data-v-ddfcefee::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #007aff;
  border-radius: 2rpx;
}
.tab-text.data-v-ddfcefee {
  font-size: 28rpx;
  color: #333;
}
.tab-item.active .tab-text.data-v-ddfcefee {
  color: #007aff;
  font-weight: 500;
}
.tab-count.data-v-ddfcefee {
  background: #ff4757;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 32rpx;
  text-align: center;
}
.house-list.data-v-ddfcefee {
  flex: 1;
  padding: 20rpx;
}
.house-item.data-v-ddfcefee {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  display: flex;
}
.house-image.data-v-ddfcefee {
  position: relative;
  width: 240rpx;
  height: 200rpx;
  flex-shrink: 0;
}
.house-image image.data-v-ddfcefee {
  width: 100%;
  height: 100%;
}
.house-status.data-v-ddfcefee {
  position: absolute;
  top: 15rpx;
  left: 15rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: #fff;
}
.house-status.available.data-v-ddfcefee {
  background: #10c560;
}
.house-status.rented.data-v-ddfcefee {
  background: #ff6b6b;
}
.house-status.offline.data-v-ddfcefee {
  background: #999;
}
.house-info.data-v-ddfcefee {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0; /* 防止flex子项溢出 */
}
.house-title.data-v-ddfcefee {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.house-tags.data-v-ddfcefee {
  display: flex;
  gap: 8rpx;
  margin-bottom: 10rpx;
  flex-wrap: wrap;
}
.tag.data-v-ddfcefee {
  background: #f0f0f0;
  color: #666;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  font-size: 22rpx;
}
.house-location.data-v-ddfcefee {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  min-width: 0; /* 防止溢出 */
}
.location-text.data-v-ddfcefee {
  font-size: 24rpx;
  color: #999;
  margin-left: 6rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1; /* 占用剩余空间 */
  min-width: 0; /* 防止溢出 */
}
.house-bottom.data-v-ddfcefee {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10rpx;
}
.price-info.data-v-ddfcefee {
  display: flex;
  align-items: baseline;
}
.price.data-v-ddfcefee {
  font-size: 30rpx;
  font-weight: bold;
  color: #ff6b6b;
}
.price-unit.data-v-ddfcefee {
  font-size: 20rpx;
  color: #999;
  margin-left: 4rpx;
}
.house-stats.data-v-ddfcefee {
  display: flex;
  gap: 15rpx;
}
.stat-item.data-v-ddfcefee {
  font-size: 20rpx;
  color: #999;
  display: flex;
  align-items: center;
  gap: 4rpx;
}
.publish-time.data-v-ddfcefee {
  font-size: 22rpx;
  color: #ccc;
}
.house-actions.data-v-ddfcefee {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20rpx;
  gap: 15rpx;
  border-left: 1rpx solid #f0f0f0;
  width: 160rpx; /* 固定宽度，确保按钮可见 */
  flex-shrink: 0; /* 防止被压缩 */
}
.action-btn.data-v-ddfcefee {
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
  width: 100%; /* 占满容器宽度 */
  text-align: center;
  font-weight: 500;
  transition: all 0.3s ease;
}
.action-btn.data-v-ddfcefee:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.action-btn.edit.data-v-ddfcefee {
  background: #e3f2fd;
  color: #007aff;
  border: 1rpx solid #007aff;
}
.action-btn.status.data-v-ddfcefee {
  background: #fff3e0;
  color: #ff9800;
  border: 1rpx solid #ff9800;
}
.action-btn.delete.data-v-ddfcefee {
  background: #ffebee;
  color: #f44336;
  border: 1rpx solid #f44336;
}
.load-status.data-v-ddfcefee {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}
.empty-state.data-v-ddfcefee {
  text-align: center;
  padding: 100rpx 40rpx;
}
.empty-state image.data-v-ddfcefee {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}
.empty-text.data-v-ddfcefee {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}
.publish-btn.data-v-ddfcefee {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}
.floating-btn.data-v-ddfcefee {
  position: fixed;
  right: 40rpx;
  bottom: 100rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(45deg, #007aff, #0056d3);
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 30rpx rgba(0, 122, 255, 0.3);
}

