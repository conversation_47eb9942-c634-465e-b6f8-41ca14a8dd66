<template>
  <view class="login-container">
    <view class="header">
      <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
      <text class="title">毕业租房平台</text>
      <text class="subtitle">找到理想的住所</text>
    </view>
    
    <view class="form-container">
      <view class="form-item">
        <view class="input-wrapper">
          <uni-icons type="person" size="20" color="#999"></uni-icons>
          <input 
            class="input" 
            type="text" 
            placeholder="请输入用户名/手机号" 
            v-model="form.username"
            maxlength="20"
          />
        </view>
      </view>
      
      <view class="form-item">
        <view class="input-wrapper">
          <uni-icons type="locked" size="20" color="#999"></uni-icons>
          <input 
            class="input" 
            :type="showPassword ? 'text' : 'password'" 
            placeholder="请输入密码" 
            v-model="form.password"
            maxlength="20"
          />
          <uni-icons 
            :type="showPassword ? 'eye-slash' : 'eye'" 
            size="20" 
            color="#999"
            @click="togglePassword"
          ></uni-icons>
        </view>
      </view>
      
      <button class="login-btn" @click="handleLogin" :disabled="!canLogin">
        {{ loading ? '登录中...' : '登录' }}
      </button>
      
      <view class="links">
        <text class="link" @click="toRegister">还没有账号？立即注册</text>
        <text class="link" @click="toForgotPassword">忘记密码？</text>
      </view>
    </view>
    
    <view class="footer">
      <text class="footer-text">登录即表示同意</text>
      <text class="footer-link" @click="toPrivacy">《用户协议》</text>
      <text class="footer-text">和</text>
      <text class="footer-link" @click="toPrivacy">《隐私政策》</text>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
import { validatePhone, setUserInfo } from '@/utils/common.js'

export default {
  data() {
    return {
      form: {
        username: '',
        password: ''
      },
      showPassword: false,
      loading: false
    }
  },
  computed: {
    canLogin() {
      return this.form.username.trim() && this.form.password.trim() && !this.loading
    }
  },
  methods: {
    // 切换密码显示状态
    togglePassword() {
      this.showPassword = !this.showPassword
    },
    
    // 登录处理
    async handleLogin() {
      if (!this.canLogin) return
      
      const { username, password } = this.form
      
      // 基础验证
      if (username.length < 2) {
        uni.showToast({
          title: '用户名至少2个字符',
          icon: 'none'
        })
        return
      }
      
      if (password.length < 6) {
        uni.showToast({
          title: '密码至少6个字符',
          icon: 'none'
        })
        return
      }
      
      this.loading = true
      
      try {
        const result = await request.callFunction('user-auth', {
          action: 'login',
          data: {
            username: username.trim(),
            password: password
          }
        })
        
        if (result.code === 0) {
          // 保存token和用户信息
          uni.setStorageSync('uni_id_token', result.data.token)
          setUserInfo(result.data.userInfo)

          uni.showToast({
            title: '登录成功',
            icon: 'success'
          })
          
          // 延迟跳转，让用户看到成功提示
          setTimeout(() => {
            // 获取来源页面参数，判断是否需要跳转到特定页面
            const pages = getCurrentPages()
            const currentPage = pages[pages.length - 1]
            const options = currentPage.options || {}

            if (options.redirect) {
              // 如果有重定向参数，跳转到指定页面
              uni.reLaunch({
                url: decodeURIComponent(options.redirect)
              })
            } else if (pages.length > 1) {
              // 如果有来源页面，返回上一页
              uni.navigateBack()
            } else {
              // 否则跳转到个人中心页面
              uni.switchTab({
                url: '/pages/user/profile'
              })
            }
          }, 1500)
        }
      } catch (error) {
        console.error('登录失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 跳转到注册页
    toRegister() {
      uni.navigateTo({
        url: '/pages/register/register'
      })
    },
    
    // 跳转到忘记密码页
    toForgotPassword() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },
    
    // 跳转到隐私政策页
    toPrivacy() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    }
  },
  
  onLoad(options) {
    // 如果已登录，直接跳转
    const token = uni.getStorageSync('uni_id_token')
    if (token) {
      uni.reLaunch({
        url: '/pages/index/index'
      })
    }
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  display: flex;
  flex-direction: column;
}

.header {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.form-container {
  flex: 1;
}

.form-item {
  margin-bottom: 40rpx;
}

.input-wrapper {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  height: 100rpx;
}

.input {
  flex: 1;
  margin-left: 20rpx;
  font-size: 32rpx;
  color: #333;
}

.login-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: #fff;
  border: none;
  border-radius: 50rpx;
  font-size: 36rpx;
  font-weight: bold;
  margin-top: 40rpx;
  margin-bottom: 40rpx;
}

.login-btn:disabled {
  background: #ccc;
}

.links {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.link {
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
}

.footer {
  text-align: center;
  margin-top: auto;
  padding-top: 40rpx;
}

.footer-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 24rpx;
}

.footer-link {
  color: rgba(255, 255, 255, 0.9);
  font-size: 24rpx;
}
</style>
