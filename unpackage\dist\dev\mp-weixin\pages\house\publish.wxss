
.publish-container.data-v-62878d77 {
  background: #f8f9fa;
  min-height: 100vh;
}



/* 内容区域 */
.content.data-v-62878d77 {
  background: #f8f9fa;
  min-height: 100vh;
  padding: 20rpx 0 120rpx;
}
.form-section.data-v-62878d77 {
  background: #fff;
  margin: 0 30rpx 40rpx;
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}
.section-title.data-v-62878d77 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 2rpx solid #f5f7fa;
  position: relative;
}
.section-title.data-v-62878d77::before {
  content: '';
  position: absolute;
  bottom: -2rpx;
  left: 0;
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2rpx;
}
.form-item.data-v-62878d77 {
  margin-bottom: 40rpx; /* 增加表单项之间的间距 */
}
.form-row.data-v-62878d77 {
  display: flex;
  gap: 24rpx; /* 增加列之间的间距 */
  margin-bottom: 40rpx; /* 增加行之间的间距 */
}
.form-item.half.data-v-62878d77 {
  flex: 1;
}
.form-item.third.data-v-62878d77 {
  flex: 1;
}
.label.data-v-62878d77 {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
  line-height: 1.4; /* 增加行高确保文字显示完整 */
  white-space: nowrap; /* 防止标签文字换行 */
}
.input.data-v-62878d77, .textarea.data-v-62878d77 {
  width: 100%;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 28rpx 24rpx; /* 增加上下内边距 */
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s ease;
  min-height: 96rpx; /* 确保输入框有足够高度 */
  line-height: 1.4;
}
.input.data-v-62878d77:focus, .textarea.data-v-62878d77:focus {
  border-color: #667eea;
  background: #fff;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
  outline: none;
}
.textarea.data-v-62878d77 {
  min-height: 180rpx; /* 增加文本域的最小高度 */
  resize: none;
  line-height: 1.6;
  padding: 28rpx 24rpx; /* 确保文本域也有足够的内边距 */
}
.picker.data-v-62878d77 {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 28rpx 24rpx; /* 增加上下内边距 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  color: #333;
  transition: all 0.3s ease;
  min-height: 96rpx; /* 增加最小高度 */
  box-sizing: border-box;
  line-height: 1.4;
}
.picker.data-v-62878d77:active {
  border-color: #667eea;
  background: #fff;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}
.facilities-grid.data-v-62878d77 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}
.facility-item.data-v-62878d77 {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 26rpx;
  color: #666;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 120rpx;
}
.facility-item.active.data-v-62878d77 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: #fff;
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
}
.facility-icon.data-v-62878d77 {
  font-size: 32rpx;
  margin-bottom: 12rpx;
}
.facility-text.data-v-62878d77 {
  font-size: 26rpx;
  font-weight: 500;
}
.facility-item.active .facility-text.data-v-62878d77 {
  color: #fff;
}
.facility-check.data-v-62878d77 {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.image-upload.data-v-62878d77 {
  margin-top: 30rpx;
}
.image-list.data-v-62878d77 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.image-item.data-v-62878d77 {
  position: relative;
  aspect-ratio: 1;
  border-radius: 16rpx;
  overflow: hidden;
  background: #f8f9fa;
}
.image-item image.data-v-62878d77 {
  width: 100%;
  height: 100%;
}
.image-delete.data-v-62878d77 {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.image-add.data-v-62878d77 {
  aspect-ratio: 1;
  background: #f8f9fa;
  border: 2rpx dashed #e9ecef;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.image-add.data-v-62878d77:active {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}
.add-text.data-v-62878d77 {
  font-size: 24rpx;
  color: #667eea;
  margin-top: 12rpx;
  font-weight: 500;
}
.image-tip.data-v-62878d77 {
  display: block;
  font-size: 24rpx;
  color: #667eea;
  margin-top: 20rpx;
  background: #f0f9ff;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
}
.location-picker.data-v-62878d77 {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 28rpx 24rpx; /* 增加上下内边距 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  min-height: 96rpx; /* 增加最小高度 */
  box-sizing: border-box;
}
.location-picker.data-v-62878d77:active {
  border-color: #667eea;
  background: #fff;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}
.location-content.data-v-62878d77 {
  flex: 1;
  display: flex;
  align-items: center;
}
.location-content.data-v-62878d77::before {
  content: '📍';
  font-size: 32rpx;
  margin-right: 16rpx;
}
.location-text.data-v-62878d77 {
  font-size: 28rpx;
  color: #333;
  display: block;
  line-height: 1.4; /* 增加行高 */
  word-break: break-all; /* 允许长地址换行 */
}
.location-text.placeholder.data-v-62878d77 {
  color: #999;
}
.location-name.data-v-62878d77 {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
  display: block;
  line-height: 1.4;
}
.submit-section.data-v-62878d77 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.submit-btn.data-v-62878d77 {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 30rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.submit-btn.data-v-62878d77::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}
.submit-btn.data-v-62878d77:active::before {
  left: 100%;
}
.submit-btn.data-v-62878d77:active {
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
  box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.4);
}
.submit-btn.data-v-62878d77:disabled {
  background: #e9ecef;
  color: #999;
  box-shadow: none;
  -webkit-transform: none;
          transform: none;
}

