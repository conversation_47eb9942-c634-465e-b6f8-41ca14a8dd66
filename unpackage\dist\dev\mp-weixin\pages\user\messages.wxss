
.messages-container.data-v-bcbfc132 {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}
.type-tabs.data-v-bcbfc132 {
  background: #fff;
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}
.tab-item.data-v-bcbfc132 {
  flex: 1;
  padding: 30rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  position: relative;
}
.tab-item.active.data-v-bcbfc132 {
  color: #007aff;
}
.tab-item.active.data-v-bcbfc132::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #007aff;
  border-radius: 2rpx;
}
.tab-text.data-v-bcbfc132 {
  font-size: 28rpx;
  color: #333;
}
.tab-item.active .tab-text.data-v-bcbfc132 {
  color: #007aff;
  font-weight: 500;
}
.tab-count.data-v-bcbfc132 {
  background: #ff4757;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 32rpx;
  text-align: center;
}
.action-bar.data-v-bcbfc132 {
  background: #fff;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #f0f0f0;
}
.unread-count.data-v-bcbfc132 {
  font-size: 26rpx;
  color: #666;
}
.mark-all-read.data-v-bcbfc132 {
  font-size: 26rpx;
  color: #007aff;
}
.message-list.data-v-bcbfc132 {
  flex: 1;
  padding: 20rpx;
}
.message-item.data-v-bcbfc132 {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  position: relative;
}
.message-item.unread.data-v-bcbfc132 {
  border-left: 6rpx solid #007aff;
}
.message-header.data-v-bcbfc132 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15rpx;
}
.message-type.data-v-bcbfc132 {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: #fff;
}
.message-type.system.data-v-bcbfc132 {
  background: #007aff;
}
.message-type.appointment.data-v-bcbfc132 {
  background: #10c560;
}
.message-type.contact.data-v-bcbfc132 {
  background: #ff9800;
}
.message-time.data-v-bcbfc132 {
  font-size: 24rpx;
  color: #999;
}
.message-content.data-v-bcbfc132 {
  margin-bottom: 20rpx;
}
.message-title.data-v-bcbfc132 {
  display: block;
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}
.message-text.data-v-bcbfc132 {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}
.message-footer.data-v-bcbfc132 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.sender-info.data-v-bcbfc132 {
  display: flex;
  align-items: center;
}
.sender-avatar.data-v-bcbfc132 {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  margin-right: 12rpx;
}
.sender-name.data-v-bcbfc132 {
  font-size: 24rpx;
  color: #999;
}
.read-status.data-v-bcbfc132 {
  display: flex;
  align-items: center;
}
.read-text.data-v-bcbfc132 {
  font-size: 22rpx;
  color: #ccc;
}
.load-status.data-v-bcbfc132 {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}
.empty-state.data-v-bcbfc132 {
  text-align: center;
  padding: 100rpx 40rpx;
}
.empty-state image.data-v-bcbfc132 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}
.empty-text.data-v-bcbfc132 {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.empty-tip.data-v-bcbfc132 {
  display: block;
  font-size: 24rpx;
  color: #ccc;
}

