<template>
  <view class="user-list">
    <!-- 搜索和筛选 -->
    <view class="search-section">
      <view class="search-row">
        <view class="search-item">
          <uni-easyinput 
            v-model="searchKeyword" 
            placeholder="搜索用户昵称、手机号"
            @confirm="onSearch"
            clearable
          >
            <template #right>
              <uni-icons type="search" size="18" color="#999" @click="onSearch" />
            </template>
          </uni-easyinput>
        </view>
        
        <view class="filter-item">
          <uni-data-picker 
            v-model="filters.status" 
            :localdata="statusOptions"
            placeholder="用户状态"
            @change="onFilterChange"
          />
        </view>
        
        <view class="filter-item">
          <uni-datetime-picker 
            v-model="filters.dateRange" 
            type="daterange"
            placeholder="注册时间"
            @change="onFilterChange"
          />
        </view>
        
        <button class="reset-btn" @click="resetFilters">重置</button>
        <button class="init-btn" @click="initTestData">初始化测试数据</button>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-bar">
      <view class="stat-item">
        <text class="stat-label">总用户</text>
        <text class="stat-value">{{ statistics.total }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">活跃用户</text>
        <text class="stat-value">{{ statistics.active }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">今日新增</text>
        <text class="stat-value">{{ statistics.todayNew }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">本月新增</text>
        <text class="stat-value">{{ statistics.monthNew }}</text>
      </view>
    </view>

    <!-- 用户列表 -->
    <view class="list-section">
      <uni-table 
        :loading="loading"
        border
        stripe
        emptyText="暂无用户数据"
      >
        <uni-tr>
          <uni-th width="80" align="center">
            <checkbox @change="onSelectAll" :checked="isAllSelected" />
          </uni-th>
          <uni-th width="100" align="center">头像</uni-th>
          <uni-th width="150" align="center">用户信息</uni-th>
          <uni-th width="120" align="center">联系方式</uni-th>
          <uni-th width="100" align="center">状态</uni-th>
          <uni-th width="100" align="center">房源数</uni-th>
          <uni-th width="100" align="center">预约数</uni-th>
          <uni-th width="120" align="center">注册时间</uni-th>
          <uni-th width="120" align="center">最后登录</uni-th>
          <uni-th width="200" align="center">操作</uni-th>
        </uni-tr>
        
        <uni-tr v-for="(user, index) in userList" :key="user._id">
          <uni-td align="center">
            <checkbox 
              :checked="selectedIds.includes(user._id)"
              @change="onSelectItem(user._id)"
            />
          </uni-td>
          
          <uni-td align="center">
            <image 
              class="user-avatar" 
              :src="user.avatar || '/static/default-avatar.png'"
              mode="aspectFill"
            />
          </uni-td>
          
          <uni-td>
            <view class="user-info">
              <text class="user-nickname">{{ user.nickname || '未设置' }}</text>
              <text class="user-id">ID: {{ user._id.slice(-8) }}</text>
              <text class="user-role">{{ getRoleText(user.role) }}</text>
            </view>
          </uni-td>
          
          <uni-td>
            <view class="contact-info">
              <text class="phone" v-if="user.mobile">{{ user.mobile }}</text>
              <text class="email" v-if="user.email">{{ user.email }}</text>
            </view>
          </uni-td>
          
          <uni-td align="center">
            <uni-tag 
              :text="getStatusText(user.status)" 
              :type="getStatusType(user.status)"
            />
          </uni-td>
          
          <uni-td align="center">
            <text class="count-text" @click="viewUserHouses(user._id)">
              {{ user.house_count || 0 }}
            </text>
          </uni-td>
          
          <uni-td align="center">
            <text class="count-text" @click="viewUserAppointments(user._id)">
              {{ user.appointment_count || 0 }}
            </text>
          </uni-td>
          
          <uni-td align="center">
            <text class="date-text">{{ formatDate(user.register_date) }}</text>
          </uni-td>
          
          <uni-td align="center">
            <text class="date-text">{{ formatDate(user.last_login_date) }}</text>
          </uni-td>
          
          <uni-td align="center">
            <view class="action-buttons">
              <button 
                class="action-btn view-btn" 
                @click="viewUser(user)"
              >
                查看
              </button>
              <button 
                v-if="user.status === 'active'"
                class="action-btn disable-btn" 
                @click="disableUser(user)"
              >
                禁用
              </button>
              <button 
                v-if="user.status === 'disabled'"
                class="action-btn enable-btn" 
                @click="enableUser(user)"
              >
                启用
              </button>
              <button 
                class="action-btn delete-btn" 
                @click="deleteUser(user)"
              >
                删除
              </button>
            </view>
          </uni-td>
        </uni-tr>
      </uni-table>
      
      <!-- 批量操作 -->
      <view class="batch-actions" v-if="selectedIds.length > 0">
        <text class="selected-count">已选择 {{ selectedIds.length }} 项</text>
        <button class="batch-btn disable-btn" @click="batchDisable">批量禁用</button>
        <button class="batch-btn enable-btn" @click="batchEnable">批量启用</button>
        <button class="batch-btn delete-btn" @click="batchDelete">批量删除</button>
      </view>
      
      <!-- 分页 -->
      <view class="pagination">
        <uni-pagination 
          :current="currentPage"
          :total="totalCount"
          :pageSize="pageSize"
          @change="onPageChange"
        />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'UserList',
  data() {
    return {
      loading: false,
      searchKeyword: '',
      
      // 筛选条件
      filters: {
        status: '',
        dateRange: []
      },
      
      // 筛选选项
      statusOptions: [
        { value: '', text: '全部状态' },
        { value: 'active', text: '正常' },
        { value: 'disabled', text: '禁用' }
      ],
      
      // 统计数据
      statistics: {
        total: 0,
        active: 0,
        todayNew: 0,
        monthNew: 0
      },
      
      // 列表数据
      userList: [],
      selectedIds: [],
      isAllSelected: false,
      
      // 分页
      currentPage: 1,
      pageSize: 10,
      totalCount: 0
    }
  },

  onLoad() {
    this.loadUserList()
    this.loadStatistics()
  },

  onPullDownRefresh() {
    this.currentPage = 1
    this.loadUserList().then(() => {
      uni.stopPullDownRefresh()
    })
  },

  methods: {
    // 加载用户列表
    async loadUserList() {
      try {
        this.loading = true
        
        const result = await uniCloud.callFunction({
          name: 'user-auth',
          data: {
            action: 'getUserListForAdmin',
            page: this.currentPage,
            pageSize: this.pageSize,
            keyword: this.searchKeyword,
            filters: this.filters
          }
        })

        if (result.result.code === 0) {
          this.userList = result.result.data.list
          this.totalCount = result.result.data.total
        } else {
          uni.showToast({
            title: result.result.message,
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('加载用户列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 加载统计数据
    async loadStatistics() {
      try {
        const result = await uniCloud.callFunction({
          name: 'system-management',
          data: {
            action: 'getUserStatistics'
          }
        })

        if (result.result.code === 0) {
          this.statistics = result.result.data
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    // 搜索
    onSearch() {
      this.currentPage = 1
      this.loadUserList()
    },

    // 筛选改变
    onFilterChange() {
      this.currentPage = 1
      this.loadUserList()
    },

    // 重置筛选
    resetFilters() {
      this.searchKeyword = ''
      this.filters = {
        status: '',
        dateRange: []
      }
      this.currentPage = 1
      this.loadUserList()
    },

    // 全选/取消全选
    onSelectAll(e) {
      this.isAllSelected = e.detail.value
      if (this.isAllSelected) {
        this.selectedIds = this.userList.map(item => item._id)
      } else {
        this.selectedIds = []
      }
    },

    // 选择单项
    onSelectItem(id) {
      const index = this.selectedIds.indexOf(id)
      if (index > -1) {
        this.selectedIds.splice(index, 1)
      } else {
        this.selectedIds.push(id)
      }
      
      this.isAllSelected = this.selectedIds.length === this.userList.length
    },

    // 查看用户
    viewUser(user) {
      uni.navigateTo({
        url: `/pages/rental/user/detail?id=${user._id}`
      })
    },

    // 查看用户房源
    viewUserHouses(userId) {
      uni.navigateTo({
        url: `/pages/rental/house/list?publisher_id=${userId}`
      })
    },

    // 查看用户预约
    viewUserAppointments(userId) {
      uni.navigateTo({
        url: `/pages/rental/appointment/list?user_id=${userId}`
      })
    },

    // 禁用用户
    async disableUser(user) {
      uni.showModal({
        title: '确认禁用',
        content: `确定要禁用用户"${user.nickname}"吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await uniCloud.callFunction({
                name: 'user-auth',
                data: {
                  action: 'disableUser',
                  user_id: user._id
                }
              })

              if (result.result.code === 0) {
                uni.showToast({
                  title: '禁用成功',
                  icon: 'success'
                })
                this.loadUserList()
                this.loadStatistics()
              } else {
                uni.showToast({
                  title: result.result.message,
                  icon: 'none'
                })
              }
            } catch (error) {
              console.error('禁用用户失败:', error)
              uni.showToast({
                title: '禁用失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    // 启用用户
    async enableUser(user) {
      uni.showModal({
        title: '确认启用',
        content: `确定要启用用户"${user.nickname}"吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await uniCloud.callFunction({
                name: 'user-auth',
                data: {
                  action: 'enableUser',
                  user_id: user._id
                }
              })

              if (result.result.code === 0) {
                uni.showToast({
                  title: '启用成功',
                  icon: 'success'
                })
                this.loadUserList()
                this.loadStatistics()
              } else {
                uni.showToast({
                  title: result.result.message,
                  icon: 'none'
                })
              }
            } catch (error) {
              console.error('启用用户失败:', error)
              uni.showToast({
                title: '启用失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    // 删除用户
    deleteUser(user) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除用户"${user.nickname}"吗？此操作不可恢复！`,
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await uniCloud.callFunction({
                name: 'user-auth',
                data: {
                  action: 'deleteUser',
                  user_id: user._id
                }
              })

              if (result.result.code === 0) {
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                })
                this.loadUserList()
                this.loadStatistics()
              } else {
                uni.showToast({
                  title: result.result.message,
                  icon: 'none'
                })
              }
            } catch (error) {
              console.error('删除用户失败:', error)
              uni.showToast({
                title: '删除失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    // 批量禁用
    batchDisable() {
      uni.showModal({
        title: '批量禁用',
        content: `确定要禁用选中的 ${this.selectedIds.length} 个用户吗？`,
        success: async (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '批量禁用功能开发中',
              icon: 'none'
            })
          }
        }
      })
    },

    // 批量启用
    batchEnable() {
      uni.showModal({
        title: '批量启用',
        content: `确定要启用选中的 ${this.selectedIds.length} 个用户吗？`,
        success: async (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '批量启用功能开发中',
              icon: 'none'
            })
          }
        }
      })
    },

    // 批量删除
    batchDelete() {
      uni.showModal({
        title: '批量删除',
        content: `确定要删除选中的 ${this.selectedIds.length} 个用户吗？此操作不可恢复！`,
        success: async (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '批量删除功能开发中',
              icon: 'none'
            })
          }
        }
      })
    },

    // 分页改变
    onPageChange(page) {
      this.currentPage = page
      this.loadUserList()
    },

    // 初始化测试数据
    async initTestData() {
      uni.showModal({
        title: '初始化测试数据',
        content: '这将创建一些测试用户数据，确定要继续吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              uni.showLoading({
                title: '初始化中...'
              })

              // 调用云函数初始化数据
              const result = await uniCloud.callFunction({
                name: 'user-auth',
                data: {
                  action: 'initTestData'
                }
              })

              console.log('初始化结果:', result)

              if (result.result && result.result.code === 0) {
                uni.hideLoading()
                uni.showToast({
                  title: '测试数据初始化成功',
                  icon: 'success'
                })

                // 重新加载数据
                this.loadUserList()
                this.loadStatistics()
              } else {
                throw new Error(result.result?.message || '初始化失败')
              }

            } catch (error) {
              console.error('初始化测试数据失败:', error)
              uni.hideLoading()
              uni.showToast({
                title: '初始化失败: ' + error.message,
                icon: 'none'
              })
            }
          }
        }
      })
    },

    // 获取角色文本
    getRoleText(role) {
      if (Array.isArray(role) && role.length > 0) {
        return role.includes('admin') ? '管理员' : '普通用户'
      }
      return '普通用户'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'active': '正常',
        'disabled': '禁用'
      }
      return statusMap[status] || '未知'
    },

    // 获取状态类型
    getStatusType(status) {
      const typeMap = {
        'active': 'success',
        'disabled': 'error'
      }
      return typeMap[status] || 'default'
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '-'
      const d = new Date(date)
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
    }
  }
}
</script>

<style lang="scss" scoped>
.user-list {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.search-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.search-row {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex-wrap: wrap;
}

.search-item {
  flex: 1;
  min-width: 300rpx;
}

.filter-item {
  min-width: 200rpx;
}

.reset-btn {
  background: #8E8E93;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.init-btn {
  background: #34C759;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.stats-bar {
  display: flex;
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  gap: 60rpx;
}

.stat-item {
  text-align: center;
  
  .stat-label {
    display: block;
    font-size: 28rpx;
    color: #666;
    margin-bottom: 10rpx;
  }
  
  .stat-value {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
}

.list-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
}

.user-info {
  .user-nickname {
    display: block;
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
  }
  
  .user-id {
    display: block;
    font-size: 24rpx;
    color: #999;
    margin-bottom: 8rpx;
  }
  
  .user-role {
    display: inline-block;
    background: #f0f0f0;
    color: #666;
    padding: 4rpx 12rpx;
    border-radius: 4rpx;
    font-size: 22rpx;
  }
}

.contact-info {
  .phone, .email {
    display: block;
    font-size: 24rpx;
    color: #333;
    margin-bottom: 4rpx;
  }
}

.count-text {
  color: #007AFF;
  text-decoration: underline;
  cursor: pointer;
}

.date-text {
  font-size: 24rpx;
  color: #666;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.action-btn {
  padding: 8rpx 16rpx;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
  cursor: pointer;
  
  &.view-btn {
    background: #007AFF;
    color: #fff;
  }
  
  &.disable-btn {
    background: #FF9500;
    color: #fff;
  }
  
  &.enable-btn {
    background: #34C759;
    color: #fff;
  }
  
  &.delete-btn {
    background: #FF3B30;
    color: #fff;
  }
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-top: 30rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
}

.selected-count {
  font-size: 28rpx;
  color: #333;
}

.batch-btn {
  padding: 12rpx 24rpx;
  border: none;
  border-radius: 6rpx;
  font-size: 26rpx;
  cursor: pointer;
  
  &.disable-btn {
    background: #FF9500;
    color: #fff;
  }
  
  &.enable-btn {
    background: #34C759;
    color: #fff;
  }
  
  &.delete-btn {
    background: #FF3B30;
    color: #fff;
  }
}

.pagination {
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .search-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .stats-bar {
    flex-wrap: wrap;
    gap: 30rpx;
  }
  
  .action-buttons {
    flex-direction: row;
    flex-wrap: wrap;
  }
}
</style>
