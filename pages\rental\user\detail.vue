<template>
  <view class="user-detail">
    <view v-if="loading" class="loading">
      <uni-load-more status="loading" />
    </view>
    
    <view v-else-if="userInfo" class="detail-content">
      <!-- 用户基本信息 -->
      <view class="info-section">
        <view class="section-header">
          <text class="section-title">用户基本信息</text>
          <view class="status-badges">
            <uni-tag 
              :text="getStatusText(userInfo.status)" 
              :type="getStatusType(userInfo.status)"
            />
            <uni-tag 
              :text="getRoleText(userInfo.role)" 
              type="primary"
            />
          </view>
        </view>
        
        <view class="user-profile">
          <image 
            class="user-avatar" 
            :src="userInfo.avatar || '/static/default-avatar.png'"
            mode="aspectFill"
          />
          <view class="profile-info">
            <text class="user-nickname">{{ userInfo.nickname || '未设置昵称' }}</text>
            <text class="user-id">用户ID: {{ userInfo._id }}</text>
            <text class="register-date">注册时间: {{ formatDateTime(userInfo.register_date) }}</text>
            <text class="last-login">最后登录: {{ formatDateTime(userInfo.last_login_date) }}</text>
          </view>
        </view>
        
        <view class="info-grid">
          <view class="info-item">
            <text class="info-label">手机号</text>
            <text class="info-value">{{ userInfo.mobile || '未绑定' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">邮箱</text>
            <text class="info-value">{{ userInfo.email || '未绑定' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">性别</text>
            <text class="info-value">{{ getGenderText(userInfo.gender) }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">年龄</text>
            <text class="info-value">{{ userInfo.age || '未设置' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">职业</text>
            <text class="info-value">{{ userInfo.occupation || '未设置' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">学校</text>
            <text class="info-value">{{ userInfo.school || '未设置' }}</text>
          </view>
        </view>
        
        <view class="info-item full-width" v-if="userInfo.bio">
          <text class="info-label">个人简介</text>
          <text class="info-value description">{{ userInfo.bio }}</text>
        </view>
      </view>

      <!-- 统计数据 -->
      <view class="stats-section">
        <view class="section-header">
          <text class="section-title">数据统计</text>
        </view>
        
        <view class="stats-grid">
          <view class="stat-card" @click="viewUserHouses">
            <view class="stat-icon house-icon">
              <uni-icons type="home" size="24" color="#fff" />
            </view>
            <view class="stat-content">
              <text class="stat-value">{{ userStats.houseCount }}</text>
              <text class="stat-label">发布房源</text>
            </view>
          </view>
          
          <view class="stat-card" @click="viewUserAppointments">
            <view class="stat-icon appointment-icon">
              <uni-icons type="calendar" size="24" color="#fff" />
            </view>
            <view class="stat-content">
              <text class="stat-value">{{ userStats.appointmentCount }}</text>
              <text class="stat-label">预约记录</text>
            </view>
          </view>
          
          <view class="stat-card">
            <view class="stat-icon favorite-icon">
              <uni-icons type="heart" size="24" color="#fff" />
            </view>
            <view class="stat-content">
              <text class="stat-value">{{ userStats.favoriteCount }}</text>
              <text class="stat-label">收藏房源</text>
            </view>
          </view>
          
          <view class="stat-card">
            <view class="stat-icon view-icon">
              <uni-icons type="eye" size="24" color="#fff" />
            </view>
            <view class="stat-content">
              <text class="stat-value">{{ userStats.totalViews }}</text>
              <text class="stat-label">房源浏览</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 最近发布的房源 -->
      <view class="recent-houses-section" v-if="recentHouses.length > 0">
        <view class="section-header">
          <text class="section-title">最近发布的房源</text>
          <button class="view-all-btn" @click="viewUserHouses">查看全部</button>
        </view>
        
        <view class="house-list">
          <view 
            v-for="house in recentHouses" 
            :key="house._id"
            class="house-item"
            @click="viewHouse(house._id)"
          >
            <image 
              class="house-cover" 
              :src="house.images && house.images[0] || '/static/placeholder.png'"
              mode="aspectFill"
            />
            <view class="house-info">
              <text class="house-title">{{ house.title }}</text>
              <text class="house-price">¥{{ house.price }}/月</text>
              <text class="house-address">{{ house.location?.address }}</text>
              <view class="house-meta">
                <uni-tag 
                  :text="house.is_verified ? '已审核' : '待审核'" 
                  :type="house.is_verified ? 'success' : 'warning'"
                  size="small"
                />
                <text class="publish-date">{{ formatDate(house.publish_date) }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 最近预约记录 -->
      <view class="recent-appointments-section" v-if="recentAppointments.length > 0">
        <view class="section-header">
          <text class="section-title">最近预约记录</text>
          <button class="view-all-btn" @click="viewUserAppointments">查看全部</button>
        </view>
        
        <view class="appointment-list">
          <view 
            v-for="appointment in recentAppointments" 
            :key="appointment._id"
            class="appointment-item"
          >
            <view class="appointment-info">
              <text class="house-title">{{ appointment.house_info?.title }}</text>
              <text class="appointment-time">预约时间: {{ formatDateTime(appointment.appointment_time) }}</text>
              <text class="contact-info">联系方式: {{ appointment.contact_phone }}</text>
            </view>
            <view class="appointment-status">
              <uni-tag 
                :text="getAppointmentStatusText(appointment.status)" 
                :type="getAppointmentStatusType(appointment.status)"
              />
            </view>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-section">
        <button 
          v-if="userInfo.status === 'active'"
          class="action-btn disable-btn" 
          @click="disableUser"
        >
          禁用用户
        </button>
        <button 
          v-if="userInfo.status === 'disabled'"
          class="action-btn enable-btn" 
          @click="enableUser"
        >
          启用用户
        </button>
        <button class="action-btn reset-pwd-btn" @click="resetPassword">重置密码</button>
        <button class="action-btn delete-btn" @click="deleteUser">删除用户</button>
      </view>
    </view>
    
    <view v-else class="error-state">
      <text>用户信息加载失败</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'UserDetail',
  data() {
    return {
      loading: true,
      userId: '',
      userInfo: null,
      userStats: {
        houseCount: 0,
        appointmentCount: 0,
        favoriteCount: 0,
        totalViews: 0
      },
      recentHouses: [],
      recentAppointments: []
    }
  },

  onLoad(options) {
    this.userId = options.id
    if (this.userId) {
      this.loadUserDetail()
    }
  },

  methods: {
    // 加载用户详情
    async loadUserDetail() {
      try {
        this.loading = true
        
        // 并发加载用户信息、统计数据、最近房源、最近预约
        const [userResult, statsResult, housesResult, appointmentsResult] = await Promise.all([
          uniCloud.callFunction({
            name: 'user-auth',
            data: {
              action: 'getUserDetailForAdmin',
              user_id: this.userId
            }
          }),
          uniCloud.callFunction({
            name: 'system-management',
            data: {
              action: 'getUserStats',
              user_id: this.userId
            }
          }),
          uniCloud.callFunction({
            name: 'house-management',
            data: {
              action: 'getUserRecentHouses',
              user_id: this.userId,
              limit: 5
            }
          }),
          uniCloud.callFunction({
            name: 'appointment-management',
            data: {
              action: 'getUserRecentAppointments',
              user_id: this.userId,
              limit: 5
            }
          })
        ])

        if (userResult.result.code === 0) {
          this.userInfo = userResult.result.data
        } else {
          uni.showToast({
            title: userResult.result.message,
            icon: 'none'
          })
        }

        if (statsResult.result.code === 0) {
          this.userStats = statsResult.result.data
        }

        if (housesResult.result.code === 0) {
          this.recentHouses = housesResult.result.data
        }

        if (appointmentsResult.result.code === 0) {
          this.recentAppointments = appointmentsResult.result.data
        }

      } catch (error) {
        console.error('加载用户详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 查看用户房源
    viewUserHouses() {
      uni.navigateTo({
        url: `/pages/rental/house/list?publisher_id=${this.userId}`
      })
    },

    // 查看用户预约
    viewUserAppointments() {
      uni.navigateTo({
        url: `/pages/rental/appointment/list?user_id=${this.userId}`
      })
    },

    // 查看房源详情
    viewHouse(houseId) {
      uni.navigateTo({
        url: `/pages/rental/house/detail?id=${houseId}`
      })
    },

    // 禁用用户
    async disableUser() {
      uni.showModal({
        title: '确认禁用',
        content: `确定要禁用用户"${this.userInfo.nickname}"吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await uniCloud.callFunction({
                name: 'user-auth',
                data: {
                  action: 'disableUser',
                  user_id: this.userId
                }
              })

              if (result.result.code === 0) {
                uni.showToast({
                  title: '禁用成功',
                  icon: 'success'
                })
                this.loadUserDetail()
              } else {
                uni.showToast({
                  title: result.result.message,
                  icon: 'none'
                })
              }
            } catch (error) {
              console.error('禁用用户失败:', error)
              uni.showToast({
                title: '禁用失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    // 启用用户
    async enableUser() {
      uni.showModal({
        title: '确认启用',
        content: `确定要启用用户"${this.userInfo.nickname}"吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await uniCloud.callFunction({
                name: 'user-auth',
                data: {
                  action: 'enableUser',
                  user_id: this.userId
                }
              })

              if (result.result.code === 0) {
                uni.showToast({
                  title: '启用成功',
                  icon: 'success'
                })
                this.loadUserDetail()
              } else {
                uni.showToast({
                  title: result.result.message,
                  icon: 'none'
                })
              }
            } catch (error) {
              console.error('启用用户失败:', error)
              uni.showToast({
                title: '启用失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    // 重置密码
    resetPassword() {
      uni.showModal({
        title: '重置密码',
        content: '确定要重置该用户的密码吗？新密码将发送到用户手机。',
        success: async (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '重置密码功能开发中',
              icon: 'none'
            })
          }
        }
      })
    },

    // 删除用户
    deleteUser() {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除用户"${this.userInfo.nickname}"吗？此操作不可恢复！`,
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await uniCloud.callFunction({
                name: 'user-auth',
                data: {
                  action: 'deleteUser',
                  user_id: this.userId
                }
              })

              if (result.result.code === 0) {
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                })
                setTimeout(() => {
                  uni.navigateBack()
                }, 1500)
              } else {
                uni.showToast({
                  title: result.result.message,
                  icon: 'none'
                })
              }
            } catch (error) {
              console.error('删除用户失败:', error)
              uni.showToast({
                title: '删除失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    // 获取角色文本
    getRoleText(role) {
      if (Array.isArray(role) && role.length > 0) {
        return role.includes('admin') ? '管理员' : '普通用户'
      }
      return '普通用户'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'active': '正常',
        'disabled': '禁用'
      }
      return statusMap[status] || '未知'
    },

    // 获取状态类型
    getStatusType(status) {
      const typeMap = {
        'active': 'success',
        'disabled': 'error'
      }
      return typeMap[status] || 'default'
    },

    // 获取性别文本
    getGenderText(gender) {
      const genderMap = {
        'male': '男',
        'female': '女'
      }
      return genderMap[gender] || '未设置'
    },

    // 获取预约状态文本
    getAppointmentStatusText(status) {
      const statusMap = {
        'pending': '待确认',
        'confirmed': '已确认',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusMap[status] || '未知'
    },

    // 获取预约状态类型
    getAppointmentStatusType(status) {
      const typeMap = {
        'pending': 'warning',
        'confirmed': 'primary',
        'completed': 'success',
        'cancelled': 'error'
      }
      return typeMap[status] || 'default'
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '-'
      const d = new Date(date)
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
    },

    // 格式化日期时间
    formatDateTime(date) {
      if (!date) return '-'
      const d = new Date(date)
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}`
    }
  }
}
</script>

<style lang="scss" scoped>
.user-detail {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.detail-content {
  .info-section, .stats-section, .recent-houses-section, .recent-appointments-section {
    background: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
  
  .section-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
  
  .status-badges {
    display: flex;
    gap: 10rpx;
  }
  
  .view-all-btn {
    background: #007AFF;
    color: #fff;
    border: none;
    border-radius: 8rpx;
    padding: 12rpx 24rpx;
    font-size: 26rpx;
  }
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 30rpx;
  margin-bottom: 30rpx;
  
  .user-avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
  }
  
  .profile-info {
    flex: 1;
    
    .user-nickname {
      display: block;
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 10rpx;
    }
    
    .user-id, .register-date, .last-login {
      display: block;
      font-size: 26rpx;
      color: #666;
      margin-bottom: 8rpx;
    }
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300rpx, 1fr));
  gap: 30rpx;
  margin-bottom: 30rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  
  &.full-width {
    grid-column: 1 / -1;
  }
  
  .info-label {
    font-size: 28rpx;
    color: #666;
  }
  
  .info-value {
    font-size: 32rpx;
    color: #333;
    
    &.description {
      line-height: 1.6;
      white-space: pre-wrap;
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: 20rpx;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  border-radius: 8rpx;
  background: #f8f8f8;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    background: #f0f0f0;
  }
}

.stat-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.house-icon {
    background: #007AFF;
  }
  
  &.appointment-icon {
    background: #34C759;
  }
  
  &.favorite-icon {
    background: #FF3B30;
  }
  
  &.view-icon {
    background: #FF9500;
  }
}

.stat-content {
  .stat-value {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
  }
  
  .stat-label {
    font-size: 24rpx;
    color: #666;
  }
}

.house-list, .appointment-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.house-item {
  display: flex;
  gap: 20rpx;
  padding: 20rpx;
  border-radius: 8rpx;
  background: #f8f8f8;
  cursor: pointer;
  
  .house-cover {
    width: 120rpx;
    height: 120rpx;
    border-radius: 8rpx;
  }
  
  .house-info {
    flex: 1;
    
    .house-title {
      display: block;
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .house-price {
      display: block;
      font-size: 26rpx;
      color: #FF3B30;
      font-weight: 600;
      margin-bottom: 8rpx;
    }
    
    .house-address {
      display: block;
      font-size: 24rpx;
      color: #666;
      margin-bottom: 10rpx;
    }
    
    .house-meta {
      display: flex;
      align-items: center;
      gap: 20rpx;
      
      .publish-date {
        font-size: 22rpx;
        color: #999;
      }
    }
  }
}

.appointment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-radius: 8rpx;
  background: #f8f8f8;
  
  .appointment-info {
    flex: 1;
    
    .house-title {
      display: block;
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .appointment-time, .contact-info {
      display: block;
      font-size: 24rpx;
      color: #666;
      margin-bottom: 4rpx;
    }
  }
}

.action-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  min-width: 150rpx;
  padding: 20rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  cursor: pointer;
  
  &.disable-btn {
    background: #FF9500;
    color: #fff;
  }
  
  &.enable-btn {
    background: #34C759;
    color: #fff;
  }
  
  &.reset-pwd-btn {
    background: #8E8E93;
    color: #fff;
  }
  
  &.delete-btn {
    background: #FF3B30;
    color: #fff;
  }
}

.error-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  font-size: 32rpx;
  color: #666;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .user-profile {
    flex-direction: column;
    text-align: center;
  }
  
  .house-item {
    flex-direction: column;
  }
  
  .action-section {
    flex-direction: column;
  }
}
</style>
