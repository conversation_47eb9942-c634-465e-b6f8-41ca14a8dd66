# 🚀 快速开始指南

欢迎使用毕业租房信息平台！这个指南将帮助您快速配置和运行项目。

## 📋 前置要求

- **HBuilderX** 3.0+ 
- **微信开发者工具**
- **uniCloud 账号**
- **Node.js** (可选，用于图片配置脚本)

## ⚡ 快速启动（5分钟）

### 1. 配置图片资源（选择一种方式）

#### 方式A: 使用占位符（推荐，立即可用）
```bash
# 在项目根目录运行
node scripts/setup-images.js placeholder
```

#### 方式B: 手动下载图片
参考 `docs/manual-download-guide.md` 下载真实图片

#### 方式C: 自动下载（需要网络）
```bash
node scripts/download-images.js download
```

### 2. 配置 uniCloud

1. 在 HBuilderX 中打开项目
2. 右键 `uniCloud-aliyun` 目录
3. 选择"关联云服务空间"
4. 创建或选择已有的云服务空间

### 3. 配置 uni-id（重要）

1. **上传公共模块**
   - 右键 `uniCloud-aliyun/cloudfunctions/common` 目录
   - 选择"上传公共模块"
   - 等待上传完成

2. **检查配置文件**
   - 确保 `uniCloud-aliyun/cloudfunctions/common/uni-config-center/uni-id/config.json` 文件存在
   - 如果遇到配置问题，请参考 `docs/uni-id-setup.md`

### 4. 上传云函数

右键以下云函数目录，选择"上传并运行"：
- `user-auth` - 用户认证（⚠️ 必须先上传公共模块）
- `house-management` - 房源管理
- `favorite-management` - 收藏管理
- `appointment-management` - 预约管理
- `message-management` - 消息管理
- `file-upload` - 文件上传
- `system-management` - 系统管理

**注意：** 如果遇到 `Invalid uni-id config file` 错误，请参考 `docs/uni-id-setup.md` 解决。

### 5. 初始化数据库

在 uniCloud 控制台创建以下数据库集合：
- `uni-id-users` - 用户表（⚠️ 必需，uni-id 系统表）
- `houses` - 房源表
- `favorites` - 收藏表
- `appointments` - 预约表
- `messages` - 消息表
- `system_config` - 系统配置表
- `reports` - 举报表

**提示：** 可以使用 `uniCloud-aliyun/database/db_init.json` 文件快速初始化数据库结构。

### 6. 运行项目

在 HBuilderX 中：
1. 点击"运行" -> "运行到小程序模拟器" -> "微信开发者工具"
2. 或运行到浏览器进行调试

### 7. 测试功能

1. **测试用户注册**
   - 打开注册页面
   - 填写用户信息
   - 提交注册

2. **如果遇到错误**
   - 查看控制台错误信息
   - 参考 `docs/uni-id-setup.md` 解决配置问题
   - 检查云函数日志

## 🔧 详细配置

### 图片资源配置

#### 检查图片状态
```bash
node scripts/setup-images.js check
```

#### 生成图片清单
```bash
node scripts/setup-images.js checklist
```

#### 切换配置模式
```bash
# 本地图片模式
node scripts/setup-images.js local

# 占位符模式  
node scripts/setup-images.js placeholder

# 混合模式（推荐）
node scripts/setup-images.js hybrid
```

### 小程序配置

1. **注册小程序**
   - 在微信公众平台注册小程序
   - 获取 AppID

2. **配置 manifest.json**
   ```json
   {
     "mp-weixin": {
       "appid": "你的小程序AppID"
     }
   }
   ```

3. **配置服务器域名**
   在微信公众平台配置以下域名：
   - request合法域名：你的uniCloud域名
   - uploadFile合法域名：你的uniCloud域名
   - downloadFile合法域名：你的uniCloud域名

## 📱 功能测试

### 基础功能测试
1. **用户注册/登录**
   - 注册新用户
   - 登录验证
   - 个人信息查看

2. **房源功能**
   - 浏览房源列表
   - 查看房源详情
   - 发布新房源（需要登录）

3. **交互功能**
   - 收藏房源
   - 搜索房源
   - 预约看房

### API 测试
```javascript
// 在浏览器控制台或小程序调试器中运行
import { runAllTests } from '@/test/api-test.js'
runAllTests()
```

## 🎯 核心页面

- **首页**: `/pages/index/index` - 房源推荐和快捷入口
- **房源列表**: `/pages/house/list` - 房源浏览和筛选
- **房源详情**: `/pages/house/detail` - 房源详细信息
- **发布房源**: `/pages/house/publish` - 房源发布表单
- **编辑房源**: `/pages/house/edit` - 房源编辑表单
- **搜索页面**: `/pages/search/search` - 房源搜索
- **登录页面**: `/pages/login/login` - 用户登录
- **注册页面**: `/pages/register/register` - 用户注册
- **个人中心**: `/pages/user/profile` - 用户信息管理
- **我的发布**: `/pages/user/my-houses` - 房源管理
- **我的收藏**: `/pages/user/favorites` - 收藏管理
- **预约记录**: `/pages/user/appointments` - 预约管理
- **消息通知**: `/pages/user/messages` - 消息管理

## 🔍 常见问题

### Q: 图片无法显示？
A: 
1. 检查图片文件是否存在：`node scripts/setup-images.js check`
2. 使用占位符模式：`node scripts/setup-images.js placeholder`
3. 检查文件路径和命名是否正确

### Q: 云函数调用失败？
A:
1. 确认云函数已正确上传
2. 检查 uniCloud 服务空间配置
3. 查看云函数日志排查错误

### Q: 小程序无法预览？
A:
1. 检查 AppID 配置
2. 确认域名已添加到白名单
3. 检查网络连接

### Q: 数据库操作失败？
A:
1. 确认数据库集合已创建
2. 检查数据库权限配置
3. 查看云函数日志

### Q: 页面文件找不到？
A: 所有页面文件已创建完成，包括：
- `pages/house/edit.vue` - 房源编辑页面
- `pages/user/appointments.vue` - 预约记录页面
- `pages/user/messages.vue` - 消息通知页面

### Q: uni-id 配置错误？
A: 如果遇到 `Invalid uni-id config file` 错误：
1. 检查配置文件是否存在：`uniCloud-aliyun/cloudfunctions/common/uni-config-center/uni-id/config.json`
2. 上传公共模块：右键 `common` 目录 → 上传公共模块
3. 重新上传云函数：右键 `user-auth` → 上传并运行
4. 详细解决方案请参考：`docs/uni-id-setup.md`

### Q: globalData 错误？
A: 已修复 App.vue 中的 globalData 访问问题，请重新运行项目。

## 📚 更多资源

- **完整文档**: `README.md`
- **图片配置**: `docs/image-resources.md`
- **手动下载指南**: `docs/manual-download-guide.md`
- **数据库设计**: `docs/database-design.md`
- **API 测试**: `test/api-test.js`

## 🆘 获取帮助

如果遇到问题：
1. 查看相关文档
2. 检查控制台错误信息
3. 查看 uniCloud 云函数日志
4. 参考示例代码和测试文件

## 🎉 开始开发

配置完成后，您就可以开始开发了！

主要开发目录：
- `pages/` - 页面开发
- `components/` - 组件开发
- `utils/` - 工具函数
- `uniCloud-aliyun/cloudfunctions/` - 云函数开发

祝您开发愉快！🚀

## ✅ 项目完成状态

✅ **所有页面文件已创建完成**
✅ **所有云函数已开发完成**
✅ **数据库设计已完成**
✅ **图片配置系统已完成**
✅ **工具函数已完成**
✅ **配置文件已完成**

现在您可以直接运行项目了！
