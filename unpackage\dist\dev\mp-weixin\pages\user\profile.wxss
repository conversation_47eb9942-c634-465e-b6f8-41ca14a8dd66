
.profile-container.data-v-64c291de {
  min-height: 100vh;
  background: #f8f9fa;
}
.user-header.data-v-64c291de {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.user-info.data-v-64c291de {
  display: flex;
  align-items: center;
  flex: 1;
}
.edit-btn.data-v-64c291de {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.avatar.data-v-64c291de {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}
.user-details.data-v-64c291de {
  flex: 1;
}
.nickname.data-v-64c291de {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 10rpx;
}
.role.data-v-64c291de {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.2);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-bottom: 10rpx;
  width: -webkit-fit-content;
  width: fit-content;
}
.phone.data-v-64c291de {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}
.edit-btn.data-v-64c291de {
  padding: 20rpx;
}

/* 未登录状态样式 */
.login-section.data-v-64c291de {
  background: #fff;
}
.login-header.data-v-64c291de {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.login-content.data-v-64c291de {
  padding: 80rpx 40rpx 60rpx;
  text-align: center;
  color: #fff;
}
.default-avatar.data-v-64c291de {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-bottom: 30rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}
.login-tip.data-v-64c291de {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.login-subtitle.data-v-64c291de {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 40rpx;
}
.register-tip.data-v-64c291de {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10rpx;
}
.tip-text.data-v-64c291de {
  font-size: 26rpx;
  opacity: 0.8;
}
.register-link.data-v-64c291de {
  font-size: 26rpx;
  color: #fff;
  text-decoration: underline;
}
.login-btn.data-v-64c291de {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 32rpx;
  margin-bottom: 30rpx;
}
.login-btn.data-v-64c291de:active {
  background: rgba(255, 255, 255, 0.3);
}
.menu-section.data-v-64c291de {
  margin: 20rpx;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}
.menu-title.data-v-64c291de {
  padding: 30rpx 30rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}
.menu-list.data-v-64c291de {
  padding: 0 30rpx;
}
.menu-item.data-v-64c291de {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.menu-item.data-v-64c291de:last-child {
  border-bottom: none;
}
.menu-icon.data-v-64c291de {
  margin-right: 30rpx;
}
.menu-text.data-v-64c291de {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}
.menu-badge.data-v-64c291de {
  background: #ff4757;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
  min-width: 32rpx;
  text-align: center;
}
.logout-section.data-v-64c291de {
  margin: 40rpx 20rpx;
}
.logout-btn.data-v-64c291de {
  width: 100%;
  background: #fff;
  color: #ff4757;
  border: 2rpx solid #ff4757;
  border-radius: 20rpx;
  padding: 30rpx;
  font-size: 32rpx;
}

/* 数据统计样式 */
.stats-section.data-v-64c291de {
  background: #fff;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.stats-grid.data-v-64c291de {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}
.stats-item.data-v-64c291de {
  text-align: center;
  padding: 20rpx 10rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
}
.stats-item.data-v-64c291de:active {
  background: #e9ecef;
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.stats-number.data-v-64c291de {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #007aff;
  margin-bottom: 8rpx;
}
.stats-label.data-v-64c291de {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 功能预览样式 */
.preview-section.data-v-64c291de {
  padding: 40rpx 30rpx;
  background: #fff;
}
.preview-list.data-v-64c291de {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}
.preview-item.data-v-64c291de {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
}
.preview-icon.data-v-64c291de {
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.preview-content.data-v-64c291de {
  flex: 1;
}
.preview-title.data-v-64c291de {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.preview-desc.data-v-64c291de {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 认证状态样式 */
.verify-status.data-v-64c291de {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-left: auto;
}
.status-verified.data-v-64c291de {
  background: #f6ffed;
  color: #52c41a;
}
.status-pending.data-v-64c291de {
  background: #fff7e6;
  color: #fa8c16;
}
.status-unverified.data-v-64c291de {
  background: #f5f5f5;
  color: #999;
}

/* 工具菜单样式 */
.tools-section.data-v-64c291de {
  margin-top: 20rpx;
}

