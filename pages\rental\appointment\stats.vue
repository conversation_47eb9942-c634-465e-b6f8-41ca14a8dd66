<template>
  <view class="appointment-stats">
    <!-- 筛选条件 -->
    <view class="filter-section">
      <view class="filter-row">
        <view class="filter-item">
          <text class="filter-label">时间范围</text>
          <uni-datetime-picker 
            v-model="dateRange" 
            type="daterange"
            :clear-icon="false"
            @change="onDateRangeChange"
          />
        </view>
        <view class="filter-item">
          <text class="filter-label">预约状态</text>
          <uni-data-picker 
            v-model="appointmentStatus" 
            :localdata="statusOptions"
            @change="onStatusChange"
          />
        </view>
        <view class="filter-item">
          <button class="export-btn" @click="exportData">导出报表</button>
        </view>
      </view>
    </view>

    <!-- 概览统计 -->
    <view class="overview-stats">
      <view class="stat-card">
        <view class="stat-icon appointment-icon">
          <uni-icons type="calendar" size="24" color="#fff" />
        </view>
        <view class="stat-content">
          <text class="stat-value">{{ overviewData.totalAppointments }}</text>
          <text class="stat-label">总预约数</text>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-icon confirmed-icon">
          <uni-icons type="checkmarkempty" size="24" color="#fff" />
        </view>
        <view class="stat-content">
          <text class="stat-value">{{ overviewData.confirmedAppointments }}</text>
          <text class="stat-label">已确认</text>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-icon completed-icon">
          <uni-icons type="heart" size="24" color="#fff" />
        </view>
        <view class="stat-content">
          <text class="stat-value">{{ overviewData.completedAppointments }}</text>
          <text class="stat-label">已完成</text>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-icon rate-icon">
          <uni-icons type="star" size="24" color="#fff" />
        </view>
        <view class="stat-content">
          <text class="stat-value">{{ overviewData.completionRate }}%</text>
          <text class="stat-label">完成率</text>
        </view>
      </view>
    </view>

    <!-- 图表区域 -->
    <view class="charts-section">
      <!-- 预约趋势图 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">预约趋势</text>
          <uni-data-picker 
            v-model="trendPeriod" 
            :localdata="periodOptions"
            @change="onPeriodChange"
          />
        </view>
        <view class="chart-content">
          <qiun-data-charts 
            type="line"
            :opts="trendChartOpts"
            :chartData="trendChartData"
            :loading="chartLoading"
          />
        </view>
      </view>

      <!-- 预约状态分布 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">预约状态分布</text>
        </view>
        <view class="chart-content">
          <qiun-data-charts 
            type="pie"
            :opts="statusChartOpts"
            :chartData="statusChartData"
            :loading="chartLoading"
          />
        </view>
      </view>

      <!-- 预约时间分布 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">预约时间分布</text>
        </view>
        <view class="chart-content">
          <qiun-data-charts 
            type="column"
            :opts="timeChartOpts"
            :chartData="timeChartData"
            :loading="chartLoading"
          />
        </view>
      </view>

      <!-- 热门房源预约排行 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">热门房源预约排行</text>
        </view>
        <view class="ranking-list">
          <view 
            v-for="(house, index) in hotHouses" 
            :key="house._id"
            class="ranking-item"
            @click="viewHouse(house)"
          >
            <view class="ranking-number" :class="getRankingClass(index)">
              {{ index + 1 }}
            </view>
            <image 
              class="house-cover" 
              :src="house.images && house.images[0] || '/static/placeholder.png'"
              mode="aspectFill"
            />
            <view class="house-info">
              <text class="house-title">{{ house.title }}</text>
              <text class="house-price">¥{{ house.price }}/月</text>
              <text class="appointment-count">预约 {{ house.appointment_count }} 次</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 预约转化漏斗 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">预约转化漏斗</text>
        </view>
        <view class="funnel-chart">
          <view class="funnel-item">
            <view class="funnel-bar" style="width: 100%; background: #007AFF;">
              <text class="funnel-label">总预约</text>
              <text class="funnel-value">{{ funnelData.total }}</text>
            </view>
          </view>
          <view class="funnel-item">
            <view class="funnel-bar" :style="`width: ${funnelData.confirmedRate}%; background: #34C759;`">
              <text class="funnel-label">已确认</text>
              <text class="funnel-value">{{ funnelData.confirmed }}</text>
            </view>
          </view>
          <view class="funnel-item">
            <view class="funnel-bar" :style="`width: ${funnelData.completedRate}%; background: #FF9500;`">
              <text class="funnel-label">已完成</text>
              <text class="funnel-value">{{ funnelData.completed }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 详细数据表格 -->
    <view class="data-table-section">
      <view class="section-header">
        <text class="section-title">详细统计数据</text>
      </view>
      
      <uni-table 
        :loading="tableLoading"
        border
        stripe
        emptyText="暂无数据"
      >
        <uni-tr>
          <uni-th align="center">日期</uni-th>
          <uni-th align="center">新增预约</uni-th>
          <uni-th align="center">确认预约</uni-th>
          <uni-th align="center">完成预约</uni-th>
          <uni-th align="center">取消预约</uni-th>
          <uni-th align="center">完成率</uni-th>
        </uni-tr>
        <uni-tr v-for="(item, index) in tableData" :key="index">
          <uni-td align="center">{{ item.date }}</uni-td>
          <uni-td align="center">{{ item.newAppointments }}</uni-td>
          <uni-td align="center">{{ item.confirmedAppointments }}</uni-td>
          <uni-td align="center">{{ item.completedAppointments }}</uni-td>
          <uni-td align="center">{{ item.cancelledAppointments }}</uni-td>
          <uni-td align="center">{{ item.completionRate }}%</uni-td>
        </uni-tr>
      </uni-table>
      
      <!-- 分页 -->
      <view class="pagination">
        <uni-pagination 
          :current="currentPage"
          :total="totalCount"
          :pageSize="pageSize"
          @change="onPageChange"
        />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'AppointmentStats',
  data() {
    return {
      chartLoading: true,
      tableLoading: true,
      
      // 筛选条件
      dateRange: [],
      appointmentStatus: '',
      trendPeriod: 'month',
      
      // 筛选选项
      statusOptions: [
        { value: '', text: '全部状态' },
        { value: 'pending', text: '待确认' },
        { value: 'confirmed', text: '已确认' },
        { value: 'completed', text: '已完成' },
        { value: 'cancelled', text: '已取消' }
      ],
      
      periodOptions: [
        { value: 'week', text: '最近7天' },
        { value: 'month', text: '最近30天' },
        { value: 'quarter', text: '最近3个月' }
      ],

      // 概览数据
      overviewData: {
        totalAppointments: 0,
        confirmedAppointments: 0,
        completedAppointments: 0,
        completionRate: 0
      },

      // 图表配置
      trendChartOpts: {
        color: ['#007AFF', '#34C759', '#FF9500', '#FF3B30'],
        padding: [15, 15, 0, 15],
        enableScroll: false,
        legend: {
          show: true
        },
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2
        },
        extra: {
          line: {
            type: 'curve',
            width: 2
          }
        }
      },

      statusChartOpts: {
        color: ['#FF9500', '#007AFF', '#34C759', '#FF3B30'],
        padding: [5, 5, 5, 5],
        enableScroll: false,
        legend: {
          show: true,
          position: 'right'
        },
        extra: {
          pie: {
            activeOpacity: 0.5,
            activeRadius: 10,
            offsetAngle: 0,
            labelWidth: 15,
            border: true,
            borderWidth: 3,
            borderColor: '#FFFFFF'
          }
        }
      },

      timeChartOpts: {
        color: ['#007AFF'],
        padding: [15, 15, 0, 15],
        enableScroll: false,
        legend: {
          show: false
        },
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2
        }
      },

      // 图表数据
      trendChartData: {},
      statusChartData: {},
      timeChartData: {},

      // 热门房源
      hotHouses: [],

      // 漏斗数据
      funnelData: {
        total: 0,
        confirmed: 0,
        completed: 0,
        confirmedRate: 0,
        completedRate: 0
      },

      // 表格数据
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0
    }
  },

  onLoad() {
    this.initDateRange()
    this.loadStatsData()
  },

  methods: {
    // 初始化日期范围
    initDateRange() {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 30)
      
      this.dateRange = [
        start.toISOString().split('T')[0],
        end.toISOString().split('T')[0]
      ]
    },

    // 加载统计数据
    async loadStatsData() {
      try {
        this.chartLoading = true
        this.tableLoading = true

        // 并发加载所有数据
        await Promise.all([
          this.loadOverviewData(),
          this.loadChartData(),
          this.loadHotHouses(),
          this.loadFunnelData(),
          this.loadTableData()
        ])

        this.chartLoading = false
        this.tableLoading = false
      } catch (error) {
        console.error('加载统计数据失败:', error)
        uni.showToast({
          title: '加载数据失败',
          icon: 'none'
        })
        this.chartLoading = false
        this.tableLoading = false
      }
    },

    // 加载概览数据
    async loadOverviewData() {
      try {
        const result = await uniCloud.callFunction({
          name: 'system-management',
          data: {
            action: 'getAppointmentOverview',
            dateRange: this.dateRange,
            appointmentStatus: this.appointmentStatus
          }
        })

        if (result.result.code === 0) {
          this.overviewData = result.result.data
        }
      } catch (error) {
        console.error('加载概览数据失败:', error)
      }
    },

    // 加载图表数据
    async loadChartData() {
      // 模拟数据，实际应该调用云函数
      this.trendChartData = {
        categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
        series: [
          {
            name: '新增预约',
            data: [15, 18, 22, 25, 28, 32]
          },
          {
            name: '确认预约',
            data: [12, 15, 18, 20, 22, 25]
          },
          {
            name: '完成预约',
            data: [10, 12, 15, 17, 19, 22]
          },
          {
            name: '取消预约',
            data: [3, 3, 4, 5, 6, 7]
          }
        ]
      }

      this.statusChartData = {
        series: [{
          name: '预约状态',
          data: [
            { name: '待确认', value: 25 },
            { name: '已确认', value: 35 },
            { name: '已完成', value: 30 },
            { name: '已取消', value: 10 }
          ]
        }]
      }

      this.timeChartData = {
        categories: ['9:00', '10:00', '11:00', '14:00', '15:00', '16:00', '17:00', '18:00'],
        series: [{
          name: '预约数量',
          data: [8, 12, 15, 18, 22, 20, 16, 10]
        }]
      }
    },

    // 加载热门房源
    async loadHotHouses() {
      // 模拟数据
      this.hotHouses = [
        {
          _id: '1',
          title: '阳光小区三室一厅',
          price: 3500,
          appointment_count: 25,
          images: ['/static/house1.jpg']
        },
        {
          _id: '2',
          title: '市中心精装公寓',
          price: 4200,
          appointment_count: 22,
          images: ['/static/house2.jpg']
        },
        {
          _id: '3',
          title: '学区房两室一厅',
          price: 2800,
          appointment_count: 18,
          images: ['/static/house3.jpg']
        }
      ]
    },

    // 加载漏斗数据
    async loadFunnelData() {
      // 模拟数据
      this.funnelData = {
        total: 150,
        confirmed: 120,
        completed: 95,
        confirmedRate: 80,
        completedRate: 63
      }
    },

    // 加载表格数据
    async loadTableData() {
      // 模拟数据
      this.tableData = [
        {
          date: '2024-01-01',
          newAppointments: 8,
          confirmedAppointments: 6,
          completedAppointments: 5,
          cancelledAppointments: 1,
          completionRate: 83
        },
        {
          date: '2024-01-02',
          newAppointments: 6,
          confirmedAppointments: 5,
          completedAppointments: 4,
          cancelledAppointments: 1,
          completionRate: 80
        }
      ]
      this.totalCount = 100
    },

    // 日期范围改变
    onDateRangeChange() {
      this.loadStatsData()
    },

    // 预约状态改变
    onStatusChange() {
      this.loadStatsData()
    },

    // 趋势周期改变
    onPeriodChange() {
      this.loadChartData()
    },

    // 查看房源
    viewHouse(house) {
      uni.navigateTo({
        url: `/pages/rental/house/detail?id=${house._id}`
      })
    },

    // 获取排名样式
    getRankingClass(index) {
      if (index === 0) return 'first'
      if (index === 1) return 'second'
      if (index === 2) return 'third'
      return 'normal'
    },

    // 分页改变
    onPageChange(page) {
      this.currentPage = page
      this.loadTableData()
    },

    // 导出数据
    exportData() {
      uni.showToast({
        title: '导出功能开发中',
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.appointment-stats {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.filter-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 40rpx;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  
  .filter-label {
    font-size: 28rpx;
    color: #333;
    white-space: nowrap;
  }
  
  .export-btn {
    background: #007AFF;
    color: #fff;
    border: none;
    border-radius: 8rpx;
    padding: 16rpx 32rpx;
    font-size: 28rpx;
  }
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stat-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.appointment-icon {
    background: #007AFF;
  }
  
  &.confirmed-icon {
    background: #34C759;
  }
  
  &.completed-icon {
    background: #FF9500;
  }
  
  &.rate-icon {
    background: #FF3B30;
  }
}

.stat-content {
  .stat-value {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
  }
  
  .stat-label {
    font-size: 24rpx;
    color: #666;
  }
}

.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500rpx, 1fr));
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.chart-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  
  .chart-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
}

.chart-content {
  height: 400rpx;
}

.ranking-list {
  .ranking-item {
    display: flex;
    align-items: center;
    gap: 20rpx;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    cursor: pointer;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:hover {
      background: #f8f8f8;
    }
  }
  
  .ranking-number {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    font-weight: 600;
    color: #fff;
    
    &.first {
      background: #FFD700;
    }
    
    &.second {
      background: #C0C0C0;
    }
    
    &.third {
      background: #CD7F32;
    }
    
    &.normal {
      background: #8E8E93;
    }
  }
  
  .house-cover {
    width: 80rpx;
    height: 80rpx;
    border-radius: 8rpx;
  }
  
  .house-info {
    flex: 1;
    
    .house-title {
      display: block;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 8rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .house-price {
      display: block;
      font-size: 24rpx;
      color: #FF3B30;
      font-weight: 600;
      margin-bottom: 4rpx;
    }
    
    .appointment-count {
      font-size: 22rpx;
      color: #999;
    }
  }
}

.funnel-chart {
  .funnel-item {
    margin-bottom: 20rpx;
    
    .funnel-bar {
      height: 60rpx;
      border-radius: 8rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20rpx;
      color: #fff;
      font-weight: 600;
      
      .funnel-label {
        font-size: 28rpx;
      }
      
      .funnel-value {
        font-size: 32rpx;
      }
    }
  }
}

.data-table-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  margin-bottom: 30rpx;
  
  .section-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
}

.pagination {
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .overview-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .charts-section {
    grid-template-columns: 1fr;
  }
}
</style>
