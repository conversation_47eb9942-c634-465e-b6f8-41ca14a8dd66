
.login-container.data-v-b237504c {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  display: flex;
  flex-direction: column;
}
.header.data-v-b237504c {
  text-align: center;
  margin-bottom: 80rpx;
}
.logo.data-v-b237504c {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}
.title.data-v-b237504c {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 10rpx;
}
.subtitle.data-v-b237504c {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}
.form-container.data-v-b237504c {
  flex: 1;
}
.form-item.data-v-b237504c {
  margin-bottom: 40rpx;
}
.input-wrapper.data-v-b237504c {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  height: 100rpx;
}
.input.data-v-b237504c {
  flex: 1;
  margin-left: 20rpx;
  font-size: 32rpx;
  color: #333;
}
.login-btn.data-v-b237504c {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: #fff;
  border: none;
  border-radius: 50rpx;
  font-size: 36rpx;
  font-weight: bold;
  margin-top: 40rpx;
  margin-bottom: 40rpx;
}
.login-btn.data-v-b237504c:disabled {
  background: #ccc;
}
.links.data-v-b237504c {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}
.link.data-v-b237504c {
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
}
.footer.data-v-b237504c {
  text-align: center;
  margin-top: auto;
  padding-top: 40rpx;
}
.footer-text.data-v-b237504c {
  color: rgba(255, 255, 255, 0.7);
  font-size: 24rpx;
}
.footer-link.data-v-b237504c {
  color: rgba(255, 255, 255, 0.9);
  font-size: 24rpx;
}

