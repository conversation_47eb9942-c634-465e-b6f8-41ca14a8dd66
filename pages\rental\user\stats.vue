<template>
  <view class="user-stats">
    <!-- 筛选条件 -->
    <view class="filter-section">
      <view class="filter-row">
        <view class="filter-item">
          <text class="filter-label">时间范围</text>
          <uni-datetime-picker 
            v-model="dateRange" 
            type="daterange"
            :clear-icon="false"
            @change="onDateRangeChange"
          />
        </view>
        <view class="filter-item">
          <text class="filter-label">用户状态</text>
          <uni-data-picker 
            v-model="userStatus" 
            :localdata="statusOptions"
            @change="onStatusChange"
          />
        </view>
        <view class="filter-item">
          <button class="export-btn" @click="exportData">导出报表</button>
        </view>
      </view>
    </view>

    <!-- 概览统计 -->
    <view class="overview-stats">
      <view class="stat-card">
        <view class="stat-icon user-icon">
          <uni-icons type="person" size="24" color="#fff" />
        </view>
        <view class="stat-content">
          <text class="stat-value">{{ overviewData.totalUsers }}</text>
          <text class="stat-label">总用户数</text>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-icon active-icon">
          <uni-icons type="checkmarkempty" size="24" color="#fff" />
        </view>
        <view class="stat-content">
          <text class="stat-value">{{ overviewData.activeUsers }}</text>
          <text class="stat-label">活跃用户</text>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-icon new-icon">
          <uni-icons type="plus" size="24" color="#fff" />
        </view>
        <view class="stat-content">
          <text class="stat-value">{{ overviewData.newUsers }}</text>
          <text class="stat-label">新增用户</text>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-icon retention-icon">
          <uni-icons type="heart" size="24" color="#fff" />
        </view>
        <view class="stat-content">
          <text class="stat-value">{{ overviewData.retentionRate }}%</text>
          <text class="stat-label">留存率</text>
        </view>
      </view>
    </view>

    <!-- 图表区域 -->
    <view class="charts-section">
      <!-- 用户注册趋势 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">用户注册趋势</text>
          <uni-data-picker 
            v-model="trendPeriod" 
            :localdata="periodOptions"
            @change="onPeriodChange"
          />
        </view>
        <view class="chart-content">
          <qiun-data-charts 
            type="line"
            :opts="trendChartOpts"
            :chartData="trendChartData"
            :loading="chartLoading"
          />
        </view>
      </view>

      <!-- 用户活跃度分布 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">用户活跃度分布</text>
        </view>
        <view class="chart-content">
          <qiun-data-charts 
            type="pie"
            :opts="activityChartOpts"
            :chartData="activityChartData"
            :loading="chartLoading"
          />
        </view>
      </view>

      <!-- 用户年龄分布 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">用户年龄分布</text>
        </view>
        <view class="chart-content">
          <qiun-data-charts 
            type="column"
            :opts="ageChartOpts"
            :chartData="ageChartData"
            :loading="chartLoading"
          />
        </view>
      </view>

      <!-- 用户地区分布 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">用户地区分布</text>
        </view>
        <view class="chart-content">
          <qiun-data-charts 
            type="bar"
            :opts="regionChartOpts"
            :chartData="regionChartData"
            :loading="chartLoading"
          />
        </view>
      </view>

      <!-- 活跃用户排行 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">活跃用户排行</text>
        </view>
        <view class="ranking-list">
          <view 
            v-for="(user, index) in activeUsers" 
            :key="user._id"
            class="ranking-item"
            @click="viewUser(user)"
          >
            <view class="ranking-number" :class="getRankingClass(index)">
              {{ index + 1 }}
            </view>
            <image 
              class="user-avatar" 
              :src="user.avatar || '/static/default-avatar.png'"
              mode="aspectFill"
            />
            <view class="user-info">
              <text class="user-nickname">{{ user.nickname || '未设置' }}</text>
              <text class="user-activity">活跃度: {{ user.activity_score }}</text>
              <text class="user-houses">发布房源: {{ user.house_count }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 详细数据表格 -->
    <view class="data-table-section">
      <view class="section-header">
        <text class="section-title">详细统计数据</text>
      </view>
      
      <uni-table 
        :loading="tableLoading"
        border
        stripe
        emptyText="暂无数据"
      >
        <uni-tr>
          <uni-th align="center">日期</uni-th>
          <uni-th align="center">新增用户</uni-th>
          <uni-th align="center">活跃用户</uni-th>
          <uni-th align="center">留存用户</uni-th>
          <uni-th align="center">发布房源</uni-th>
          <uni-th align="center">预约次数</uni-th>
        </uni-tr>
        <uni-tr v-for="(item, index) in tableData" :key="index">
          <uni-td align="center">{{ item.date }}</uni-td>
          <uni-td align="center">{{ item.newUsers }}</uni-td>
          <uni-td align="center">{{ item.activeUsers }}</uni-td>
          <uni-td align="center">{{ item.retentionUsers }}</uni-td>
          <uni-td align="center">{{ item.publishedHouses }}</uni-td>
          <uni-td align="center">{{ item.appointments }}</uni-td>
        </uni-tr>
      </uni-table>
      
      <!-- 分页 -->
      <view class="pagination">
        <uni-pagination 
          :current="currentPage"
          :total="totalCount"
          :pageSize="pageSize"
          @change="onPageChange"
        />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'UserStats',
  data() {
    return {
      chartLoading: true,
      tableLoading: true,
      
      // 筛选条件
      dateRange: [],
      userStatus: '',
      trendPeriod: 'month',
      
      // 筛选选项
      statusOptions: [
        { value: '', text: '全部状态' },
        { value: 'active', text: '正常' },
        { value: 'disabled', text: '禁用' }
      ],
      
      periodOptions: [
        { value: 'week', text: '最近7天' },
        { value: 'month', text: '最近30天' },
        { value: 'quarter', text: '最近3个月' }
      ],

      // 概览数据
      overviewData: {
        totalUsers: 0,
        activeUsers: 0,
        newUsers: 0,
        retentionRate: 0
      },

      // 图表配置
      trendChartOpts: {
        color: ['#007AFF', '#34C759'],
        padding: [15, 15, 0, 15],
        enableScroll: false,
        legend: {
          show: true
        },
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2
        },
        extra: {
          line: {
            type: 'curve',
            width: 2
          }
        }
      },

      activityChartOpts: {
        color: ['#007AFF', '#34C759', '#FF9500', '#FF3B30'],
        padding: [5, 5, 5, 5],
        enableScroll: false,
        legend: {
          show: true,
          position: 'right'
        },
        extra: {
          pie: {
            activeOpacity: 0.5,
            activeRadius: 10,
            offsetAngle: 0,
            labelWidth: 15,
            border: true,
            borderWidth: 3,
            borderColor: '#FFFFFF'
          }
        }
      },

      ageChartOpts: {
        color: ['#FF9500'],
        padding: [15, 15, 0, 15],
        enableScroll: false,
        legend: {
          show: false
        },
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2
        }
      },

      regionChartOpts: {
        color: ['#34C759'],
        padding: [15, 15, 0, 15],
        enableScroll: false,
        legend: {
          show: false
        },
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2
        }
      },

      // 图表数据
      trendChartData: {},
      activityChartData: {},
      ageChartData: {},
      regionChartData: {},

      // 活跃用户
      activeUsers: [],

      // 表格数据
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0
    }
  },

  onLoad() {
    this.initDateRange()
    this.loadStatsData()
  },

  methods: {
    // 初始化日期范围
    initDateRange() {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 30)
      
      this.dateRange = [
        start.toISOString().split('T')[0],
        end.toISOString().split('T')[0]
      ]
    },

    // 加载统计数据
    async loadStatsData() {
      try {
        this.chartLoading = true
        this.tableLoading = true

        // 并发加载所有数据
        await Promise.all([
          this.loadOverviewData(),
          this.loadChartData(),
          this.loadActiveUsers(),
          this.loadTableData()
        ])

        this.chartLoading = false
        this.tableLoading = false
      } catch (error) {
        console.error('加载统计数据失败:', error)
        uni.showToast({
          title: '加载数据失败',
          icon: 'none'
        })
        this.chartLoading = false
        this.tableLoading = false
      }
    },

    // 加载概览数据
    async loadOverviewData() {
      try {
        const result = await uniCloud.callFunction({
          name: 'system-management',
          data: {
            action: 'getUserOverview',
            dateRange: this.dateRange,
            userStatus: this.userStatus
          }
        })

        if (result.result.code === 0) {
          this.overviewData = result.result.data
        }
      } catch (error) {
        console.error('加载概览数据失败:', error)
      }
    },

    // 加载图表数据
    async loadChartData() {
      // 模拟数据，实际应该调用云函数
      this.trendChartData = {
        categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
        series: [
          {
            name: '新增用户',
            data: [25, 28, 31, 33, 35, 38]
          },
          {
            name: '活跃用户',
            data: [120, 125, 130, 135, 140, 145]
          }
        ]
      }

      this.activityChartData = {
        series: [{
          name: '活跃度',
          data: [
            { name: '高活跃', value: 35 },
            { name: '中活跃', value: 40 },
            { name: '低活跃', value: 20 },
            { name: '不活跃', value: 5 }
          ]
        }]
      }

      this.ageChartData = {
        categories: ['18-22岁', '23-27岁', '28-32岁', '33-37岁', '38岁以上'],
        series: [{
          name: '用户数量',
          data: [45, 65, 35, 25, 15]
        }]
      }

      this.regionChartData = {
        categories: ['北京', '上海', '广州', '深圳', '杭州'],
        series: [{
          name: '用户数量',
          data: [85, 78, 65, 72, 58]
        }]
      }
    },

    // 加载活跃用户
    async loadActiveUsers() {
      // 模拟数据
      this.activeUsers = [
        {
          _id: '1',
          nickname: '张同学',
          avatar: '/static/avatar1.jpg',
          activity_score: 95,
          house_count: 5
        },
        {
          _id: '2',
          nickname: '李老师',
          avatar: '/static/avatar2.jpg',
          activity_score: 88,
          house_count: 3
        },
        {
          _id: '3',
          nickname: '王先生',
          avatar: '/static/avatar3.jpg',
          activity_score: 82,
          house_count: 4
        }
      ]
    },

    // 加载表格数据
    async loadTableData() {
      // 模拟数据
      this.tableData = [
        {
          date: '2024-01-01',
          newUsers: 15,
          activeUsers: 125,
          retentionUsers: 98,
          publishedHouses: 8,
          appointments: 25
        },
        {
          date: '2024-01-02',
          newUsers: 12,
          activeUsers: 128,
          retentionUsers: 102,
          publishedHouses: 6,
          appointments: 22
        }
      ]
      this.totalCount = 100
    },

    // 日期范围改变
    onDateRangeChange() {
      this.loadStatsData()
    },

    // 用户状态改变
    onStatusChange() {
      this.loadStatsData()
    },

    // 趋势周期改变
    onPeriodChange() {
      this.loadChartData()
    },

    // 查看用户
    viewUser(user) {
      uni.navigateTo({
        url: `/pages/rental/user/detail?id=${user._id}`
      })
    },

    // 获取排名样式
    getRankingClass(index) {
      if (index === 0) return 'first'
      if (index === 1) return 'second'
      if (index === 2) return 'third'
      return 'normal'
    },

    // 分页改变
    onPageChange(page) {
      this.currentPage = page
      this.loadTableData()
    },

    // 导出数据
    exportData() {
      uni.showToast({
        title: '导出功能开发中',
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.user-stats {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.filter-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 40rpx;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  
  .filter-label {
    font-size: 28rpx;
    color: #333;
    white-space: nowrap;
  }
  
  .export-btn {
    background: #007AFF;
    color: #fff;
    border: none;
    border-radius: 8rpx;
    padding: 16rpx 32rpx;
    font-size: 28rpx;
  }
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stat-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.user-icon {
    background: #007AFF;
  }
  
  &.active-icon {
    background: #34C759;
  }
  
  &.new-icon {
    background: #FF9500;
  }
  
  &.retention-icon {
    background: #FF3B30;
  }
}

.stat-content {
  .stat-value {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
  }
  
  .stat-label {
    font-size: 24rpx;
    color: #666;
  }
}

.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500rpx, 1fr));
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.chart-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  
  .chart-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
}

.chart-content {
  height: 400rpx;
}

.ranking-list {
  .ranking-item {
    display: flex;
    align-items: center;
    gap: 20rpx;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    cursor: pointer;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:hover {
      background: #f8f8f8;
    }
  }
  
  .ranking-number {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    font-weight: 600;
    color: #fff;
    
    &.first {
      background: #FFD700;
    }
    
    &.second {
      background: #C0C0C0;
    }
    
    &.third {
      background: #CD7F32;
    }
    
    &.normal {
      background: #8E8E93;
    }
  }
  
  .user-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
  }
  
  .user-info {
    flex: 1;
    
    .user-nickname {
      display: block;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .user-activity {
      display: block;
      font-size: 24rpx;
      color: #007AFF;
      font-weight: 600;
      margin-bottom: 4rpx;
    }
    
    .user-houses {
      font-size: 22rpx;
      color: #999;
    }
  }
}

.data-table-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  margin-bottom: 30rpx;
  
  .section-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
}

.pagination {
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .overview-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .charts-section {
    grid-template-columns: 1fr;
  }
}
</style>
