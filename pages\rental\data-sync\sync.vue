<template>
  <view class="data-sync">
    <view class="header">
      <text class="title">数据同步工具</text>
      <text class="subtitle">用于初始化后端管理系统的测试数据</text>
    </view>

    <view class="sync-section">
      <view class="sync-card">
        <view class="card-header">
          <text class="card-title">用户数据同步</text>
          <text class="card-desc">同步用户信息到后端数据库</text>
        </view>
        <view class="card-actions">
          <button 
            class="sync-btn" 
            :loading="userLoading"
            @click="syncUsers"
          >
            {{ userLoading ? '同步中...' : '同步用户数据' }}
          </button>
        </view>
        <view class="sync-result" v-if="userResult">
          <text :class="userResult.code === 0 ? 'success' : 'error'">
            {{ userResult.message }}
          </text>
          <text v-if="userResult.data" class="count">
            同步数量: {{ userResult.data.count || userResult.data.users }}
          </text>
        </view>
      </view>

      <view class="sync-card">
        <view class="card-header">
          <text class="card-title">房源数据同步</text>
          <text class="card-desc">同步房源信息到后端数据库</text>
        </view>
        <view class="card-actions">
          <button 
            class="sync-btn" 
            :loading="houseLoading"
            @click="syncHouses"
          >
            {{ houseLoading ? '同步中...' : '同步房源数据' }}
          </button>
        </view>
        <view class="sync-result" v-if="houseResult">
          <text :class="houseResult.code === 0 ? 'success' : 'error'">
            {{ houseResult.message }}
          </text>
          <text v-if="houseResult.data" class="count">
            同步数量: {{ houseResult.data.count || houseResult.data.houses }}
          </text>
        </view>
      </view>

      <view class="sync-card">
        <view class="card-header">
          <text class="card-title">预约数据同步</text>
          <text class="card-desc">同步预约信息到后端数据库</text>
        </view>
        <view class="card-actions">
          <button 
            class="sync-btn" 
            :loading="appointmentLoading"
            @click="syncAppointments"
          >
            {{ appointmentLoading ? '同步中...' : '同步预约数据' }}
          </button>
        </view>
        <view class="sync-result" v-if="appointmentResult">
          <text :class="appointmentResult.code === 0 ? 'success' : 'error'">
            {{ appointmentResult.message }}
          </text>
          <text v-if="appointmentResult.data" class="count">
            同步数量: {{ appointmentResult.data.count || appointmentResult.data.appointments }}
          </text>
        </view>
      </view>

      <view class="sync-card highlight">
        <view class="card-header">
          <text class="card-title">一键同步所有数据</text>
          <text class="card-desc">同步所有数据到后端数据库（推荐）</text>
        </view>
        <view class="card-actions">
          <button 
            class="sync-btn primary" 
            :loading="allLoading"
            @click="syncAll"
          >
            {{ allLoading ? '同步中...' : '一键同步所有数据' }}
          </button>
        </view>
        <view class="sync-result" v-if="allResult">
          <text :class="allResult.code === 0 ? 'success' : 'error'">
            {{ allResult.message }}
          </text>
          <view v-if="allResult.data" class="count-detail">
            <text class="count">用户: {{ allResult.data.users }}</text>
            <text class="count">房源: {{ allResult.data.houses }}</text>
            <text class="count">预约: {{ allResult.data.appointments }}</text>
          </view>
        </view>
      </view>

      <view class="sync-card danger">
        <view class="card-header">
          <text class="card-title">重置并初始化数据</text>
          <text class="card-desc">清空现有数据并重新初始化（谨慎操作）</text>
        </view>
        <view class="card-actions">
          <button 
            class="sync-btn danger" 
            :loading="initLoading"
            @click="initTestData"
          >
            {{ initLoading ? '初始化中...' : '重置并初始化数据' }}
          </button>
        </view>
        <view class="sync-result" v-if="initResult">
          <text :class="initResult.code === 0 ? 'success' : 'error'">
            {{ initResult.message }}
          </text>
          <view v-if="initResult.data" class="count-detail">
            <text class="count">用户: {{ initResult.data.users }}</text>
            <text class="count">房源: {{ initResult.data.houses }}</text>
            <text class="count">预约: {{ initResult.data.appointments }}</text>
          </view>
        </view>
      </view>
    </view>

    <view class="tips">
      <view class="tip-item">
        <text class="tip-title">使用说明：</text>
      </view>
      <view class="tip-item">
        <text class="tip-text">1. 首次使用建议点击"一键同步所有数据"</text>
      </view>
      <view class="tip-item">
        <text class="tip-text">2. 如果数据有问题，可以使用"重置并初始化数据"</text>
      </view>
      <view class="tip-item">
        <text class="tip-text">3. 同步完成后可以在各个管理页面查看数据</text>
      </view>
      <view class="tip-item">
        <text class="tip-text">4. 这些是测试数据，实际使用时请替换为真实数据</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'DataSync',
  data() {
    return {
      userLoading: false,
      houseLoading: false,
      appointmentLoading: false,
      allLoading: false,
      initLoading: false,
      
      userResult: null,
      houseResult: null,
      appointmentResult: null,
      allResult: null,
      initResult: null
    }
  },

  methods: {
    // 同步用户数据
    async syncUsers() {
      try {
        this.userLoading = true
        this.userResult = null
        
        const result = await uniCloud.callFunction({
          name: 'data-sync',
          data: {
            action: 'syncUsers'
          }
        })

        this.userResult = result.result
        
        if (result.result.code === 0) {
          uni.showToast({
            title: '用户数据同步成功',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: result.result.message,
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('同步用户数据失败:', error)
        this.userResult = {
          code: 500,
          message: '同步失败: ' + error.message
        }
        uni.showToast({
          title: '同步失败',
          icon: 'none'
        })
      } finally {
        this.userLoading = false
      }
    },

    // 同步房源数据
    async syncHouses() {
      try {
        this.houseLoading = true
        this.houseResult = null
        
        const result = await uniCloud.callFunction({
          name: 'data-sync',
          data: {
            action: 'syncHouses'
          }
        })

        this.houseResult = result.result
        
        if (result.result.code === 0) {
          uni.showToast({
            title: '房源数据同步成功',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: result.result.message,
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('同步房源数据失败:', error)
        this.houseResult = {
          code: 500,
          message: '同步失败: ' + error.message
        }
        uni.showToast({
          title: '同步失败',
          icon: 'none'
        })
      } finally {
        this.houseLoading = false
      }
    },

    // 同步预约数据
    async syncAppointments() {
      try {
        this.appointmentLoading = true
        this.appointmentResult = null
        
        const result = await uniCloud.callFunction({
          name: 'data-sync',
          data: {
            action: 'syncAppointments'
          }
        })

        this.appointmentResult = result.result
        
        if (result.result.code === 0) {
          uni.showToast({
            title: '预约数据同步成功',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: result.result.message,
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('同步预约数据失败:', error)
        this.appointmentResult = {
          code: 500,
          message: '同步失败: ' + error.message
        }
        uni.showToast({
          title: '同步失败',
          icon: 'none'
        })
      } finally {
        this.appointmentLoading = false
      }
    },

    // 同步所有数据
    async syncAll() {
      try {
        this.allLoading = true
        this.allResult = null
        
        const result = await uniCloud.callFunction({
          name: 'data-sync',
          data: {
            action: 'syncAll'
          }
        })

        this.allResult = result.result
        
        if (result.result.code === 0) {
          uni.showToast({
            title: '所有数据同步成功',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: result.result.message,
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('同步所有数据失败:', error)
        this.allResult = {
          code: 500,
          message: '同步失败: ' + error.message
        }
        uni.showToast({
          title: '同步失败',
          icon: 'none'
        })
      } finally {
        this.allLoading = false
      }
    },

    // 初始化测试数据
    async initTestData() {
      uni.showModal({
        title: '确认操作',
        content: '此操作将清空现有数据并重新初始化，确定要继续吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              this.initLoading = true
              this.initResult = null
              
              const result = await uniCloud.callFunction({
                name: 'data-sync',
                data: {
                  action: 'initTestData'
                }
              })

              this.initResult = result.result
              
              if (result.result.code === 0) {
                uni.showToast({
                  title: '数据初始化成功',
                  icon: 'success'
                })
              } else {
                uni.showToast({
                  title: result.result.message,
                  icon: 'none'
                })
              }
            } catch (error) {
              console.error('初始化数据失败:', error)
              this.initResult = {
                code: 500,
                message: '初始化失败: ' + error.message
              }
              uni.showToast({
                title: '初始化失败',
                icon: 'none'
              })
            } finally {
              this.initLoading = false
            }
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.data-sync {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  
  .title {
    display: block;
    font-size: 48rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
    color: #666;
  }
}

.sync-section {
  display: grid;
  gap: 30rpx;
  margin-bottom: 40rpx;
}

.sync-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  
  &.highlight {
    border: 2rpx solid #007AFF;
    background: linear-gradient(135deg, #f8fbff 0%, #fff 100%);
  }
  
  &.danger {
    border: 2rpx solid #FF3B30;
    background: linear-gradient(135deg, #fff8f8 0%, #fff 100%);
  }
}

.card-header {
  margin-bottom: 30rpx;
  
  .card-title {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 12rpx;
  }
  
  .card-desc {
    font-size: 28rpx;
    color: #666;
  }
}

.card-actions {
  margin-bottom: 20rpx;
}

.sync-btn {
  width: 100%;
  padding: 24rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  background: #8E8E93;
  color: #fff;
  
  &.primary {
    background: #007AFF;
  }
  
  &.danger {
    background: #FF3B30;
  }
  
  &[loading] {
    opacity: 0.7;
  }
}

.sync-result {
  padding: 20rpx;
  border-radius: 8rpx;
  background: #f8f8f8;
  
  .success {
    color: #34C759;
    font-weight: 600;
  }
  
  .error {
    color: #FF3B30;
    font-weight: 600;
  }
  
  .count {
    display: block;
    font-size: 24rpx;
    color: #666;
    margin-top: 8rpx;
  }
  
  .count-detail {
    margin-top: 12rpx;
    display: flex;
    gap: 20rpx;
    
    .count {
      margin-top: 0;
    }
  }
}

.tips {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.tip-item {
  margin-bottom: 16rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.tip-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.tip-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 响应式设计 */
@media screen and (min-width: 768px) {
  .sync-section {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .sync-card.highlight,
  .sync-card.danger {
    grid-column: 1 / -1;
  }
}
</style>
