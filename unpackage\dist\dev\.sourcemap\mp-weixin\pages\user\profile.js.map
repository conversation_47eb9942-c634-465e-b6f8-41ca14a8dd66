{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端/pages/user/profile.vue?c17b", "webpack:///D:/web/project/前端/pages/user/profile.vue?02f8", "webpack:///D:/web/project/前端/pages/user/profile.vue?8f9d", "webpack:///D:/web/project/前端/pages/user/profile.vue?65f4", "uni-app:///pages/user/profile.vue", "webpack:///D:/web/project/前端/pages/user/profile.vue?7f6e", "webpack:///D:/web/project/前端/pages/user/profile.vue?08a7"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userInfo", "myHousesCount", "favoritesCount", "appointmentsCount", "unreadCount", "methods", "getRoleText", "loadUserInfo", "localUserInfo", "request", "action", "result", "console", "loadStatistics", "uni", "title", "mask", "promises", "page", "pageSize", "countOnly", "showLoading", "showError", "Promise", "results", "icon", "<PERSON><PERSON><PERSON><PERSON>", "count", "sizeType", "sourceType", "success", "tempFile<PERSON>ath", "filePath", "cloudPath", "uploadResult", "avatar", "updateResult", "toEditProfile", "url", "<PERSON><PERSON><PERSON><PERSON>", "toMyHouses", "toFavorites", "toAppointments", "toMessages", "toSettings", "toHelp", "toAbout", "toRegister", "toPublishHouse", "toIdentityVerify", "getVerifyStatusText", "getVerifyStatusClass", "handleLogout", "content", "onLoad", "onShow", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACqC;;;AAG3F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAkoB,CAAgB,goBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC6LtpB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAKA;gBACAC;gBACA;kBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACAC;kBACAC;gBACA;cAAA;gBAFAC;gBAIA;kBACA;kBACA;kBACA;gBACA;kBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACA;gBACAJ;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGA;gBACAC;kBACAC;kBACAC;gBACA;;gBAEA;gBACAC;gBACA;gBACAR;kBACAC;kBACAX;oBAAAmB;oBAAAC;oBAAAC;kBAAA;gBACA;kBAAAC;kBAAAC;gBAAA;gBACA;gBACAb;kBACAC;kBACAX;oBAAAmB;oBAAAC;oBAAAC;kBAAA;gBACA;kBAAAC;kBAAAC;gBAAA;gBACA;gBACAb;kBACAC;kBACAX;oBAAAmB;oBAAAC;oBAAAC;kBAAA;gBACA;kBAAAC;kBAAAC;gBAAA;gBACA;gBACAb;kBACAC;gBACA;kBAAAW;kBAAAC;gBAAA,GACA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBAEA;gBACA;kBACA;gBACA;kBACA;gBACA;;gBAEA;gBACA;kBACA;gBACA;kBACA;gBACA;;gBAEA;gBACA;kBACA;gBACA;kBACA;gBACA;;gBAEA;gBACA;kBACA;gBACA;kBACA;gBACA;;gBAEA;gBACAV;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAF;;gBAEA;gBACAE;;gBAEA;gBACA;gBACA;gBACA;gBACA;;gBAEA;gBACAA;kBACAC;kBACAU;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAIA;IACAC;MAAA;MACAZ;QACAa;QACAC;QACAC;QACAC;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBACAC;oBAEAjB;sBACAC;oBACA;oBAAA;oBAAA;oBAAA,OAIAN;sBACAC;sBACAX;wBACAiC;wBACAC;sBACA;oBACA;kBAAA;oBANAC;oBAAA,MAQAA;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA,OAEAzB;sBACAC;sBACAX;wBACAoC;sBACA;oBACA;kBAAA;oBALAC;oBAOA;sBACA;sBACAtB;wBACAC;wBACAU;sBACA;oBACA;kBAAA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAGAb;oBACAE;sBACAC;sBACAU;oBACA;kBAAA;oBAAA;oBAEAX;oBAAA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAEA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACAuB;MACAvB;QACAwB;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA1B;QACAwB;MACA;IACA;IAEA;IACAG;MACA3B;QACAwB;MACA;IACA;IAEA;IACAI;MACA5B;QACAwB;MACA;IACA;IAEA;IACAK;MACA7B;QACAwB;MACA;IACA;IAEA;IACAM;MACA9B;QACAC;QACAU;MACA;IACA;IAEA;IACAoB;MACA/B;QACAC;QACAU;MACA;IACA;IAEA;IACAqB;MACAhC;QACAC;QACAU;MACA;IACA;IAEA;IACAsB;MACAjC;QACAwB;MACA;IACA;IAEA;IACAU;MACAlC;QACAwB;MACA;IACA;IAEA;IACAW;MACAnC;QACAC;QACAU;MACA;IACA;IAEA;IACAyB;MAAA;MACA;QACA;MACA;MAEA;MACA;MAEA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACAtC;QACAC;QACAsC;QACAvB;UACA;YACA;YACA;YACAhB;cACAC;cACAU;YACA;UACA;QACA;MACA;IACA;EACA;EAEA6B;IACA;IACA;EACA;EAEAC;IACA;IACA;IACA;EACA;EAEA;EACAC;IACAjC,aACA,qBACA,sBACA;MACAT;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9hBA;AAAA;AAAA;AAAA;AAAy7B,CAAgB,m5BAAG,EAAC,C;;;;;;;;;;;ACA78B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/profile.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/user/profile.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./profile.vue?vue&type=template&id=64c291de&scoped=true&\"\nvar renderjs\nimport script from \"./profile.vue?vue&type=script&lang=js&\"\nexport * from \"./profile.vue?vue&type=script&lang=js&\"\nimport style0 from \"./profile.vue?vue&type=style&index=0&id=64c291de&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"64c291de\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/profile.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=template&id=64c291de&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.userInfo._id ? _vm.getRoleText(_vm.userInfo.role) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"profile-container\">\n    <!-- 用户信息头部 - 已登录状态 -->\n    <view class=\"user-header\" v-if=\"userInfo._id\">\n      <view class=\"user-info\">\n        <image\n          class=\"avatar\"\n          :src=\"userInfo.avatar || '/static/default-avatar.png'\"\n          mode=\"aspectFill\"\n          @click=\"chooseAvatar\"\n        ></image>\n        <view class=\"user-details\">\n          <text class=\"nickname\">{{ userInfo.nickname || userInfo.username || '未设置昵称' }}</text>\n          <text class=\"role\">{{ getRoleText(userInfo.role) }}</text>\n          <text class=\"phone\">{{ userInfo.mobile || '未绑定手机' }}</text>\n        </view>\n      </view>\n      <view class=\"edit-btn\" @click=\"toEditProfile\">\n        <uni-icons type=\"compose\" size=\"20\" color=\"#fff\"></uni-icons>\n      </view>\n    </view>\n\n    <!-- 未登录状态 -->\n    <view class=\"login-section\" v-else>\n      <view class=\"login-header\">\n        <view class=\"login-content\">\n          <image class=\"default-avatar\" src=\"/static/default-avatar.png\" mode=\"aspectFill\"></image>\n          <text class=\"login-tip\">欢迎来到毕业租房平台</text>\n          <text class=\"login-subtitle\">登录后享受更多便捷服务</text>\n          <button class=\"login-btn\" @click=\"toLogin\">立即登录</button>\n          <view class=\"register-tip\">\n            <text class=\"tip-text\">还没有账号？</text>\n            <text class=\"register-link\" @click=\"toRegister\">立即注册</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 未登录时的功能预览 -->\n      <view class=\"preview-section\">\n        <view class=\"section-title\">\n          <uni-icons type=\"star\" size=\"16\" color=\"#007aff\"></uni-icons>\n          <text class=\"title-text\">登录后可享受</text>\n        </view>\n        <view class=\"preview-list\">\n          <view class=\"preview-item\">\n            <view class=\"preview-icon\">\n              <uni-icons type=\"home\" size=\"24\" color=\"#007aff\"></uni-icons>\n            </view>\n            <view class=\"preview-content\">\n              <text class=\"preview-title\">发布房源</text>\n              <text class=\"preview-desc\">免费发布房源信息，快速找到租客</text>\n            </view>\n          </view>\n          <view class=\"preview-item\">\n            <view class=\"preview-icon\">\n              <uni-icons type=\"heart\" size=\"24\" color=\"#ff6b6b\"></uni-icons>\n            </view>\n            <view class=\"preview-content\">\n              <text class=\"preview-title\">收藏房源</text>\n              <text class=\"preview-desc\">收藏心仪房源，随时查看对比</text>\n            </view>\n          </view>\n          <view class=\"preview-item\">\n            <view class=\"preview-icon\">\n              <uni-icons type=\"calendar\" size=\"24\" color=\"#ffa500\"></uni-icons>\n            </view>\n            <view class=\"preview-content\">\n              <text class=\"preview-title\">预约看房</text>\n              <text class=\"preview-desc\">在线预约看房时间，高效找房</text>\n            </view>\n          </view>\n          <view class=\"preview-item\">\n            <view class=\"preview-icon\">\n              <uni-icons type=\"chatbubble\" size=\"24\" color=\"#52c41a\"></uni-icons>\n            </view>\n            <view class=\"preview-content\">\n              <text class=\"preview-title\">在线沟通</text>\n              <text class=\"preview-desc\">与房东直接沟通，了解详细信息</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 数据统计卡片 - 已登录状态 -->\n    <view class=\"stats-section\" v-if=\"userInfo._id\">\n      <view class=\"stats-grid\">\n        <view class=\"stats-item\" @click=\"toMyHouses\">\n          <text class=\"stats-number\">{{ myHousesCount }}</text>\n          <text class=\"stats-label\">我的发布</text>\n        </view>\n        <view class=\"stats-item\" @click=\"toFavorites\">\n          <text class=\"stats-number\">{{ favoritesCount }}</text>\n          <text class=\"stats-label\">我的收藏</text>\n        </view>\n        <view class=\"stats-item\" @click=\"toAppointments\">\n          <text class=\"stats-number\">{{ appointmentsCount }}</text>\n          <text class=\"stats-label\">看房记录</text>\n        </view>\n        <view class=\"stats-item\" @click=\"toMessages\">\n          <text class=\"stats-number\">{{ unreadCount }}</text>\n          <text class=\"stats-label\">未读消息</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 功能菜单 -->\n    <view class=\"menu-section\" v-if=\"userInfo._id\">\n      <view class=\"section-title\">\n        <uni-icons type=\"grid\" size=\"16\" color=\"#007aff\"></uni-icons>\n        <text class=\"title-text\">我的服务</text>\n      </view>\n      <view class=\"menu-list\">\n        <view class=\"menu-item\" @click=\"toMyHouses\">\n          <view class=\"menu-icon\">\n            <uni-icons type=\"home\" size=\"24\" color=\"#007aff\"></uni-icons>\n          </view>\n          <text class=\"menu-text\">我的发布</text>\n          <view class=\"menu-badge\" v-if=\"myHousesCount > 0\">{{ myHousesCount }}</view>\n          <uni-icons type=\"arrowright\" size=\"16\" color=\"#ccc\"></uni-icons>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"toFavorites\">\n          <view class=\"menu-icon\">\n            <uni-icons type=\"heart\" size=\"24\" color=\"#ff6b6b\"></uni-icons>\n          </view>\n          <text class=\"menu-text\">我的收藏</text>\n          <view class=\"menu-badge\" v-if=\"favoritesCount > 0\">{{ favoritesCount }}</view>\n          <uni-icons type=\"arrowright\" size=\"16\" color=\"#ccc\"></uni-icons>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"toAppointments\">\n          <view class=\"menu-icon\">\n            <uni-icons type=\"calendar\" size=\"24\" color=\"#ffa500\"></uni-icons>\n          </view>\n          <text class=\"menu-text\">看房记录</text>\n          <view class=\"menu-badge\" v-if=\"appointmentsCount > 0\">{{ appointmentsCount }}</view>\n          <uni-icons type=\"arrowright\" size=\"16\" color=\"#ccc\"></uni-icons>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"toMessages\">\n          <view class=\"menu-icon\">\n            <uni-icons type=\"chatbubble\" size=\"24\" color=\"#10c560\"></uni-icons>\n          </view>\n          <text class=\"menu-text\">消息通知</text>\n          <view class=\"menu-badge\" v-if=\"unreadCount > 0\">{{ unreadCount }}</view>\n          <uni-icons type=\"arrowright\" size=\"16\" color=\"#ccc\"></uni-icons>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 其他功能 -->\n    <view class=\"menu-section\" v-if=\"userInfo._id\">\n      <view class=\"menu-title\">其他功能</view>\n      <view class=\"menu-list\">\n        <view class=\"menu-item\" @click=\"toSettings\">\n          <view class=\"menu-icon\">\n            <uni-icons type=\"gear\" size=\"24\" color=\"#666\"></uni-icons>\n          </view>\n          <text class=\"menu-text\">设置</text>\n          <uni-icons type=\"arrowright\" size=\"16\" color=\"#ccc\"></uni-icons>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"toHelp\">\n          <view class=\"menu-icon\">\n            <uni-icons type=\"help\" size=\"24\" color=\"#666\"></uni-icons>\n          </view>\n          <text class=\"menu-text\">帮助与反馈</text>\n          <uni-icons type=\"arrowright\" size=\"16\" color=\"#ccc\"></uni-icons>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"toAbout\">\n          <view class=\"menu-icon\">\n            <uni-icons type=\"info\" size=\"24\" color=\"#666\"></uni-icons>\n          </view>\n          <text class=\"menu-text\">关于我们</text>\n          <uni-icons type=\"arrowright\" size=\"16\" color=\"#ccc\"></uni-icons>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 退出登录 -->\n    <view class=\"logout-section\" v-if=\"userInfo._id\">\n      <button class=\"logout-btn\" @click=\"handleLogout\">退出登录</button>\n    </view>\n  </view>\n</template>\n\n<script>\nimport request from '@/utils/request.js'\nimport { getUserInfo, setUserInfo, clearUserInfo, checkLogin, requireLogin } from '@/utils/common.js'\nimport { USER_ROLES } from '@/common/config.js'\n\nexport default {\n  data() {\n    return {\n      userInfo: {},\n      myHousesCount: 0,\n      favoritesCount: 0,\n      appointmentsCount: 0,\n      unreadCount: 0\n    }\n  },\n  methods: {\n    // 获取角色文本\n    getRoleText(roles) {\n      if (!roles || !Array.isArray(roles)) return '普通用户'\n      const role = roles[0]\n      return USER_ROLES[role] || '普通用户'\n    },\n    \n    // 加载用户信息\n    async loadUserInfo() {\n      if (!checkLogin()) {\n        this.userInfo = {}\n        return\n      }\n\n      try {\n        // 先从本地存储获取用户信息\n        const localUserInfo = getUserInfo()\n        if (localUserInfo && localUserInfo._id) {\n          this.userInfo = localUserInfo\n        }\n\n        // 然后从服务器获取最新信息\n        const result = await request.callFunction('user-auth', {\n          action: 'getUserInfo'\n        })\n\n        if (result.code === 0) {\n          this.userInfo = result.data\n          // 更新本地存储\n          setUserInfo(result.data)\n        } else {\n          // 如果服务器返回错误，清除本地信息\n          clearUserInfo()\n          this.userInfo = {}\n        }\n      } catch (error) {\n        console.error('获取用户信息失败:', error)\n        // 发生错误时，使用本地存储的信息\n        const localUserInfo = getUserInfo()\n        if (localUserInfo && localUserInfo._id) {\n          this.userInfo = localUserInfo\n        }\n      }\n    },\n    \n    // 加载统计数据\n    async loadStatistics() {\n      if (!checkLogin()) return\n\n      try {\n        // 显示统一的加载状态\n        uni.showLoading({\n          title: '加载统计数据...',\n          mask: true\n        });\n\n        // 并发请求各种统计数据，禁用单个请求的loading\n        const promises = [\n          // 我的发布房源数量\n          request.callFunction('house-management', {\n            action: 'getMyHouses',\n            data: { page: 1, pageSize: 1, countOnly: true }\n          }, { showLoading: false, showError: false }),\n          // 我的收藏数量\n          request.callFunction('favorite-management', {\n            action: 'getFavorites',\n            data: { page: 1, pageSize: 1, countOnly: true }\n          }, { showLoading: false, showError: false }),\n          // 看房记录数量\n          request.callFunction('appointment-management', {\n            action: 'getMyAppointments',\n            data: { page: 1, pageSize: 1, countOnly: true }\n          }, { showLoading: false, showError: false }),\n          // 未读消息数量\n          request.callFunction('message-management', {\n            action: 'getUnreadCount'\n          }, { showLoading: false, showError: false })\n        ]\n\n        const results = await Promise.allSettled(promises)\n\n        // 处理我的发布数量\n        if (results[0].status === 'fulfilled' && results[0].value.code === 0) {\n          this.myHousesCount = results[0].value.data.total || 0\n        } else {\n          this.myHousesCount = 0\n        }\n\n        // 处理我的收藏数量\n        if (results[1].status === 'fulfilled' && results[1].value.code === 0) {\n          this.favoritesCount = results[1].value.data.total || 0\n        } else {\n          this.favoritesCount = 0\n        }\n\n        // 处理看房记录数量\n        if (results[2].status === 'fulfilled' && results[2].value.code === 0) {\n          this.appointmentsCount = results[2].value.data.total || 0\n        } else {\n          this.appointmentsCount = 0\n        }\n\n        // 处理未读消息数量\n        if (results[3].status === 'fulfilled' && results[3].value.code === 0) {\n          this.unreadCount = results[3].value.data.total || 0\n        } else {\n          this.unreadCount = 0\n        }\n\n        // 隐藏加载状态\n        uni.hideLoading()\n\n      } catch (error) {\n        console.error('获取统计数据失败:', error)\n\n        // 隐藏加载状态\n        uni.hideLoading()\n\n        // 发生错误时重置为0\n        this.myHousesCount = 0\n        this.favoritesCount = 0\n        this.appointmentsCount = 0\n        this.unreadCount = 0\n\n        // 显示错误提示\n        uni.showToast({\n          title: '获取统计数据失败',\n          icon: 'none'\n        })\n      }\n    },\n\n\n    \n    // 选择头像\n    chooseAvatar() {\n      uni.chooseImage({\n        count: 1,\n        sizeType: ['compressed'],\n        sourceType: ['album', 'camera'],\n        success: async (res) => {\n          const tempFilePath = res.tempFilePaths[0]\n\n          uni.showLoading({\n            title: '上传中...'\n          })\n\n          try {\n            // 上传到云存储\n            const uploadResult = await request.callFunction('file-upload', {\n              action: 'uploadImage',\n              data: {\n                filePath: tempFilePath,\n                cloudPath: `avatars/${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`\n              }\n            })\n\n            if (uploadResult.code === 0) {\n              // 更新用户头像\n              const updateResult = await request.callFunction('user-auth', {\n                action: 'updateProfile',\n                data: {\n                  avatar: uploadResult.data.fileID\n                }\n              })\n\n              if (updateResult.code === 0) {\n                this.userInfo.avatar = uploadResult.data.fileID\n                uni.showToast({\n                  title: '头像更新成功',\n                  icon: 'success'\n                })\n              }\n            }\n          } catch (error) {\n            console.error('上传头像失败:', error)\n            uni.showToast({\n              title: '上传失败',\n              icon: 'none'\n            })\n          } finally {\n            uni.hideLoading()\n          }\n        }\n      })\n    },\n    \n    // 跳转到编辑资料页\n    toEditProfile() {\n      uni.navigateTo({\n        url: '/pages/user/edit-profile'\n      })\n    },\n    \n    // 跳转到登录页\n    toLogin() {\n      requireLogin('/pages/user/profile')\n    },\n    \n    // 跳转到我的发布\n    toMyHouses() {\n      uni.navigateTo({\n        url: '/pages/user/my-houses'\n      })\n    },\n    \n    // 跳转到我的收藏\n    toFavorites() {\n      uni.navigateTo({\n        url: '/pages/user/favorites'\n      })\n    },\n    \n    // 跳转到看房记录\n    toAppointments() {\n      uni.navigateTo({\n        url: '/pages/user/appointments'\n      })\n    },\n    \n    // 跳转到消息通知\n    toMessages() {\n      uni.navigateTo({\n        url: '/pages/user/messages'\n      })\n    },\n    \n    // 跳转到设置页\n    toSettings() {\n      uni.showToast({\n        title: '功能开发中',\n        icon: 'none'\n      })\n    },\n    \n    // 跳转到帮助页\n    toHelp() {\n      uni.showToast({\n        title: '功能开发中',\n        icon: 'none'\n      })\n    },\n    \n    // 跳转到关于页\n    toAbout() {\n      uni.showToast({\n        title: '功能开发中',\n        icon: 'none'\n      })\n    },\n\n    // 跳转到注册页\n    toRegister() {\n      uni.navigateTo({\n        url: '/pages/register/register'\n      })\n    },\n\n    // 跳转到发布房源页\n    toPublishHouse() {\n      uni.navigateTo({\n        url: '/pages/house/publish'\n      })\n    },\n\n    // 跳转到身份认证页\n    toIdentityVerify() {\n      uni.showToast({\n        title: '功能开发中',\n        icon: 'none'\n      })\n    },\n\n    // 获取认证状态文本\n    getVerifyStatusText() {\n      if (!this.userInfo.student_info && !this.userInfo.landlord_info) {\n        return '未认证'\n      }\n\n      const studentVerified = this.userInfo.student_info?.verified\n      const landlordVerified = this.userInfo.landlord_info?.verified\n\n      if (studentVerified || landlordVerified) {\n        return '已认证'\n      } else {\n        return '待审核'\n      }\n    },\n\n    // 获取认证状态样式类\n    getVerifyStatusClass() {\n      const status = this.getVerifyStatusText()\n      return {\n        'status-verified': status === '已认证',\n        'status-pending': status === '待审核',\n        'status-unverified': status === '未认证'\n      }\n    },\n    \n    // 退出登录\n    handleLogout() {\n      uni.showModal({\n        title: '提示',\n        content: '确定要退出登录吗？',\n        success: (res) => {\n          if (res.confirm) {\n            clearUserInfo()\n            this.userInfo = {}\n            uni.showToast({\n              title: '已退出登录',\n              icon: 'success'\n            })\n          }\n        }\n      })\n    }\n  },\n  \n  onLoad() {\n    this.loadUserInfo()\n    this.loadStatistics()\n  },\n  \n  onShow() {\n    // 页面显示时重新加载数据\n    this.loadUserInfo()\n    this.loadStatistics()\n  },\n  \n  // 下拉刷新\n  onPullDownRefresh() {\n    Promise.all([\n      this.loadUserInfo(),\n      this.loadStatistics()\n    ]).finally(() => {\n      uni.stopPullDownRefresh()\n    })\n  }\n}\n</script>\n\n<style scoped>\n.profile-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n}\n\n.user-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 60rpx 40rpx 40rpx;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.edit-btn {\n  width: 60rpx;\n  height: 60rpx;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 30rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.avatar {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 60rpx;\n  margin-right: 30rpx;\n  border: 4rpx solid rgba(255, 255, 255, 0.3);\n}\n\n.user-details {\n  flex: 1;\n}\n\n.nickname {\n  display: block;\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #fff;\n  margin-bottom: 10rpx;\n}\n\n.role {\n  display: block;\n  font-size: 24rpx;\n  color: rgba(255, 255, 255, 0.8);\n  background: rgba(255, 255, 255, 0.2);\n  padding: 4rpx 12rpx;\n  border-radius: 12rpx;\n  margin-bottom: 10rpx;\n  width: fit-content;\n}\n\n.phone {\n  display: block;\n  font-size: 28rpx;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.edit-btn {\n  padding: 20rpx;\n}\n\n/* 未登录状态样式 */\n.login-section {\n  background: #fff;\n}\n\n.login-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.login-content {\n  padding: 80rpx 40rpx 60rpx;\n  text-align: center;\n  color: #fff;\n}\n\n.default-avatar {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 60rpx;\n  margin-bottom: 30rpx;\n  border: 4rpx solid rgba(255, 255, 255, 0.3);\n}\n\n.login-tip {\n  font-size: 36rpx;\n  font-weight: bold;\n  margin-bottom: 10rpx;\n}\n\n.login-subtitle {\n  font-size: 28rpx;\n  opacity: 0.9;\n  margin-bottom: 40rpx;\n}\n\n.register-tip {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 10rpx;\n}\n\n.tip-text {\n  font-size: 26rpx;\n  opacity: 0.8;\n}\n\n.register-link {\n  font-size: 26rpx;\n  color: #fff;\n  text-decoration: underline;\n}\n\n.login-btn {\n  background: rgba(255, 255, 255, 0.2);\n  color: #fff;\n  border: 2rpx solid rgba(255, 255, 255, 0.3);\n  border-radius: 50rpx;\n  padding: 20rpx 60rpx;\n  font-size: 32rpx;\n  margin-bottom: 30rpx;\n}\n\n.login-btn:active {\n  background: rgba(255, 255, 255, 0.3);\n}\n\n.menu-section {\n  margin: 20rpx;\n  background: #fff;\n  border-radius: 20rpx;\n  overflow: hidden;\n}\n\n.menu-title {\n  padding: 30rpx 30rpx 20rpx;\n  font-size: 28rpx;\n  color: #666;\n  font-weight: 500;\n}\n\n.menu-list {\n  padding: 0 30rpx;\n}\n\n.menu-item {\n  display: flex;\n  align-items: center;\n  padding: 30rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.menu-item:last-child {\n  border-bottom: none;\n}\n\n.menu-icon {\n  margin-right: 30rpx;\n}\n\n.menu-text {\n  flex: 1;\n  font-size: 32rpx;\n  color: #333;\n}\n\n.menu-badge {\n  background: #ff4757;\n  color: #fff;\n  font-size: 20rpx;\n  padding: 4rpx 8rpx;\n  border-radius: 10rpx;\n  margin-right: 20rpx;\n  min-width: 32rpx;\n  text-align: center;\n}\n\n.logout-section {\n  margin: 40rpx 20rpx;\n}\n\n.logout-btn {\n  width: 100%;\n  background: #fff;\n  color: #ff4757;\n  border: 2rpx solid #ff4757;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  font-size: 32rpx;\n}\n\n/* 数据统计样式 */\n.stats-section {\n  background: #fff;\n  margin: 20rpx 30rpx;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 20rpx;\n}\n\n.stats-item {\n  text-align: center;\n  padding: 20rpx 10rpx;\n  border-radius: 12rpx;\n  background: #f8f9fa;\n  transition: all 0.3s ease;\n}\n\n.stats-item:active {\n  background: #e9ecef;\n  transform: scale(0.95);\n}\n\n.stats-number {\n  display: block;\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #007aff;\n  margin-bottom: 8rpx;\n}\n\n.stats-label {\n  display: block;\n  font-size: 24rpx;\n  color: #666;\n}\n\n/* 功能预览样式 */\n.preview-section {\n  padding: 40rpx 30rpx;\n  background: #fff;\n}\n\n.preview-list {\n  display: flex;\n  flex-direction: column;\n  gap: 30rpx;\n}\n\n.preview-item {\n  display: flex;\n  align-items: center;\n  gap: 20rpx;\n  padding: 30rpx;\n  background: #f8f9fa;\n  border-radius: 20rpx;\n}\n\n.preview-icon {\n  width: 60rpx;\n  height: 60rpx;\n  background: rgba(0, 122, 255, 0.1);\n  border-radius: 30rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.preview-content {\n  flex: 1;\n}\n\n.preview-title {\n  display: block;\n  font-size: 30rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.preview-desc {\n  display: block;\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.4;\n}\n\n/* 认证状态样式 */\n.verify-status {\n  font-size: 24rpx;\n  padding: 4rpx 12rpx;\n  border-radius: 12rpx;\n  margin-left: auto;\n}\n\n.status-verified {\n  background: #f6ffed;\n  color: #52c41a;\n}\n\n.status-pending {\n  background: #fff7e6;\n  color: #fa8c16;\n}\n\n.status-unverified {\n  background: #f5f5f5;\n  color: #999;\n}\n\n/* 工具菜单样式 */\n.tools-section {\n  margin-top: 20rpx;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=style&index=0&id=64c291de&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=style&index=0&id=64c291de&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751999941\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}