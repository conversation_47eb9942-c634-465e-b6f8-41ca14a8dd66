{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端/pages/index/index.vue?9c3f", "webpack:///D:/web/project/前端/pages/index/index.vue?4577", "webpack:///D:/web/project/前端/pages/index/index.vue?4e00", "webpack:///D:/web/project/前端/pages/index/index.vue?a536", "uni-app:///pages/index/index.vue", "webpack:///D:/web/project/前端/pages/index/index.vue?bcb9", "webpack:///D:/web/project/前端/pages/index/index.vue?8390"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "currentLocation", "loading", "statusBarHeight", "banners", "image", "title", "desc", "url", "recommendHouses", "latestHouses", "methods", "getTypeText", "loadRecommendHouses", "console", "request", "action", "page", "pageSize", "sort", "result", "uni", "icon", "loadLatestHouses", "loadData", "Promise", "toSearch", "chooseLocation", "success", "name", "latitude", "longitude", "fail", "onBannerClick", "urlParams", "filterData", "toHouseList", "type", "duration", "toMap", "address", "city", "district", "setTimeout", "errorMsg", "content", "confirmText", "parseCity", "parseDistrict", "toHouseDetail", "onLoad", "onShow", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAgoB,CAAgB,8nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACmOppB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MAAA;MACAC,UACA;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,EACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;QAAA;MAAA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBAAA;gBAAA,OACAC;kBACAC;kBACAhB;oBACAiB;oBACAC;oBACAC;kBACA;gBACA;cAAA;gBAPAC;gBASAN;gBACA;kBACA;kBACAA;gBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAO;kBACAf;kBACAgB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAT;gBAAA;gBAAA,OACAC;kBACAC;kBACAhB;oBACAiB;oBACAC;oBACAC;kBACA;gBACA;cAAA;gBAPAC;gBASAN;gBACA;kBACA;kBACAA;gBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAO;kBACAf;kBACAgB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAE;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAEAC,aACA,8BACA,0BACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACAL;QACAb;MACA;IACA;IAEA;IACAmB;MAAA;MACAN;QACAO;UACA;UACAP;YACAQ;YACAC;YACAC;UACA;QACA;QACAC;UACAlB;QACA;MACA;IACA;IAEA;IACAmB;MACA;QACA;QACA,mBACA,sBACA,qBACA,wBACA,sBACA;;QAEA;QACA;QAEA;UACA;UACA;YACA;YACA;cACA;cACA;cACAC;gBACAC;cACA;cACAd;YACA;UACA;UACAA;YACAb;UACA;QACA;UACAa;YACAb;UACA;QACA;MACA;IACA;IAEA;IACA4B;MACA;MACA;QACA;QACAtB;QACAO;UAAAgB;QAAA;;QAEA;QACA;UACA;UACA;UACA;QACA;QACAhB;UACAf;UACAgB;UACAgB;QACA;MACA;MACAjB;QACAb;MACA;IACA;IAEA;IACA+B;MAAA;MACAzB;;MAEA;MACAO;QACAO;UACAd;;UAEA;UACA;YACAgB;YACAC;YACAS;YACAX;YACAY;YACAC;UACA;UAEArB;UAEAA;YACAf;YACAgB;YACAgB;UACA;;UAEA;UACAK;YACAtB;cACAb;YACA;UACA;QACA;QACAwB;UACAlB;UAEA;UACA;YACA8B;UACA;YACAA;UACA;YACAA;UACA;YACAA;UACA;UAEAvB;YACAf;YACAgB;YACAgB;UACA;;UAEA;UACA;YACAK;cACAtB;gBACAf;gBACAuC;gBACAC;gBACAlB;kBACA;oBACAP;kBACA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACA0B;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA5B;QACAb;MACA;IACA;EACA;EAEA0C;IACA;IACA;IACA;;IAEA;IACA;IACA;MACA;IACA;;IAEA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEA;EACAC;IACA;MACA/B;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpiBA;AAAA;AAAA;AAAA;AAAu7B,CAAgB,i5BAAG,EAAC,C;;;;;;;;;;;ACA38B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.loading ? _vm.recommendHouses.length : null\n  var l0 =\n    !_vm.loading && g0 > 0\n      ? _vm.__map(_vm.recommendHouses, function (house, __i0__) {\n          var $orig = _vm.__get_orig(house)\n          var m0 = _vm.getTypeText(house.type)\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  var g1 = !_vm.loading ? _vm.latestHouses.length : null\n  var l1 =\n    !_vm.loading && g1 > 0\n      ? _vm.__map(_vm.latestHouses, function (house, __i1__) {\n          var $orig = _vm.__get_orig(house)\n          var m1 = _vm.getTypeText(house.type)\n          return {\n            $orig: $orig,\n            m1: m1,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"index-container\">\r\n    <!-- 头部搜索栏 -->\r\n    <view class=\"header\" :style=\"{ paddingTop: (statusBarHeight * 2 + 60) + 'rpx' }\">\r\n      <view class=\"header-content\">\r\n        <view class=\"greeting-section\">\r\n          <text class=\"greeting-text\">Hi，找房子吗？</text>\r\n          <text class=\"greeting-sub\">为您推荐优质房源</text>\r\n        </view>\r\n        <view class=\"location-btn\" @click=\"chooseLocation\">\r\n          <uni-icons type=\"location\" size=\"16\" color=\"#fff\"></uni-icons>\r\n          <text class=\"location-text\">{{ currentLocation || '定位' }}</text>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"search-section\">\r\n        <view class=\"search-bar\" @click=\"toSearch\">\r\n          <uni-icons type=\"search\" size=\"20\" color=\"#999\"></uni-icons>\r\n          <text class=\"search-placeholder\">搜索房源、地址、关键词</text>\r\n          <view class=\"search-btn\">\r\n            <text class=\"search-btn-text\">搜索</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 轮播图 -->\r\n    <view class=\"banner-section\">\r\n      <swiper\r\n        class=\"banner-swiper\"\r\n        indicator-dots\r\n        indicator-color=\"rgba(255,255,255,0.4)\"\r\n        indicator-active-color=\"#fff\"\r\n        autoplay\r\n        circular\r\n        interval=\"4000\"\r\n        duration=\"500\"\r\n      >\r\n        <swiper-item v-for=\"(banner, index) in banners\" :key=\"index\">\r\n          <view class=\"banner-item\" @click=\"onBannerClick(banner)\">\r\n            <image :src=\"banner.image\" mode=\"aspectFill\" class=\"banner-image\"></image>\r\n            <view class=\"banner-overlay\">\r\n              <text class=\"banner-title\">{{ banner.title }}</text>\r\n              <text class=\"banner-desc\">{{ banner.desc || '发现更多优质房源' }}</text>\r\n            </view>\r\n          </view>\r\n        </swiper-item>\r\n      </swiper>\r\n    </view>\r\n\r\n    <!-- 快捷入口 -->\r\n    <view class=\"quick-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">快速找房</text>\r\n        <text class=\"section-subtitle\">选择您需要的房源类型</text>\r\n      </view>\r\n\r\n      <view class=\"quick-grid\">\r\n        <view class=\"quick-item\" @click=\"toHouseList('whole')\">\r\n          <view class=\"quick-icon whole\">\r\n            <text class=\"icon-emoji\">🏠</text>\r\n          </view>\r\n          <text class=\"quick-title\">整租</text>\r\n          <text class=\"quick-desc\">独立空间</text>\r\n        </view>\r\n        <view class=\"quick-item\" @click=\"toHouseList('shared')\">\r\n          <view class=\"quick-icon shared\">\r\n            <text class=\"icon-emoji\">👥</text>\r\n          </view>\r\n          <text class=\"quick-title\">合租</text>\r\n          <text class=\"quick-desc\">性价比高</text>\r\n        </view>\r\n        <view class=\"quick-item\" @click=\"toHouseList('single')\">\r\n          <view class=\"quick-icon single\">\r\n            <text class=\"icon-emoji\">🛏️</text>\r\n          </view>\r\n          <text class=\"quick-title\">单间</text>\r\n          <text class=\"quick-desc\">经济实惠</text>\r\n        </view>\r\n        <view class=\"quick-item\" @click=\"toMap\">\r\n          <view class=\"quick-icon map\">\r\n            <text class=\"icon-emoji\">🗺️</text>\r\n          </view>\r\n          <text class=\"quick-title\">地图找房</text>\r\n          <text class=\"quick-desc\">位置优先</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 推荐房源 -->\r\n    <view class=\"recommend-section\">\r\n      <view class=\"content-header\">\r\n        <view class=\"header-left\">\r\n          <text class=\"content-title\">🔥 热门推荐</text>\r\n          <text class=\"content-subtitle\">精选优质房源</text>\r\n        </view>\r\n        <view class=\"more-btn\" @click=\"toHouseList()\">\r\n          <text class=\"more-text\">更多</text>\r\n          <uni-icons type=\"arrowright\" size=\"14\" color=\"#007aff\"></uni-icons>\r\n        </view>\r\n      </view>\r\n\r\n      <view v-if=\"loading\" class=\"loading-container\">\r\n        <view class=\"loading-content\">\r\n          <view class=\"loading-spinner\"></view>\r\n          <text class=\"loading-text\">加载中...</text>\r\n        </view>\r\n      </view>\r\n\r\n      <scroll-view v-else-if=\"recommendHouses.length > 0\" class=\"recommend-scroll\" scroll-x show-scrollbar=\"false\">\r\n        <view class=\"recommend-list\">\r\n          <view class=\"recommend-card\" v-for=\"house in recommendHouses\" :key=\"house._id\" @click=\"toHouseDetail(house._id)\">\r\n            <view class=\"card-image\">\r\n              <image :src=\"house.images && house.images[0] || '/static/default-house.png'\" mode=\"aspectFill\" class=\"house-image\"></image>\r\n              <view class=\"image-overlay\">\r\n                <view class=\"house-type-tag\">{{ getTypeText(house.type) }}</view>\r\n                <view class=\"favorite-btn\">\r\n                  <uni-icons type=\"heart\" size=\"16\" color=\"#fff\"></uni-icons>\r\n                </view>\r\n              </view>\r\n            </view>\r\n            <view class=\"card-content\">\r\n              <text class=\"card-title\">{{ house.title }}</text>\r\n              <view class=\"card-tags\">\r\n                <text class=\"tag\" v-if=\"house.room_count\">{{ house.room_count }}室</text>\r\n                <text class=\"tag\" v-if=\"house.hall_count\">{{ house.hall_count }}厅</text>\r\n                <text class=\"tag\" v-if=\"house.area\">{{ house.area }}㎡</text>\r\n              </view>\r\n              <view class=\"card-location\">\r\n                <uni-icons type=\"location\" size=\"12\" color=\"#999\"></uni-icons>\r\n                <text class=\"location-text\">{{ house.location.district }}</text>\r\n              </view>\r\n              <view class=\"card-price\">\r\n                <text class=\"price\">¥{{ house.price }}</text>\r\n                <text class=\"price-unit\">/月</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n\r\n      <view v-else class=\"empty-state\">\r\n        <view class=\"empty-icon\">🏠</view>\r\n        <text class=\"empty-title\">暂无推荐房源</text>\r\n        <text class=\"empty-desc\">稍后再来看看吧</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 最新房源 -->\r\n    <view class=\"latest-section\">\r\n      <view class=\"content-header\">\r\n        <view class=\"header-left\">\r\n          <text class=\"content-title\">✨ 最新发布</text>\r\n          <text class=\"content-subtitle\">新鲜出炉的房源</text>\r\n        </view>\r\n        <view class=\"more-btn\" @click=\"toHouseList()\">\r\n          <text class=\"more-text\">更多</text>\r\n          <uni-icons type=\"arrowright\" size=\"14\" color=\"#007aff\"></uni-icons>\r\n        </view>\r\n      </view>\r\n\r\n      <view v-if=\"loading\" class=\"loading-container\">\r\n        <view class=\"loading-content\">\r\n          <view class=\"loading-spinner\"></view>\r\n          <text class=\"loading-text\">加载中...</text>\r\n        </view>\r\n      </view>\r\n\r\n      <view v-else-if=\"latestHouses.length > 0\" class=\"latest-list\">\r\n        <view class=\"latest-card\" v-for=\"house in latestHouses\" :key=\"house._id\" @click=\"toHouseDetail(house._id)\">\r\n          <view class=\"card-image-container\">\r\n            <image :src=\"house.images && house.images[0] || '/static/default-house.png'\" mode=\"aspectFill\" class=\"card-image\"></image>\r\n            <view class=\"image-badges\">\r\n              <view class=\"type-badge\">{{ getTypeText(house.type) }}</view>\r\n              <view class=\"new-badge\">NEW</view>\r\n            </view>\r\n          </view>\r\n          <view class=\"card-info\">\r\n            <view class=\"info-header\">\r\n              <text class=\"info-title\">{{ house.title }}</text>\r\n              <view class=\"favorite-icon\">\r\n                <uni-icons type=\"heart\" size=\"16\" color=\"#ddd\"></uni-icons>\r\n              </view>\r\n            </view>\r\n            <text class=\"info-desc\">{{ house.description || '暂无描述' }}</text>\r\n            <view class=\"info-tags\">\r\n              <text class=\"info-tag\" v-if=\"house.room_count\">{{ house.room_count }}室</text>\r\n              <text class=\"info-tag\" v-if=\"house.hall_count\">{{ house.hall_count }}厅</text>\r\n              <text class=\"info-tag\" v-if=\"house.area\">{{ house.area }}㎡</text>\r\n            </view>\r\n            <view class=\"info-location\">\r\n              <uni-icons type=\"location\" size=\"14\" color=\"#999\"></uni-icons>\r\n              <text class=\"location-detail\">{{ house.location.district }} {{ house.location.address }}</text>\r\n            </view>\r\n            <view class=\"info-bottom\">\r\n              <view class=\"price-section\">\r\n                <text class=\"price-amount\">¥{{ house.price }}</text>\r\n                <text class=\"price-period\">/月</text>\r\n              </view>\r\n              <view class=\"stats-section\">\r\n                <view class=\"stat-item\">\r\n                  <uni-icons type=\"eye\" size=\"12\" color=\"#999\"></uni-icons>\r\n                  <text class=\"stat-text\">{{ house.view_count || 0 }}</text>\r\n                </view>\r\n                <view class=\"stat-item\">\r\n                  <uni-icons type=\"heart\" size=\"12\" color=\"#999\"></uni-icons>\r\n                  <text class=\"stat-text\">{{ house.favorite_count || 0 }}</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <view v-else class=\"empty-state\">\r\n        <view class=\"empty-icon\">📝</view>\r\n        <text class=\"empty-title\">暂无最新房源</text>\r\n        <text class=\"empty-desc\">稍后再来看看吧</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 底部间距 -->\r\n    <view class=\"bottom-spacing\"></view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport request from '@/utils/request.js'\r\nimport { HOUSE_TYPES } from '@/common/config.js'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      currentLocation: '',\r\n      loading: false,\r\n      statusBarHeight: 0, // 状态栏高度\r\n      banners: [\r\n        {\r\n          image: '/static/banner1.jpg',\r\n          title: '精选优质房源',\r\n          desc: '为您推荐性价比最高的房源',\r\n          url: '/pages/house/list?sort=favorite_count_desc'\r\n        },\r\n        {\r\n          image: '/static/banner2.jpg',\r\n          title: '毕业生租房专区',\r\n          desc: '专为毕业生打造的租房平台',\r\n          url: '/pages/house/list?type=single'\r\n        },\r\n        {\r\n          image: '/static/banner3.jpg',\r\n          title: '安全租房保障',\r\n          desc: '真实房源，安全交易，放心租房',\r\n          url: '/pages/house/list'\r\n        }\r\n      ],\r\n      recommendHouses: [],\r\n      latestHouses: []\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取房源类型文本\r\n    getTypeText(type) {\r\n      const typeItem = HOUSE_TYPES.find(item => item.value === type)\r\n      return typeItem ? typeItem.label : type\r\n    },\r\n\r\n    // 加载推荐房源\r\n    async loadRecommendHouses() {\r\n      try {\r\n        console.log('开始加载推荐房源...')\r\n        const result = await request.callFunction('house-management', {\r\n          action: 'getHouseList',\r\n          data: {\r\n            page: 1,\r\n            pageSize: 5,\r\n            sort: 'favorite_count_desc'\r\n          }\r\n        })\r\n\r\n        console.log('推荐房源返回结果:', result)\r\n        if (result.code === 0) {\r\n          this.recommendHouses = result.data.list || []\r\n          console.log('推荐房源数据:', this.recommendHouses)\r\n        } else {\r\n          console.error('推荐房源请求失败:', result)\r\n        }\r\n      } catch (error) {\r\n        console.error('加载推荐房源失败:', error)\r\n        uni.showToast({\r\n          title: '加载推荐房源失败',\r\n          icon: 'none'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 加载最新房源\r\n    async loadLatestHouses() {\r\n      try {\r\n        console.log('开始加载最新房源...')\r\n        const result = await request.callFunction('house-management', {\r\n          action: 'getHouseList',\r\n          data: {\r\n            page: 1,\r\n            pageSize: 3,\r\n            sort: 'publish_date_desc'\r\n          }\r\n        })\r\n\r\n        console.log('最新房源返回结果:', result)\r\n        if (result.code === 0) {\r\n          this.latestHouses = result.data.list || []\r\n          console.log('最新房源数据:', this.latestHouses)\r\n        } else {\r\n          console.error('最新房源请求失败:', result)\r\n        }\r\n      } catch (error) {\r\n        console.error('加载最新房源失败:', error)\r\n        uni.showToast({\r\n          title: '加载最新房源失败',\r\n          icon: 'none'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 加载所有数据\r\n    async loadData() {\r\n      this.loading = true\r\n      try {\r\n        await Promise.all([\r\n          this.loadRecommendHouses(),\r\n          this.loadLatestHouses()\r\n        ])\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 跳转到搜索页\r\n    toSearch() {\r\n      uni.navigateTo({\r\n        url: '/pages/search/search'\r\n      })\r\n    },\r\n\r\n    // 选择位置\r\n    chooseLocation() {\r\n      uni.chooseLocation({\r\n        success: (res) => {\r\n          this.currentLocation = res.name || res.address\r\n          uni.setStorageSync('userLocation', {\r\n            name: this.currentLocation,\r\n            latitude: res.latitude,\r\n            longitude: res.longitude\r\n          })\r\n        },\r\n        fail: (error) => {\r\n          console.error('选择位置失败:', error)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 轮播图点击\r\n    onBannerClick(banner) {\r\n      if (banner.url) {\r\n        // 检查是否是 tabBar 页面\r\n        const tabBarPages = [\r\n          '/pages/index/index',\r\n          '/pages/house/list',\r\n          '/pages/search/search',\r\n          '/pages/user/profile'\r\n        ]\r\n\r\n        // 提取页面路径（去掉查询参数）\r\n        const pagePath = banner.url.split('?')[0]\r\n\r\n        if (tabBarPages.includes(pagePath)) {\r\n          // 如果有查询参数，需要特殊处理\r\n          if (banner.url.includes('?')) {\r\n            const urlParams = new URLSearchParams(banner.url.split('?')[1])\r\n            if (pagePath === '/pages/house/list') {\r\n              // 房源列表页面的参数处理\r\n              const filterData = {}\r\n              urlParams.forEach((value, key) => {\r\n                filterData[key] = value\r\n              })\r\n              uni.setStorageSync('houseListFilter', filterData)\r\n            }\r\n          }\r\n          uni.switchTab({\r\n            url: pagePath\r\n          })\r\n        } else {\r\n          uni.navigateTo({\r\n            url: banner.url\r\n          })\r\n        }\r\n      }\r\n    },\r\n\r\n    // 跳转到房源列表\r\n    toHouseList(type) {\r\n      // 房源列表是 tabBar 页面，需要使用 switchTab\r\n      if (type) {\r\n        // 如果有类型参数，先存储到本地，然后在房源列表页面读取\r\n        console.log('首页设置房源筛选类型:', type)\r\n        uni.setStorageSync('houseListFilter', { type })\r\n\r\n        // 显示提示信息\r\n        const typeNames = {\r\n          'whole': '整租',\r\n          'shared': '合租',\r\n          'single': '单间'\r\n        }\r\n        uni.showToast({\r\n          title: `跳转到${typeNames[type] || type}房源`,\r\n          icon: 'none',\r\n          duration: 1500\r\n        })\r\n      }\r\n      uni.switchTab({\r\n        url: '/pages/house/list'\r\n      })\r\n    },\r\n\r\n    // 地图找房 - 使用系统位置选择器\r\n    toMap() {\r\n      console.log('toMap 函数被调用')\r\n\r\n      // 直接调用系统位置选择器\r\n      uni.chooseLocation({\r\n        success: (res) => {\r\n          console.log('选择位置成功:', res)\r\n\r\n          // 存储选择的位置信息，用于房源列表筛选\r\n          const locationFilter = {\r\n            latitude: res.latitude,\r\n            longitude: res.longitude,\r\n            address: res.address,\r\n            name: res.name || '',\r\n            city: this.parseCity(res.address),\r\n            district: this.parseDistrict(res.address)\r\n          }\r\n\r\n          uni.setStorageSync('houseListLocationFilter', locationFilter)\r\n\r\n          uni.showToast({\r\n            title: `已选择：${res.name || res.address}`,\r\n            icon: 'success',\r\n            duration: 2000\r\n          })\r\n\r\n          // 跳转到房源列表页面，并应用位置筛选\r\n          setTimeout(() => {\r\n            uni.switchTab({\r\n              url: '/pages/house/list'\r\n            })\r\n          }, 1500)\r\n        },\r\n        fail: (error) => {\r\n          console.error('选择位置失败:', error)\r\n\r\n          let errorMsg = '选择位置失败'\r\n          if (error.errMsg.includes('cancel')) {\r\n            errorMsg = '已取消选择位置'\r\n          } else if (error.errMsg.includes('auth')) {\r\n            errorMsg = '需要位置权限，请在小程序设置中开启'\r\n          } else if (error.errMsg.includes('denied')) {\r\n            errorMsg = '位置权限被拒绝'\r\n          } else if (error.errMsg.includes('system permission denied')) {\r\n            errorMsg = '系统位置权限未开启'\r\n          }\r\n\r\n          uni.showToast({\r\n            title: errorMsg,\r\n            icon: 'none',\r\n            duration: 3000\r\n          })\r\n\r\n          // 如果是权限问题，提示用户手动开启\r\n          if (error.errMsg.includes('auth') || error.errMsg.includes('denied')) {\r\n            setTimeout(() => {\r\n              uni.showModal({\r\n                title: '位置权限',\r\n                content: '需要位置权限来使用地图找房功能，请在小程序设置中开启位置权限',\r\n                confirmText: '去设置',\r\n                success: (modalRes) => {\r\n                  if (modalRes.confirm) {\r\n                    uni.openSetting()\r\n                  }\r\n                }\r\n              })\r\n            }, 1000)\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 解析城市信息\r\n    parseCity(address) {\r\n      if (!address) return ''\r\n      // 简单的城市解析逻辑，可以根据实际需要优化\r\n      const cityMatch = address.match(/(.+?市)/)\r\n      return cityMatch ? cityMatch[1] : ''\r\n    },\r\n\r\n    // 解析区县信息\r\n    parseDistrict(address) {\r\n      if (!address) return ''\r\n      // 简单的区县解析逻辑，可以根据实际需要优化\r\n      const districtMatch = address.match(/市(.+?区|.+?县)/)\r\n      return districtMatch ? districtMatch[1] : ''\r\n    },\r\n\r\n    // 跳转到房源详情\r\n    toHouseDetail(houseId) {\r\n      uni.navigateTo({\r\n        url: `/pages/house/detail?id=${houseId}`\r\n      })\r\n    }\r\n  },\r\n\r\n  onLoad() {\r\n    // 获取系统信息，包括状态栏高度\r\n    const systemInfo = uni.getSystemInfoSync()\r\n    this.statusBarHeight = systemInfo.statusBarHeight || 44 // 默认44px\r\n\r\n    // 获取用户位置\r\n    const location = uni.getStorageSync('userLocation')\r\n    if (location) {\r\n      this.currentLocation = location.name\r\n    }\r\n\r\n    // 加载数据\r\n    this.loadData()\r\n  },\r\n\r\n  onShow() {\r\n    // 页面显示时刷新数据\r\n    this.loadData()\r\n  },\r\n\r\n  // 下拉刷新\r\n  onPullDownRefresh() {\r\n    this.loadData().finally(() => {\r\n      uni.stopPullDownRefresh()\r\n    })\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.index-container {\r\n  background: #f8fafc;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 头部区域 */\r\n.header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 30rpx;\r\n  padding-bottom: 100rpx;\r\n  position: relative;\r\n  overflow: hidden;\r\n  border-radius: 0 0 40rpx 40rpx;\r\n}\r\n\r\n.header::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"80\" cy=\"40\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"40\" cy=\"80\" r=\"1.5\" fill=\"rgba(255,255,255,0.1)\"/></svg>');\r\n  pointer-events: none;\r\n}\r\n\r\n\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 40rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.greeting-section {\r\n  flex: 1;\r\n  padding-right: 20rpx;\r\n}\r\n\r\n.greeting-text {\r\n  display: block;\r\n  font-size: 40rpx;\r\n  font-weight: 700;\r\n  color: #fff;\r\n  margin-bottom: 12rpx;\r\n  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\r\n  line-height: 1.2;\r\n}\r\n\r\n.greeting-sub {\r\n  display: block;\r\n  font-size: 28rpx;\r\n  color: rgba(255, 255, 255, 0.85);\r\n  font-weight: 400;\r\n  line-height: 1.3;\r\n}\r\n\r\n.location-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8rpx;\r\n  padding: 16rpx 20rpx;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  border-radius: 25rpx;\r\n  backdrop-filter: blur(20rpx);\r\n  border: 1rpx solid rgba(255, 255, 255, 0.2);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.location-btn:active {\r\n  transform: scale(0.95);\r\n  background: rgba(255, 255, 255, 0.25);\r\n}\r\n\r\n.location-text {\r\n  font-size: 26rpx;\r\n  color: #fff;\r\n  max-width: 120rpx;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  font-weight: 500;\r\n}\r\n\r\n.search-section {\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.search-bar {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border-radius: 30rpx;\r\n  padding: 28rpx 32rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16rpx;\r\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n  backdrop-filter: blur(20rpx);\r\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\r\n  transition: all 0.3s ease;\r\n  min-height: 88rpx;\r\n}\r\n\r\n.search-bar:active {\r\n  transform: translateY(2rpx);\r\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.search-placeholder {\r\n  flex: 1;\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  font-weight: 400;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #007aff 0%, #5856d6 100%);\r\n  border-radius: 22rpx;\r\n  padding: 16rpx 28rpx;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-btn:active {\r\n  transform: scale(0.95);\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.4);\r\n}\r\n\r\n.search-btn-text {\r\n  font-size: 28rpx;\r\n  color: #fff;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 轮播图区域 */\r\n.banner-section {\r\n  margin: 30rpx;\r\n  margin-top: -80rpx;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.banner-swiper {\r\n  height: 360rpx;\r\n  border-radius: 24rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.12), 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\r\n  border: 1rpx solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.banner-item {\r\n  position: relative;\r\n  height: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.banner-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.banner-overlay {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));\r\n  padding: 40rpx 30rpx 30rpx;\r\n  color: #fff;\r\n}\r\n\r\n.banner-title {\r\n  display: block;\r\n  font-size: 32rpx;\r\n  font-weight: 700;\r\n  margin-bottom: 8rpx;\r\n  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.banner-desc {\r\n  display: block;\r\n  font-size: 24rpx;\r\n  opacity: 0.9;\r\n  font-weight: 400;\r\n}\r\n\r\n/* 快捷入口区域 */\r\n.quick-section {\r\n  margin: 50rpx 30rpx;\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.section-title {\r\n  display: block;\r\n  font-size: 32rpx;\r\n  font-weight: 700;\r\n  color: #1a202c;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.section-subtitle {\r\n  display: block;\r\n  font-size: 26rpx;\r\n  color: #718096;\r\n  font-weight: 400;\r\n}\r\n\r\n.quick-grid {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 20rpx;\r\n}\r\n\r\n.quick-item {\r\n  background: #fff;\r\n  border-radius: 20rpx;\r\n  padding: 40rpx 30rpx;\r\n  text-align: center;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\r\n  border: 1rpx solid #f7fafc;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.quick-item::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 4rpx;\r\n  background: linear-gradient(90deg, #667eea, #764ba2);\r\n  transform: scaleX(0);\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.quick-item:active {\r\n  transform: translateY(4rpx);\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.quick-item:active::before {\r\n  transform: scaleX(1);\r\n}\r\n\r\n.quick-icon {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 20rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 auto 20rpx;\r\n  position: relative;\r\n}\r\n\r\n.quick-icon.whole {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.quick-icon.shared {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n}\r\n\r\n.quick-icon.single {\r\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n}\r\n\r\n.quick-icon.map {\r\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\r\n}\r\n\r\n.icon-emoji {\r\n  font-size: 36rpx;\r\n}\r\n\r\n.quick-title {\r\n  display: block;\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #2d3748;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.quick-desc {\r\n  display: block;\r\n  font-size: 24rpx;\r\n  color: #718096;\r\n  font-weight: 400;\r\n}\r\n\r\n/* 内容区域通用样式 */\r\n.recommend-section, .latest-section {\r\n  margin: 50rpx 30rpx;\r\n}\r\n\r\n.content-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-end;\r\n  margin-bottom: 35rpx;\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.content-title {\r\n  display: block;\r\n  font-size: 34rpx;\r\n  font-weight: 700;\r\n  color: #1a202c;\r\n  margin-bottom: 10rpx;\r\n  line-height: 1.2;\r\n}\r\n\r\n.content-subtitle {\r\n  display: block;\r\n  font-size: 26rpx;\r\n  color: #718096;\r\n  font-weight: 400;\r\n  line-height: 1.3;\r\n}\r\n\r\n.more-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8rpx;\r\n  padding: 12rpx 20rpx;\r\n  background: #f7fafc;\r\n  border-radius: 20rpx;\r\n  border: 1rpx solid #e2e8f0;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.more-btn:active {\r\n  background: #edf2f7;\r\n  transform: scale(0.95);\r\n}\r\n\r\n.more-text {\r\n  font-size: 26rpx;\r\n  color: #007aff;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 加载状态 */\r\n.loading-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 80rpx 0;\r\n}\r\n\r\n.loading-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 20rpx;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border: 4rpx solid #f3f4f6;\r\n  border-top: 4rpx solid #007aff;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n.loading-text {\r\n  font-size: 26rpx;\r\n  color: #9ca3af;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 推荐房源样式 */\r\n.recommend-scroll {\r\n  margin: 0 -30rpx;\r\n  padding: 0 30rpx;\r\n}\r\n\r\n.recommend-list {\r\n  display: flex;\r\n  gap: 20rpx;\r\n  padding-bottom: 20rpx;\r\n}\r\n\r\n.recommend-card {\r\n  width: 280rpx;\r\n  flex-shrink: 0;\r\n  background: #fff;\r\n  border-radius: 20rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\r\n  border: 1rpx solid #f7fafc;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.recommend-card:active {\r\n  transform: translateY(4rpx);\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.card-image {\r\n  position: relative;\r\n  height: 200rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.house-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.recommend-card:active .house-image {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.image-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-end;\r\n  padding: 20rpx;\r\n}\r\n\r\n.house-type-tag {\r\n  background: rgba(0, 122, 255, 0.9);\r\n  color: #fff;\r\n  padding: 8rpx 16rpx;\r\n  border-radius: 12rpx;\r\n  font-size: 22rpx;\r\n  font-weight: 600;\r\n  backdrop-filter: blur(10rpx);\r\n}\r\n\r\n.favorite-btn {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  backdrop-filter: blur(10rpx);\r\n}\r\n\r\n.card-content {\r\n  padding: 24rpx;\r\n}\r\n\r\n.card-title {\r\n  display: block;\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #2d3748;\r\n  margin-bottom: 12rpx;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.card-tags {\r\n  display: flex;\r\n  gap: 8rpx;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.tag {\r\n  background: #f7fafc;\r\n  color: #4a5568;\r\n  padding: 6rpx 12rpx;\r\n  border-radius: 8rpx;\r\n  font-size: 22rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.card-location {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8rpx;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.location-text {\r\n  font-size: 24rpx;\r\n  color: #718096;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.card-price {\r\n  display: flex;\r\n  align-items: baseline;\r\n  gap: 4rpx;\r\n}\r\n\r\n.price {\r\n  font-size: 32rpx;\r\n  font-weight: 700;\r\n  color: #e53e3e;\r\n}\r\n\r\n.price-unit {\r\n  font-size: 24rpx;\r\n  color: #718096;\r\n  font-weight: 400;\r\n}\r\n\r\n/* 最新房源样式 */\r\n.latest-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20rpx;\r\n}\r\n\r\n.latest-card {\r\n  background: #fff;\r\n  border-radius: 20rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\r\n  border: 1rpx solid #f7fafc;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n}\r\n\r\n.latest-card:active {\r\n  transform: translateY(2rpx);\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.card-image-container {\r\n  position: relative;\r\n  width: 240rpx;\r\n  height: 180rpx;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.card-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.latest-card:active .card-image {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.image-badges {\r\n  position: absolute;\r\n  top: 12rpx;\r\n  left: 12rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8rpx;\r\n}\r\n\r\n.type-badge {\r\n  background: rgba(0, 122, 255, 0.9);\r\n  color: #fff;\r\n  padding: 6rpx 12rpx;\r\n  border-radius: 10rpx;\r\n  font-size: 20rpx;\r\n  font-weight: 600;\r\n  backdrop-filter: blur(10rpx);\r\n}\r\n\r\n.new-badge {\r\n  background: rgba(239, 68, 68, 0.9);\r\n  color: #fff;\r\n  padding: 6rpx 12rpx;\r\n  border-radius: 10rpx;\r\n  font-size: 20rpx;\r\n  font-weight: 600;\r\n  backdrop-filter: blur(10rpx);\r\n}\r\n\r\n.card-info {\r\n  flex: 1;\r\n  padding: 24rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.info-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.info-title {\r\n  flex: 1;\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #2d3748;\r\n  line-height: 1.4;\r\n  margin-right: 16rpx;\r\n}\r\n\r\n.favorite-icon {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.info-desc {\r\n  font-size: 24rpx;\r\n  color: #718096;\r\n  line-height: 1.5;\r\n  margin-bottom: 12rpx;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.info-tags {\r\n  display: flex;\r\n  gap: 8rpx;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.info-tag {\r\n  background: #f7fafc;\r\n  color: #4a5568;\r\n  padding: 6rpx 12rpx;\r\n  border-radius: 8rpx;\r\n  font-size: 22rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.info-location {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8rpx;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.location-detail {\r\n  font-size: 24rpx;\r\n  color: #718096;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  flex: 1;\r\n}\r\n\r\n.info-bottom {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: auto;\r\n}\r\n\r\n.price-section {\r\n  display: flex;\r\n  align-items: baseline;\r\n  gap: 4rpx;\r\n}\r\n\r\n.price-amount {\r\n  font-size: 32rpx;\r\n  font-weight: 700;\r\n  color: #e53e3e;\r\n}\r\n\r\n.price-period {\r\n  font-size: 24rpx;\r\n  color: #718096;\r\n  font-weight: 400;\r\n}\r\n\r\n.stats-section {\r\n  display: flex;\r\n  gap: 16rpx;\r\n}\r\n\r\n.stat-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6rpx;\r\n}\r\n\r\n.stat-text {\r\n  font-size: 22rpx;\r\n  color: #9ca3af;\r\n}\r\n\r\n/* 空状态样式 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 80rpx 40rpx;\r\n  text-align: center;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 80rpx;\r\n  margin-bottom: 24rpx;\r\n  opacity: 0.6;\r\n}\r\n\r\n.empty-title {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #4a5568;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.empty-desc {\r\n  font-size: 24rpx;\r\n  color: #9ca3af;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 底部间距 */\r\n.bottom-spacing {\r\n  height: 40rpx;\r\n}\r\n\r\n/* 响应式适配 */\r\n@media (max-width: 750rpx) {\r\n  .quick-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .latest-card {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .card-image-container {\r\n    width: 100%;\r\n    height: 200rpx;\r\n  }\r\n}\r\n</style>\r\n\r\n.banner-swiper {\r\n  height: 300rpx;\r\n  margin: 20rpx;\r\n  border-radius: 20rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.banner-swiper image {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.quick-entry {\r\n  background: #fff;\r\n  margin: 20rpx;\r\n  border-radius: 20rpx;\r\n  padding: 40rpx 20rpx;\r\n  display: flex;\r\n  justify-content: space-around;\r\n}\r\n\r\n.entry-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 15rpx;\r\n}\r\n\r\n.entry-icon {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  background: #f8f9fa;\r\n  border-radius: 40rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.entry-text {\r\n  font-size: 26rpx;\r\n  color: #333;\r\n}\r\n\r\n\r\n\r\n.section-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 20rpx;\r\n  padding: 0 10rpx;\r\n}\r\n\r\n.section-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n\r\n.more-btn {\r\n  font-size: 26rpx;\r\n  color: #007aff;\r\n}\r\n\r\n.recommend-list {\r\n  white-space: nowrap;\r\n  padding: 0 10rpx;\r\n}\r\n\r\n.recommend-item {\r\n  display: inline-block;\r\n  width: 280rpx;\r\n  background: #fff;\r\n  border-radius: 20rpx;\r\n  margin-right: 20rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n  vertical-align: top;\r\n}\r\n\r\n.recommend-item .house-image {\r\n  height: 200rpx;\r\n  position: relative;\r\n}\r\n\r\n.recommend-item .house-image image {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.house-type {\r\n  position: absolute;\r\n  top: 15rpx;\r\n  left: 15rpx;\r\n  background: rgba(0, 122, 255, 0.9);\r\n  color: #fff;\r\n  padding: 6rpx 12rpx;\r\n  border-radius: 12rpx;\r\n  font-size: 22rpx;\r\n}\r\n\r\n.recommend-item .house-info {\r\n  padding: 20rpx;\r\n}\r\n\r\n.house-title {\r\n  display: block;\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-bottom: 10rpx;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.house-tags {\r\n  display: flex;\r\n  gap: 8rpx;\r\n  margin-bottom: 10rpx;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.tag {\r\n  background: #f0f0f0;\r\n  color: #666;\r\n  padding: 4rpx 8rpx;\r\n  border-radius: 6rpx;\r\n  font-size: 22rpx;\r\n}\r\n\r\n.house-location {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15rpx;\r\n}\r\n\r\n.location-text {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  margin-left: 6rpx;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.price-info {\r\n  display: flex;\r\n  align-items: baseline;\r\n}\r\n\r\n.price {\r\n  font-size: 30rpx;\r\n  font-weight: bold;\r\n  color: #ff6b6b;\r\n}\r\n\r\n.price-unit {\r\n  font-size: 22rpx;\r\n  color: #999;\r\n  margin-left: 6rpx;\r\n}\r\n\r\n.latest-list {\r\n  background: #fff;\r\n  border-radius: 20rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.house-item {\r\n  display: flex;\r\n  padding: 20rpx;\r\n  border-bottom: 1rpx solid #f8f9fa;\r\n}\r\n\r\n.house-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.house-item .house-image {\r\n  width: 200rpx;\r\n  height: 150rpx;\r\n  border-radius: 12rpx;\r\n  overflow: hidden;\r\n  margin-right: 20rpx;\r\n  position: relative;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.house-item .house-image image {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.house-item .house-info {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n\r\n.house-desc {\r\n  display: block;\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  margin-bottom: 10rpx;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.house-bottom {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-top: 10rpx;\r\n}\r\n\r\n.house-stats {\r\n  display: flex;\r\n  gap: 15rpx;\r\n}\r\n\r\n.stat-item {\r\n  font-size: 22rpx;\r\n  color: #999;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4rpx;\r\n}\r\n\r\n.loading-container {\r\n  padding: 40rpx 0;\r\n  text-align: center;\r\n}\r\n\r\n.empty-container {\r\n  padding: 60rpx 0;\r\n  text-align: center;\r\n  background: #fff;\r\n  border-radius: 20rpx;\r\n}\r\n\r\n.empty-image {\r\n  width: 120rpx;\r\n  height: 120rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751999910\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}