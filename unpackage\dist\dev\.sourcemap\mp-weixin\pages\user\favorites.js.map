{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端/pages/user/favorites.vue?83aa", "webpack:///D:/web/project/前端/pages/user/favorites.vue?8f15", "webpack:///D:/web/project/前端/pages/user/favorites.vue?6b55", "webpack:///D:/web/project/前端/pages/user/favorites.vue?c8dc", "uni-app:///pages/user/favorites.vue", "webpack:///D:/web/project/前端/pages/user/favorites.vue?370b", "webpack:///D:/web/project/前端/pages/user/favorites.vue?3568"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "favoriteList", "loading", "refreshing", "noMore", "page", "pageSize", "methods", "formatRelativeTime", "getTypeText", "loadFavoriteList", "refresh", "request", "action", "result", "console", "loadMore", "onRefresh", "removeFavorite", "uni", "title", "content", "success", "res", "house_id", "icon", "index", "contactLandlord", "itemList", "phoneNumber", "toHouseDetail", "url", "to<PERSON><PERSON>e", "onLoad", "onShow", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACqC;;;AAG7F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9BA;AAAA;AAAA;AAAA;AAAooB,CAAgB,koBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACmExpB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IAEA;IACAC;MACA;QAAA;MAAA;MACA;IACA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAEA;kBACA;kBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGAC;kBACAC;kBACAb;oBACAK;oBACAC;kBACA;gBACA;cAAA;gBANAQ;gBAQA;kBAAA,eACAA;kBAEA;oBACA;kBACA;oBACA;kBACA;kBAEA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACAC;kBACAC;kBACAC;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAC;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA;8BAAA,OAEAX;gCACAC;gCACAb;kCACAwB;gCACA;8BACA;4BAAA;8BALAV;8BAOA;gCACAK;kCACAC;kCACAK;gCACA;;gCAEA;gCACAC;kCAAA;gCAAA;gCACA;kCACA;gCACA;8BACA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAEAX;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAGA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAY;MACA;QACAR;UACAC;UACAK;QACA;QACA;MACA;MAEAN;QACAS;QACAN;UACA;YACA;YACAH;cACAU;YACA;UACA;YACA;YACA;cACAV;gBACAnB;gBACAsB;kBACAH;oBACAC;oBACAK;kBACA;gBACA;cACA;YACA;cACAN;gBACAC;gBACAK;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAK;MACAX;QACAY;MACA;IACA;IAEA;IACAC;MACAb;QACAY;MACA;IACA;EACA;EAEAE;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;IACAhB;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxPA;AAAA;AAAA;AAAA;AAA27B,CAAgB,q5BAAG,EAAC,C;;;;;;;;;;;ACA/8B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/favorites.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/user/favorites.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./favorites.vue?vue&type=template&id=edb94f68&scoped=true&\"\nvar renderjs\nimport script from \"./favorites.vue?vue&type=script&lang=js&\"\nexport * from \"./favorites.vue?vue&type=script&lang=js&\"\nimport style0 from \"./favorites.vue?vue&type=style&index=0&id=edb94f68&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"edb94f68\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/favorites.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./favorites.vue?vue&type=template&id=edb94f68&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.favoriteList, function (item, __i0__) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.getTypeText(item.house.type)\n    var m1 = _vm.formatRelativeTime(item.create_date)\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n    }\n  })\n  var g0 = _vm.favoriteList.length\n  var g1 = !_vm.loading && _vm.favoriteList.length === 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./favorites.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./favorites.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"favorites-container\">\n    <!-- 房源列表 -->\n    <scroll-view \n      class=\"house-list\" \n      scroll-y \n      @scrolltolower=\"loadMore\"\n      refresher-enabled\n      @refresherrefresh=\"onRefresh\"\n      :refresher-triggered=\"refreshing\"\n    >\n      <view class=\"house-item\" v-for=\"item in favoriteList\" :key=\"item.favorite_id\">\n        <view class=\"house-image\" @click=\"toHouseDetail(item.house._id)\">\n          <image \n            :src=\"item.house.images && item.house.images[0] || '/static/default-house.png'\" \n            mode=\"aspectFill\"\n          ></image>\n          <view class=\"house-type\">{{ getTypeText(item.house.type) }}</view>\n        </view>\n        <view class=\"house-info\" @click=\"toHouseDetail(item.house._id)\">\n          <text class=\"house-title\">{{ item.house.title }}</text>\n          <text class=\"house-desc\">{{ item.house.description }}</text>\n          <view class=\"house-tags\">\n            <text class=\"tag\" v-if=\"item.house.room_count\">{{ item.house.room_count }}室</text>\n            <text class=\"tag\" v-if=\"item.house.hall_count\">{{ item.house.hall_count }}厅</text>\n            <text class=\"tag\" v-if=\"item.house.area\">{{ item.house.area }}㎡</text>\n          </view>\n          <view class=\"house-location\">\n            <uni-icons type=\"location\" size=\"12\" color=\"#999\"></uni-icons>\n            <text class=\"location-text\">{{ item.house.location.district }} {{ item.house.location.address }}</text>\n          </view>\n          <view class=\"house-bottom\">\n            <view class=\"price-info\">\n              <text class=\"price\">¥{{ item.house.price }}</text>\n              <text class=\"price-unit\">/月</text>\n            </view>\n            <text class=\"favorite-time\">{{ formatRelativeTime(item.create_date) }}</text>\n          </view>\n        </view>\n        <view class=\"house-actions\">\n          <button class=\"action-btn unfavorite\" @click=\"removeFavorite(item)\">\n            <uni-icons type=\"heart-filled\" size=\"20\" color=\"#ff6b6b\"></uni-icons>\n          </button>\n          <button class=\"action-btn contact\" @click=\"contactLandlord(item.house)\">\n            <uni-icons type=\"chatbubble\" size=\"20\" color=\"#007aff\"></uni-icons>\n          </button>\n        </view>\n      </view>\n      \n      <!-- 加载状态 -->\n      <view class=\"load-status\" v-if=\"favoriteList.length > 0\">\n        <text v-if=\"loading\">加载中...</text>\n        <text v-else-if=\"noMore\">没有更多了</text>\n      </view>\n      \n      <!-- 空状态 -->\n      <view class=\"empty-state\" v-if=\"!loading && favoriteList.length === 0\">\n        <image src=\"/static/empty-favorite.png\" mode=\"aspectFit\"></image>\n        <text class=\"empty-text\">暂无收藏房源</text>\n        <text class=\"empty-tip\">去看看有什么好房源吧</text>\n        <button class=\"browse-btn\" @click=\"toBrowse\">去逛逛</button>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nimport request from '@/utils/request.js'\nimport { formatRelativeTime } from '@/utils/common.js'\nimport { HOUSE_TYPES } from '@/common/config.js'\n\nexport default {\n  data() {\n    return {\n      favoriteList: [],\n      loading: false,\n      refreshing: false,\n      noMore: false,\n      page: 1,\n      pageSize: 10\n    }\n  },\n  methods: {\n    formatRelativeTime,\n    \n    // 获取房源类型文本\n    getTypeText(type) {\n      const typeItem = HOUSE_TYPES.find(item => item.value === type)\n      return typeItem ? typeItem.label : type\n    },\n    \n    // 加载收藏列表\n    async loadFavoriteList(refresh = false) {\n      if (this.loading) return\n      \n      this.loading = true\n      \n      if (refresh) {\n        this.page = 1\n        this.noMore = false\n      }\n      \n      try {\n        const result = await request.callFunction('favorite-management', {\n          action: 'getFavoriteList',\n          data: {\n            page: this.page,\n            pageSize: this.pageSize\n          }\n        })\n        \n        if (result.code === 0) {\n          const { list, total } = result.data\n          \n          if (refresh) {\n            this.favoriteList = list\n          } else {\n            this.favoriteList.push(...list)\n          }\n          \n          this.page++\n          this.noMore = this.favoriteList.length >= total\n        }\n      } catch (error) {\n        console.error('加载收藏列表失败:', error)\n      } finally {\n        this.loading = false\n        this.refreshing = false\n      }\n    },\n    \n    // 加载更多\n    loadMore() {\n      if (!this.noMore && !this.loading) {\n        this.loadFavoriteList()\n      }\n    },\n    \n    // 下拉刷新\n    onRefresh() {\n      this.refreshing = true\n      this.loadFavoriteList(true)\n    },\n    \n    // 取消收藏\n    async removeFavorite(item) {\n      uni.showModal({\n        title: '提示',\n        content: '确定要取消收藏吗？',\n        success: async (res) => {\n          if (res.confirm) {\n            try {\n              const result = await request.callFunction('favorite-management', {\n                action: 'removeFavorite',\n                data: {\n                  house_id: item.house._id\n                }\n              })\n              \n              if (result.code === 0) {\n                uni.showToast({\n                  title: '取消收藏成功',\n                  icon: 'success'\n                })\n                \n                // 从列表中移除\n                const index = this.favoriteList.findIndex(fav => fav.favorite_id === item.favorite_id)\n                if (index > -1) {\n                  this.favoriteList.splice(index, 1)\n                }\n              }\n            } catch (error) {\n              console.error('取消收藏失败:', error)\n            }\n          }\n        }\n      })\n    },\n    \n    // 联系房东\n    contactLandlord(house) {\n      if (!house.contact) {\n        uni.showToast({\n          title: '暂无联系方式',\n          icon: 'none'\n        })\n        return\n      }\n      \n      uni.showActionSheet({\n        itemList: ['拨打电话', '复制微信号'],\n        success: (res) => {\n          if (res.tapIndex === 0) {\n            // 拨打电话\n            uni.makePhoneCall({\n              phoneNumber: house.contact.phone\n            })\n          } else if (res.tapIndex === 1) {\n            // 复制微信号\n            if (house.contact.wechat) {\n              uni.setClipboardData({\n                data: house.contact.wechat,\n                success: () => {\n                  uni.showToast({\n                    title: '微信号已复制',\n                    icon: 'success'\n                  })\n                }\n              })\n            } else {\n              uni.showToast({\n                title: '暂无微信号',\n                icon: 'none'\n              })\n            }\n          }\n        }\n      })\n    },\n    \n    // 跳转到房源详情\n    toHouseDetail(houseId) {\n      uni.navigateTo({\n        url: `/pages/house/detail?id=${houseId}`\n      })\n    },\n    \n    // 跳转到浏览页面\n    toBrowse() {\n      uni.switchTab({\n        url: '/pages/house/list'\n      })\n    }\n  },\n  \n  onLoad() {\n    this.loadFavoriteList(true)\n  },\n  \n  onShow() {\n    // 从详情页返回时刷新列表\n    this.loadFavoriteList(true)\n  },\n  \n  onPullDownRefresh() {\n    this.onRefresh()\n    uni.stopPullDownRefresh()\n  }\n}\n</script>\n\n<style scoped>\n.favorites-container {\n  height: 100vh;\n  background: #f8f9fa;\n}\n\n.house-list {\n  height: 100%;\n  padding: 20rpx;\n}\n\n.house-item {\n  background: #fff;\n  border-radius: 20rpx;\n  margin-bottom: 20rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n  display: flex;\n}\n\n.house-image {\n  position: relative;\n  width: 240rpx;\n  height: 200rpx;\n  flex-shrink: 0;\n}\n\n.house-image image {\n  width: 100%;\n  height: 100%;\n}\n\n.house-type {\n  position: absolute;\n  top: 15rpx;\n  left: 15rpx;\n  background: rgba(0, 122, 255, 0.9);\n  color: #fff;\n  padding: 6rpx 12rpx;\n  border-radius: 12rpx;\n  font-size: 22rpx;\n}\n\n.house-info {\n  flex: 1;\n  padding: 20rpx;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.house-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.house-desc {\n  font-size: 24rpx;\n  color: #666;\n  margin-bottom: 10rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.house-tags {\n  display: flex;\n  gap: 8rpx;\n  margin-bottom: 10rpx;\n  flex-wrap: wrap;\n}\n\n.tag {\n  background: #f0f0f0;\n  color: #666;\n  padding: 4rpx 8rpx;\n  border-radius: 6rpx;\n  font-size: 22rpx;\n}\n\n.house-location {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15rpx;\n}\n\n.location-text {\n  font-size: 24rpx;\n  color: #999;\n  margin-left: 6rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.house-bottom {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.price-info {\n  display: flex;\n  align-items: baseline;\n}\n\n.price {\n  font-size: 30rpx;\n  font-weight: bold;\n  color: #ff6b6b;\n}\n\n.price-unit {\n  font-size: 20rpx;\n  color: #999;\n  margin-left: 4rpx;\n}\n\n.favorite-time {\n  font-size: 22rpx;\n  color: #ccc;\n}\n\n.house-actions {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  padding: 20rpx;\n  gap: 20rpx;\n  border-left: 1rpx solid #f0f0f0;\n}\n\n.action-btn {\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 30rpx;\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.action-btn.unfavorite {\n  background: #ffebee;\n}\n\n.action-btn.contact {\n  background: #e3f2fd;\n}\n\n.load-status {\n  text-align: center;\n  padding: 40rpx;\n  color: #999;\n  font-size: 28rpx;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 100rpx 40rpx;\n}\n\n.empty-state image {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 40rpx;\n}\n\n.empty-text {\n  display: block;\n  font-size: 28rpx;\n  color: #999;\n  margin-bottom: 10rpx;\n}\n\n.empty-tip {\n  display: block;\n  font-size: 24rpx;\n  color: #ccc;\n  margin-bottom: 40rpx;\n}\n\n.browse-btn {\n  background: #007aff;\n  color: #fff;\n  border: none;\n  border-radius: 25rpx;\n  padding: 20rpx 40rpx;\n  font-size: 28rpx;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./favorites.vue?vue&type=style&index=0&id=edb94f68&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./favorites.vue?vue&type=style&index=0&id=edb94f68&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751999946\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}