
.house-list-container.data-v-145608e6 {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  overflow-x: hidden;
}
.filter-bar.data-v-145608e6 {
  background: #fff;
  padding: 20rpx 16rpx;
  display: flex;
  border-bottom: 1rpx solid #e9ecef;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
}
.filter-item.data-v-145608e6 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 12rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  position: relative;
}
.filter-item.active.data-v-145608e6 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}
.filter-item.active .filter-text.data-v-145608e6 {
  color: #fff;
}
.filter-text.data-v-145608e6 {
  font-size: 28rpx;
  color: #333;
  margin-right: 8rpx;
  font-weight: 500;
}
.house-list.data-v-145608e6 {
  flex: 1;
  padding: 20rpx 20rpx;
  box-sizing: border-box;
}
.house-item.data-v-145608e6 {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
  width: 100%;
  box-sizing: border-box;
}
.house-item.data-v-145608e6:active {
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.house-image.data-v-145608e6 {
  position: relative;
  height: 320rpx;
  overflow: hidden;
}
.house-image image.data-v-145608e6 {
  width: 100%;
  height: 100%;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.house-item:active .house-image image.data-v-145608e6 {
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
}
.house-type.data-v-145608e6 {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}
.favorite-btn.data-v-145608e6 {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}
.favorite-btn.data-v-145608e6:active {
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
  background: rgba(255, 107, 107, 0.8);
}
.house-info.data-v-145608e6 {
  padding: 20rpx;
  overflow: hidden;
  box-sizing: border-box;
}
.house-title.data-v-145608e6 {
  display: block;
  font-size: 34rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}
.house-desc.data-v-145608e6 {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.5;
}
.house-tags.data-v-145608e6 {
  display: flex;
  gap: 8rpx;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}
.tag.data-v-145608e6 {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #495057;
  padding: 8rpx 12rpx;
  border-radius: 14rpx;
  font-size: 20rpx;
  font-weight: 500;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
  flex-shrink: 0;
}
.tag.data-v-145608e6:first-child {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border-color: #667eea;
}
.house-location.data-v-145608e6 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 10rpx 12rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  border-left: 3rpx solid #667eea;
}
.location-text.data-v-145608e6 {
  font-size: 24rpx;
  color: #666;
  margin-left: 8rpx;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.house-bottom.data-v-145608e6 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}
.price-info.data-v-145608e6 {
  display: flex;
  align-items: baseline;
}
.price.data-v-145608e6 {
  font-size: 40rpx;
  font-weight: 700;
  color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.price-unit.data-v-145608e6 {
  font-size: 24rpx;
  color: #999;
  margin-left: 8rpx;
  font-weight: 500;
}
.house-stats.data-v-145608e6 {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}
.stat-item.data-v-145608e6 {
  font-size: 20rpx;
  color: #999;
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 4rpx 8rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  flex-shrink: 0;
  white-space: nowrap;
}
.stat-item.data-v-145608e6:active {
  background: #e9ecef;
}
.load-status.data-v-145608e6 {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
  background: #f8f9fa;
  margin: 20rpx;
  border-radius: 16rpx;
}
.empty-state.data-v-145608e6 {
  text-align: center;
  padding: 120rpx 40rpx;
  background: #fff;
  margin: 20rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.empty-state image.data-v-145608e6 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}
.empty-text.data-v-145608e6 {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 12rpx;
}
.publish-btn.data-v-145608e6 {
  position: fixed;
  right: 40rpx;
  bottom: 120rpx;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  z-index: 100;
}
.publish-btn.data-v-145608e6:active {
  -webkit-transform: scale(0.95) translateY(-2rpx);
          transform: scale(0.95) translateY(-2rpx);
  box-shadow: 0 16rpx 50rpx rgba(102, 126, 234, 0.5);
}

/* 筛选选项展开容器 */
.filter-options-container.data-v-145608e6 {
  background: #fff;
  border-bottom: 1rpx solid #e9ecef;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 99;
}
.filter-options.data-v-145608e6 {
  padding: 32rpx 24rpx;
}
.options-title.data-v-145608e6 {
  font-size: 30rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 24rpx;
  position: relative;
  padding-left: 16rpx;
}
.options-title.data-v-145608e6::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 4rpx;
  height: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2rpx;
}

/* 网格布局（用于类型和价格） */
.options-grid.data-v-145608e6 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.option-item.data-v-145608e6 {
  flex: 1;
  min-width: 0;
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.option-item.data-v-145608e6::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}
.option-item.data-v-145608e6:active::before {
  left: 100%;
}
.option-item.selected.data-v-145608e6 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: #fff;
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
}
.option-item .option-text.data-v-145608e6 {
  font-size: 26rpx;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.option-item.selected .option-text.data-v-145608e6 {
  color: #fff;
}

/* 列表布局（用于排序） */
.options-list.data-v-145608e6 {
  display: flex;
  flex-direction: column;
}
.option-item-list.data-v-145608e6 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.option-item-list.data-v-145608e6:last-child {
  border-bottom: none;
}
.option-item-list.selected.data-v-145608e6 {
  background: #f0f8ff;
  margin: 0 -40rpx;
  padding: 25rpx 40rpx;
  border-radius: 8rpx;
}
.option-item-list .option-text.data-v-145608e6 {
  font-size: 28rpx;
  color: #333;
}
.option-item-list.selected .option-text.data-v-145608e6 {
  color: #007aff;
  font-weight: 500;
}

/* 筛选栏激活状态 */
.filter-item.active.data-v-145608e6 {
  background: #f0f8ff;
}
.filter-item.active .filter-text.data-v-145608e6 {
  color: #007aff;
}

/* 价格输入样式 */
.price-input-container.data-v-145608e6 {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.price-input-group.data-v-145608e6 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}
.input-label.data-v-145608e6 {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.price-input.data-v-145608e6 {
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fff;
  text-align: center;
}
.price-input.data-v-145608e6:focus {
  border-color: #007aff;
}
.price-unit.data-v-145608e6 {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  margin-top: 5rpx;
}
.price-separator.data-v-145608e6 {
  font-size: 32rpx;
  color: #666;
  margin-top: 30rpx;
  font-weight: bold;
}
.price-actions.data-v-145608e6 {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}
.price-btn.data-v-145608e6 {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
.reset-btn.data-v-145608e6 {
  background: #f5f5f5;
  color: #666;
}
.confirm-btn.data-v-145608e6 {
  background: #007aff;
  color: #fff;
}
.price-btn.data-v-145608e6:active {
  opacity: 0.8;
}

