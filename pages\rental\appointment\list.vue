<template>
  <view class="appointment-list">
    <!-- 搜索和筛选 -->
    <view class="search-section">
      <view class="search-row">
        <view class="search-item">
          <uni-easyinput 
            v-model="searchKeyword" 
            placeholder="搜索房源标题、预约人"
            @confirm="onSearch"
            clearable
          >
            <template #right>
              <uni-icons type="search" size="18" color="#999" @click="onSearch" />
            </template>
          </uni-easyinput>
        </view>
        
        <view class="filter-item">
          <uni-data-picker 
            v-model="filters.status" 
            :localdata="statusOptions"
            placeholder="预约状态"
            @change="onFilterChange"
          />
        </view>
        
        <view class="filter-item">
          <uni-datetime-picker 
            v-model="filters.dateRange" 
            type="daterange"
            placeholder="预约时间"
            @change="onFilterChange"
          />
        </view>
        
        <button class="reset-btn" @click="resetFilters">重置</button>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-bar">
      <view class="stat-item">
        <text class="stat-label">总预约</text>
        <text class="stat-value">{{ statistics.total }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">待确认</text>
        <text class="stat-value pending">{{ statistics.pending }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">已确认</text>
        <text class="stat-value confirmed">{{ statistics.confirmed }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">已完成</text>
        <text class="stat-value completed">{{ statistics.completed }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">今日预约</text>
        <text class="stat-value">{{ statistics.today }}</text>
      </view>
    </view>

    <!-- 预约列表 -->
    <view class="list-section">
      <uni-table 
        :loading="loading"
        border
        stripe
        emptyText="暂无预约数据"
      >
        <uni-tr>
          <uni-th width="80" align="center">
            <checkbox @change="onSelectAll" :checked="isAllSelected" />
          </uni-th>
          <uni-th width="200" align="center">房源信息</uni-th>
          <uni-th width="150" align="center">预约人信息</uni-th>
          <uni-th width="120" align="center">联系方式</uni-th>
          <uni-th width="150" align="center">预约时间</uni-th>
          <uni-th width="100" align="center">状态</uni-th>
          <uni-th width="120" align="center">创建时间</uni-th>
          <uni-th width="200" align="center">操作</uni-th>
        </uni-tr>
        
        <uni-tr v-for="(appointment, index) in appointmentList" :key="appointment._id">
          <uni-td align="center">
            <checkbox 
              :checked="selectedIds.includes(appointment._id)"
              @change="onSelectItem(appointment._id)"
            />
          </uni-td>
          
          <uni-td>
            <view class="house-info">
              <image 
                class="house-cover" 
                :src="appointment.house_info?.images?.[0] || '/static/placeholder.png'"
                mode="aspectFill"
                @click="viewHouse(appointment.house_id)"
              />
              <view class="house-details">
                <text class="house-title" @click="viewHouse(appointment.house_id)">
                  {{ appointment.house_info?.title || '房源已删除' }}
                </text>
                <text class="house-price">¥{{ appointment.house_info?.price }}/月</text>
                <text class="house-address">{{ appointment.house_info?.location?.address }}</text>
              </view>
            </view>
          </uni-td>
          
          <uni-td>
            <view class="user-info">
              <image 
                class="user-avatar" 
                :src="appointment.user_info?.avatar || '/static/default-avatar.png'"
                mode="aspectFill"
                @click="viewUser(appointment.user_id)"
              />
              <view class="user-details">
                <text class="user-nickname" @click="viewUser(appointment.user_id)">
                  {{ appointment.user_info?.nickname || '用户已删除' }}
                </text>
                <text class="user-id">ID: {{ appointment.user_id?.slice(-8) }}</text>
              </view>
            </view>
          </uni-td>
          
          <uni-td>
            <view class="contact-info">
              <text class="contact-name">{{ appointment.contact_name }}</text>
              <text class="contact-phone" @click="callPhone(appointment.contact_phone)">
                {{ appointment.contact_phone }}
              </text>
            </view>
          </uni-td>
          
          <uni-td align="center">
            <text class="appointment-time">{{ formatDateTime(appointment.appointment_time) }}</text>
          </uni-td>
          
          <uni-td align="center">
            <uni-tag 
              :text="getStatusText(appointment.status)" 
              :type="getStatusType(appointment.status)"
            />
          </uni-td>
          
          <uni-td align="center">
            <text class="create-time">{{ formatDateTime(appointment.create_time) }}</text>
          </uni-td>
          
          <uni-td align="center">
            <view class="action-buttons">
              <button 
                v-if="appointment.status === 'pending'"
                class="action-btn confirm-btn" 
                @click="confirmAppointment(appointment)"
              >
                确认
              </button>
              <button 
                v-if="appointment.status === 'confirmed'"
                class="action-btn complete-btn" 
                @click="completeAppointment(appointment)"
              >
                完成
              </button>
              <button 
                v-if="['pending', 'confirmed'].includes(appointment.status)"
                class="action-btn cancel-btn" 
                @click="cancelAppointment(appointment)"
              >
                取消
              </button>
              <button 
                class="action-btn delete-btn" 
                @click="deleteAppointment(appointment)"
              >
                删除
              </button>
            </view>
          </uni-td>
        </uni-tr>
      </uni-table>
      
      <!-- 批量操作 -->
      <view class="batch-actions" v-if="selectedIds.length > 0">
        <text class="selected-count">已选择 {{ selectedIds.length }} 项</text>
        <button class="batch-btn confirm-btn" @click="batchConfirm">批量确认</button>
        <button class="batch-btn cancel-btn" @click="batchCancel">批量取消</button>
        <button class="batch-btn delete-btn" @click="batchDelete">批量删除</button>
      </view>
      
      <!-- 分页 -->
      <view class="pagination">
        <uni-pagination 
          :current="currentPage"
          :total="totalCount"
          :pageSize="pageSize"
          @change="onPageChange"
        />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'AppointmentList',
  data() {
    return {
      loading: false,
      searchKeyword: '',
      
      // 筛选条件
      filters: {
        status: '',
        dateRange: []
      },
      
      // 筛选选项
      statusOptions: [
        { value: '', text: '全部状态' },
        { value: 'pending', text: '待确认' },
        { value: 'confirmed', text: '已确认' },
        { value: 'completed', text: '已完成' },
        { value: 'cancelled', text: '已取消' }
      ],
      
      // 统计数据
      statistics: {
        total: 0,
        pending: 0,
        confirmed: 0,
        completed: 0,
        today: 0
      },
      
      // 列表数据
      appointmentList: [],
      selectedIds: [],
      isAllSelected: false,
      
      // 分页
      currentPage: 1,
      pageSize: 10,
      totalCount: 0
    }
  },

  onLoad(options) {
    // 支持从其他页面传入筛选条件
    if (options.user_id) {
      this.filters.user_id = options.user_id
    }
    if (options.house_id) {
      this.filters.house_id = options.house_id
    }
    
    this.loadAppointmentList()
    this.loadStatistics()
  },

  onPullDownRefresh() {
    this.currentPage = 1
    this.loadAppointmentList().then(() => {
      uni.stopPullDownRefresh()
    })
  },

  methods: {
    // 加载预约列表
    async loadAppointmentList() {
      try {
        this.loading = true
        
        const result = await uniCloud.callFunction({
          name: 'appointment-management',
          data: {
            action: 'getAppointmentListForAdmin',
            page: this.currentPage,
            pageSize: this.pageSize,
            keyword: this.searchKeyword,
            filters: this.filters
          }
        })

        if (result.result.code === 0) {
          this.appointmentList = result.result.data.list
          this.totalCount = result.result.data.total
        } else {
          uni.showToast({
            title: result.result.message,
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('加载预约列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 加载统计数据
    async loadStatistics() {
      try {
        const result = await uniCloud.callFunction({
          name: 'system-management',
          data: {
            action: 'getAppointmentStatistics'
          }
        })

        if (result.result.code === 0) {
          this.statistics = result.result.data
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    // 搜索
    onSearch() {
      this.currentPage = 1
      this.loadAppointmentList()
    },

    // 筛选改变
    onFilterChange() {
      this.currentPage = 1
      this.loadAppointmentList()
    },

    // 重置筛选
    resetFilters() {
      this.searchKeyword = ''
      this.filters = {
        status: '',
        dateRange: []
      }
      this.currentPage = 1
      this.loadAppointmentList()
    },

    // 全选/取消全选
    onSelectAll(e) {
      this.isAllSelected = e.detail.value
      if (this.isAllSelected) {
        this.selectedIds = this.appointmentList.map(item => item._id)
      } else {
        this.selectedIds = []
      }
    },

    // 选择单项
    onSelectItem(id) {
      const index = this.selectedIds.indexOf(id)
      if (index > -1) {
        this.selectedIds.splice(index, 1)
      } else {
        this.selectedIds.push(id)
      }
      
      this.isAllSelected = this.selectedIds.length === this.appointmentList.length
    },

    // 查看房源
    viewHouse(houseId) {
      if (houseId) {
        uni.navigateTo({
          url: `/pages/rental/house/detail?id=${houseId}`
        })
      }
    },

    // 查看用户
    viewUser(userId) {
      if (userId) {
        uni.navigateTo({
          url: `/pages/rental/user/detail?id=${userId}`
        })
      }
    },

    // 拨打电话
    callPhone(phone) {
      if (phone) {
        uni.makePhoneCall({
          phoneNumber: phone
        })
      }
    },

    // 确认预约
    async confirmAppointment(appointment) {
      uni.showModal({
        title: '确认预约',
        content: `确定要确认这个预约吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await uniCloud.callFunction({
                name: 'appointment-management',
                data: {
                  action: 'confirmAppointment',
                  appointment_id: appointment._id
                }
              })

              if (result.result.code === 0) {
                uni.showToast({
                  title: '确认成功',
                  icon: 'success'
                })
                this.loadAppointmentList()
                this.loadStatistics()
              } else {
                uni.showToast({
                  title: result.result.message,
                  icon: 'none'
                })
              }
            } catch (error) {
              console.error('确认预约失败:', error)
              uni.showToast({
                title: '确认失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    // 完成预约
    async completeAppointment(appointment) {
      uni.showModal({
        title: '完成预约',
        content: `确定要标记这个预约为已完成吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await uniCloud.callFunction({
                name: 'appointment-management',
                data: {
                  action: 'completeAppointment',
                  appointment_id: appointment._id
                }
              })

              if (result.result.code === 0) {
                uni.showToast({
                  title: '操作成功',
                  icon: 'success'
                })
                this.loadAppointmentList()
                this.loadStatistics()
              } else {
                uni.showToast({
                  title: result.result.message,
                  icon: 'none'
                })
              }
            } catch (error) {
              console.error('完成预约失败:', error)
              uni.showToast({
                title: '操作失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    // 取消预约
    async cancelAppointment(appointment) {
      uni.showModal({
        title: '取消预约',
        content: `确定要取消这个预约吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await uniCloud.callFunction({
                name: 'appointment-management',
                data: {
                  action: 'cancelAppointment',
                  appointment_id: appointment._id
                }
              })

              if (result.result.code === 0) {
                uni.showToast({
                  title: '取消成功',
                  icon: 'success'
                })
                this.loadAppointmentList()
                this.loadStatistics()
              } else {
                uni.showToast({
                  title: result.result.message,
                  icon: 'none'
                })
              }
            } catch (error) {
              console.error('取消预约失败:', error)
              uni.showToast({
                title: '取消失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    // 删除预约
    deleteAppointment(appointment) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除这个预约记录吗？此操作不可恢复！`,
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await uniCloud.callFunction({
                name: 'appointment-management',
                data: {
                  action: 'deleteAppointment',
                  appointment_id: appointment._id
                }
              })

              if (result.result.code === 0) {
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                })
                this.loadAppointmentList()
                this.loadStatistics()
              } else {
                uni.showToast({
                  title: result.result.message,
                  icon: 'none'
                })
              }
            } catch (error) {
              console.error('删除预约失败:', error)
              uni.showToast({
                title: '删除失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    // 批量确认
    batchConfirm() {
      uni.showModal({
        title: '批量确认',
        content: `确定要确认选中的 ${this.selectedIds.length} 个预约吗？`,
        success: async (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '批量确认功能开发中',
              icon: 'none'
            })
          }
        }
      })
    },

    // 批量取消
    batchCancel() {
      uni.showModal({
        title: '批量取消',
        content: `确定要取消选中的 ${this.selectedIds.length} 个预约吗？`,
        success: async (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '批量取消功能开发中',
              icon: 'none'
            })
          }
        }
      })
    },

    // 批量删除
    batchDelete() {
      uni.showModal({
        title: '批量删除',
        content: `确定要删除选中的 ${this.selectedIds.length} 个预约吗？此操作不可恢复！`,
        success: async (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '批量删除功能开发中',
              icon: 'none'
            })
          }
        }
      })
    },

    // 分页改变
    onPageChange(page) {
      this.currentPage = page
      this.loadAppointmentList()
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'pending': '待确认',
        'confirmed': '已确认',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusMap[status] || '未知'
    },

    // 获取状态类型
    getStatusType(status) {
      const typeMap = {
        'pending': 'warning',
        'confirmed': 'primary',
        'completed': 'success',
        'cancelled': 'error'
      }
      return typeMap[status] || 'default'
    },

    // 格式化日期时间
    formatDateTime(date) {
      if (!date) return '-'
      const d = new Date(date)
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}`
    }
  }
}
</script>

<style lang="scss" scoped>
.appointment-list {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.search-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.search-row {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex-wrap: wrap;
}

.search-item {
  flex: 1;
  min-width: 300rpx;
}

.filter-item {
  min-width: 200rpx;
}

.reset-btn {
  background: #8E8E93;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.stats-bar {
  display: flex;
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  gap: 60rpx;
}

.stat-item {
  text-align: center;
  
  .stat-label {
    display: block;
    font-size: 28rpx;
    color: #666;
    margin-bottom: 10rpx;
  }
  
  .stat-value {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    
    &.pending {
      color: #FF9500;
    }
    
    &.confirmed {
      color: #007AFF;
    }
    
    &.completed {
      color: #34C759;
    }
  }
}

.list-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.house-info {
  display: flex;
  gap: 15rpx;
  align-items: center;
  
  .house-cover {
    width: 80rpx;
    height: 80rpx;
    border-radius: 8rpx;
    cursor: pointer;
  }
  
  .house-details {
    flex: 1;
    
    .house-title {
      display: block;
      font-size: 26rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 6rpx;
      cursor: pointer;
      
      &:hover {
        color: #007AFF;
      }
    }
    
    .house-price {
      display: block;
      font-size: 24rpx;
      color: #FF3B30;
      font-weight: 600;
      margin-bottom: 6rpx;
    }
    
    .house-address {
      font-size: 22rpx;
      color: #666;
    }
  }
}

.user-info {
  display: flex;
  gap: 15rpx;
  align-items: center;
  
  .user-avatar {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    cursor: pointer;
  }
  
  .user-details {
    flex: 1;
    
    .user-nickname {
      display: block;
      font-size: 26rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 6rpx;
      cursor: pointer;
      
      &:hover {
        color: #007AFF;
      }
    }
    
    .user-id {
      font-size: 22rpx;
      color: #666;
    }
  }
}

.contact-info {
  .contact-name {
    display: block;
    font-size: 26rpx;
    color: #333;
    margin-bottom: 6rpx;
  }
  
  .contact-phone {
    font-size: 24rpx;
    color: #007AFF;
    text-decoration: underline;
    cursor: pointer;
  }
}

.appointment-time, .create-time {
  font-size: 24rpx;
  color: #666;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.action-btn {
  padding: 8rpx 16rpx;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
  cursor: pointer;
  
  &.confirm-btn {
    background: #34C759;
    color: #fff;
  }
  
  &.complete-btn {
    background: #007AFF;
    color: #fff;
  }
  
  &.cancel-btn {
    background: #FF9500;
    color: #fff;
  }
  
  &.delete-btn {
    background: #FF3B30;
    color: #fff;
  }
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-top: 30rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
}

.selected-count {
  font-size: 28rpx;
  color: #333;
}

.batch-btn {
  padding: 12rpx 24rpx;
  border: none;
  border-radius: 6rpx;
  font-size: 26rpx;
  cursor: pointer;
  
  &.confirm-btn {
    background: #34C759;
    color: #fff;
  }
  
  &.cancel-btn {
    background: #FF9500;
    color: #fff;
  }
  
  &.delete-btn {
    background: #FF3B30;
    color: #fff;
  }
}

.pagination {
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .search-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .stats-bar {
    flex-wrap: wrap;
    gap: 30rpx;
  }
  
  .house-info, .user-info {
    flex-direction: column;
    text-align: center;
  }
  
  .action-buttons {
    flex-direction: row;
    flex-wrap: wrap;
  }
}
</style>
