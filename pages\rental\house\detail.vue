<template>
  <view class="house-detail">
    <view v-if="loading" class="loading">
      <uni-load-more status="loading" />
    </view>
    
    <view v-else-if="houseInfo" class="detail-content">
      <!-- 房源基本信息 -->
      <view class="info-section">
        <view class="section-header">
          <text class="section-title">房源基本信息</text>
          <view class="status-badges">
            <uni-tag 
              :text="getStatusText(houseInfo.status)" 
              :type="getStatusType(houseInfo.status)"
            />
            <uni-tag 
              :text="houseInfo.is_verified ? '已审核' : '待审核'" 
              :type="houseInfo.is_verified ? 'success' : 'warning'"
            />
          </view>
        </view>
        
        <view class="info-grid">
          <view class="info-item">
            <text class="info-label">房源标题</text>
            <text class="info-value">{{ houseInfo.title }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">房源类型</text>
            <text class="info-value">{{ houseInfo.type }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">租金</text>
            <text class="info-value price">¥{{ houseInfo.price }}/月</text>
          </view>
          <view class="info-item">
            <text class="info-label">押金</text>
            <text class="info-value">¥{{ houseInfo.deposit }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">面积</text>
            <text class="info-value">{{ houseInfo.area }}㎡</text>
          </view>
          <view class="info-item">
            <text class="info-label">房间配置</text>
            <text class="info-value">{{ houseInfo.room_count }}室{{ houseInfo.hall_count }}厅{{ houseInfo.bathroom_count }}卫</text>
          </view>
          <view class="info-item">
            <text class="info-label">楼层</text>
            <text class="info-value">{{ houseInfo.floor }}/{{ houseInfo.total_floors }}层</text>
          </view>
          <view class="info-item">
            <text class="info-label">朝向</text>
            <text class="info-value">{{ houseInfo.orientation }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">装修情况</text>
            <text class="info-value">{{ houseInfo.decoration }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">浏览次数</text>
            <text class="info-value">{{ houseInfo.view_count || 0 }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">收藏次数</text>
            <text class="info-value">{{ houseInfo.favorite_count || 0 }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">发布时间</text>
            <text class="info-value">{{ formatDateTime(houseInfo.publish_date) }}</text>
          </view>
        </view>
        
        <view class="info-item full-width">
          <text class="info-label">房源描述</text>
          <text class="info-value description">{{ houseInfo.description }}</text>
        </view>
      </view>

      <!-- 位置信息 -->
      <view class="info-section">
        <view class="section-header">
          <text class="section-title">位置信息</text>
        </view>
        
        <view class="info-grid">
          <view class="info-item">
            <text class="info-label">省份</text>
            <text class="info-value">{{ houseInfo.location?.province }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">城市</text>
            <text class="info-value">{{ houseInfo.location?.city }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">区县</text>
            <text class="info-value">{{ houseInfo.location?.district }}</text>
          </view>
          <view class="info-item full-width">
            <text class="info-label">详细地址</text>
            <text class="info-value">{{ houseInfo.location?.address }}</text>
          </view>
        </view>
      </view>

      <!-- 房源图片 -->
      <view class="info-section">
        <view class="section-header">
          <text class="section-title">房源图片</text>
        </view>
        
        <view class="image-grid">
          <view 
            v-for="(image, index) in houseInfo.images" 
            :key="index"
            class="image-item"
            @click="previewImage(index)"
          >
            <image :src="image" mode="aspectFill" class="house-image" />
          </view>
        </view>
      </view>

      <!-- 设施配套 -->
      <view class="info-section">
        <view class="section-header">
          <text class="section-title">设施配套</text>
        </view>
        
        <view class="facilities-list">
          <view 
            v-for="(facility, index) in houseInfo.facilities" 
            :key="index"
            class="facility-item"
          >
            <uni-icons type="checkmarkempty" size="16" color="#34C759" />
            <text class="facility-text">{{ facility }}</text>
          </view>
        </view>
      </view>

      <!-- 联系方式 -->
      <view class="info-section">
        <view class="section-header">
          <text class="section-title">联系方式</text>
        </view>
        
        <view class="info-grid">
          <view class="info-item">
            <text class="info-label">联系人</text>
            <text class="info-value">{{ houseInfo.contact?.name }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">联系电话</text>
            <text class="info-value phone" @click="callPhone(houseInfo.contact?.phone)">{{ houseInfo.contact?.phone }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">微信号</text>
            <text class="info-value">{{ houseInfo.contact?.wechat || '未提供' }}</text>
          </view>
        </view>
      </view>

      <!-- 发布者信息 -->
      <view class="info-section">
        <view class="section-header">
          <text class="section-title">发布者信息</text>
        </view>
        
        <view class="publisher-info">
          <image 
            :src="houseInfo.publisher_info?.avatar || '/static/default-avatar.png'" 
            class="publisher-avatar"
          />
          <view class="publisher-details">
            <text class="publisher-name">{{ houseInfo.publisher_info?.nickname || '未知用户' }}</text>
            <text class="publisher-id">用户ID: {{ houseInfo.publisher_id }}</text>
            <button class="view-publisher-btn" @click="viewPublisher">查看用户详情</button>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-section">
        <button 
          v-if="!houseInfo.is_verified"
          class="action-btn approve-btn" 
          @click="approveHouse"
        >
          审核通过
        </button>
        <button 
          v-if="!houseInfo.is_verified"
          class="action-btn reject-btn" 
          @click="rejectHouse"
        >
          审核拒绝
        </button>
        <button 
          v-if="houseInfo.status === 'available'"
          class="action-btn offline-btn" 
          @click="offlineHouse"
        >
          下线房源
        </button>
        <button 
          v-if="houseInfo.status === 'offline'"
          class="action-btn online-btn" 
          @click="onlineHouse"
        >
          上线房源
        </button>
        <button class="action-btn edit-btn" @click="editHouse">编辑房源</button>
        <button class="action-btn delete-btn" @click="deleteHouse">删除房源</button>
      </view>
    </view>
    
    <view v-else class="error-state">
      <text>房源信息加载失败</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'HouseDetail',
  data() {
    return {
      loading: true,
      houseId: '',
      houseInfo: null
    }
  },

  onLoad(options) {
    this.houseId = options.id
    if (this.houseId) {
      this.loadHouseDetail()
    }
  },

  methods: {
    // 加载房源详情
    async loadHouseDetail() {
      try {
        this.loading = true
        
        const result = await uniCloud.callFunction({
          name: 'house-management',
          data: {
            action: 'getHouseDetailForAdmin',
            house_id: this.houseId
          }
        })

        if (result.result.code === 0) {
          this.houseInfo = result.result.data
        } else {
          uni.showToast({
            title: result.result.message,
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('加载房源详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 审核通过
    async approveHouse() {
      uni.showModal({
        title: '确认审核',
        content: '确定要审核通过这个房源吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await uniCloud.callFunction({
                name: 'house-management',
                data: {
                  action: 'approveHouse',
                  house_id: this.houseId
                }
              })

              if (result.result.code === 0) {
                uni.showToast({
                  title: '审核成功',
                  icon: 'success'
                })
                this.loadHouseDetail()
              } else {
                uni.showToast({
                  title: result.result.message,
                  icon: 'none'
                })
              }
            } catch (error) {
              console.error('审核失败:', error)
              uni.showToast({
                title: '审核失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    // 审核拒绝
    rejectHouse() {
      uni.showModal({
        title: '审核拒绝',
        content: '确定要拒绝这个房源吗？',
        success: async (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '审核拒绝功能开发中',
              icon: 'none'
            })
          }
        }
      })
    },

    // 下线房源
    offlineHouse() {
      uni.showModal({
        title: '下线房源',
        content: '确定要下线这个房源吗？',
        success: async (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '下线功能开发中',
              icon: 'none'
            })
          }
        }
      })
    },

    // 上线房源
    onlineHouse() {
      uni.showModal({
        title: '上线房源',
        content: '确定要上线这个房源吗？',
        success: async (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '上线功能开发中',
              icon: 'none'
            })
          }
        }
      })
    },

    // 编辑房源
    editHouse() {
      uni.showToast({
        title: '编辑功能开发中',
        icon: 'none'
      })
    },

    // 删除房源
    deleteHouse() {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个房源吗？此操作不可恢复！',
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await uniCloud.callFunction({
                name: 'house-management',
                data: {
                  action: 'deleteHouse',
                  house_id: this.houseId
                }
              })

              if (result.result.code === 0) {
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                })
                setTimeout(() => {
                  uni.navigateBack()
                }, 1500)
              } else {
                uni.showToast({
                  title: result.result.message,
                  icon: 'none'
                })
              }
            } catch (error) {
              console.error('删除失败:', error)
              uni.showToast({
                title: '删除失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    // 查看发布者
    viewPublisher() {
      uni.navigateTo({
        url: `/pages/rental/user/detail?id=${this.houseInfo.publisher_id}`
      })
    },

    // 预览图片
    previewImage(index) {
      uni.previewImage({
        urls: this.houseInfo.images,
        current: index
      })
    },

    // 拨打电话
    callPhone(phone) {
      if (phone) {
        uni.makePhoneCall({
          phoneNumber: phone
        })
      }
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'available': '可租',
        'rented': '已租',
        'offline': '下线'
      }
      return statusMap[status] || '未知'
    },

    // 获取状态类型
    getStatusType(status) {
      const typeMap = {
        'available': 'success',
        'rented': 'warning',
        'offline': 'error'
      }
      return typeMap[status] || 'default'
    },

    // 格式化日期时间
    formatDateTime(date) {
      if (!date) return '-'
      const d = new Date(date)
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}`
    }
  }
}
</script>

<style lang="scss" scoped>
.house-detail {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.detail-content {
  .info-section {
    background: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
  
  .section-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
  
  .status-badges {
    display: flex;
    gap: 10rpx;
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300rpx, 1fr));
  gap: 30rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  
  &.full-width {
    grid-column: 1 / -1;
  }
  
  .info-label {
    font-size: 28rpx;
    color: #666;
  }
  
  .info-value {
    font-size: 32rpx;
    color: #333;
    
    &.price {
      color: #FF3B30;
      font-weight: 600;
    }
    
    &.phone {
      color: #007AFF;
      text-decoration: underline;
      cursor: pointer;
    }
    
    &.description {
      line-height: 1.6;
      white-space: pre-wrap;
    }
  }
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200rpx, 1fr));
  gap: 20rpx;
}

.image-item {
  aspect-ratio: 1;
  border-radius: 8rpx;
  overflow: hidden;
  cursor: pointer;
  
  .house-image {
    width: 100%;
    height: 100%;
  }
}

.facilities-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: 20rpx;
}

.facility-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
  
  .facility-text {
    font-size: 28rpx;
    color: #333;
  }
}

.publisher-info {
  display: flex;
  align-items: center;
  gap: 30rpx;
  
  .publisher-avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
  }
  
  .publisher-details {
    flex: 1;
    
    .publisher-name {
      display: block;
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 10rpx;
    }
    
    .publisher-id {
      display: block;
      font-size: 24rpx;
      color: #666;
      margin-bottom: 20rpx;
    }
    
    .view-publisher-btn {
      background: #007AFF;
      color: #fff;
      border: none;
      border-radius: 8rpx;
      padding: 16rpx 32rpx;
      font-size: 28rpx;
    }
  }
}

.action-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  min-width: 150rpx;
  padding: 20rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  cursor: pointer;
  
  &.approve-btn {
    background: #34C759;
    color: #fff;
  }
  
  &.reject-btn {
    background: #FF3B30;
    color: #fff;
  }
  
  &.offline-btn {
    background: #8E8E93;
    color: #fff;
  }
  
  &.online-btn {
    background: #34C759;
    color: #fff;
  }
  
  &.edit-btn {
    background: #007AFF;
    color: #fff;
  }
  
  &.delete-btn {
    background: #FF3B30;
    color: #fff;
  }
}

.error-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  font-size: 32rpx;
  color: #666;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .image-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .facilities-list {
    grid-template-columns: 1fr;
  }
  
  .action-section {
    flex-direction: column;
  }
  
  .publisher-info {
    flex-direction: column;
    text-align: center;
  }
}
</style>
