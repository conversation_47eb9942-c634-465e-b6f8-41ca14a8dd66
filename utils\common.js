// 通用工具函数

/**
 * 格式化时间
 * @param {Date|String|Number} time 时间
 * @param {String} format 格式 默认 'YYYY-MM-DD HH:mm:ss'
 */
export function formatTime(time, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!time) return '';
  
  const date = new Date(time);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hour = String(date.getHours()).padStart(2, '0');
  const minute = String(date.getMinutes()).padStart(2, '0');
  const second = String(date.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hour)
    .replace('mm', minute)
    .replace('ss', second);
}

/**
 * 格式化相对时间
 * @param {Date|String|Number} time 时间
 */
export function formatRelativeTime(time) {
  if (!time) return '';
  
  const now = new Date();
  const target = new Date(time);
  const diff = now - target;
  
  const minute = 60 * 1000;
  const hour = 60 * minute;
  const day = 24 * hour;
  const month = 30 * day;
  
  if (diff < minute) {
    return '刚刚';
  } else if (diff < hour) {
    return Math.floor(diff / minute) + '分钟前';
  } else if (diff < day) {
    return Math.floor(diff / hour) + '小时前';
  } else if (diff < month) {
    return Math.floor(diff / day) + '天前';
  } else {
    return formatTime(time, 'YYYY-MM-DD');
  }
}

/**
 * 格式化价格
 * @param {Number} price 价格
 */
export function formatPrice(price) {
  if (!price && price !== 0) return '';
  return price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/**
 * 验证手机号
 * @param {String} phone 手机号
 */
export function validatePhone(phone) {
  const reg = /^1[3-9]\d{9}$/;
  return reg.test(phone);
}

/**
 * 验证邮箱
 * @param {String} email 邮箱
 */
export function validateEmail(email) {
  const reg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return reg.test(email);
}

/**
 * 验证身份证号
 * @param {String} idCard 身份证号
 */
export function validateIdCard(idCard) {
  const reg = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
  return reg.test(idCard);
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {Number} delay 延迟时间
 */
export function debounce(func, delay = 300) {
  let timer;
  return function(...args) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {Number} delay 延迟时间
 */
export function throttle(func, delay = 300) {
  let timer;
  return function(...args) {
    if (!timer) {
      timer = setTimeout(() => {
        func.apply(this, args);
        timer = null;
      }, delay);
    }
  };
}

/**
 * 深拷贝
 * @param {Any} obj 要拷贝的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj);
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  if (typeof obj === 'object') {
    const clonedObj = {};
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
}

/**
 * 获取图片完整URL
 * @param {String} url 图片路径
 */
export function getImageUrl(url) {
  if (!url) return '';
  if (url.startsWith('http')) return url;
  if (url.startsWith('/')) return url;
  return '/' + url;
}

/**
 * 计算距离
 * @param {Number} lat1 纬度1
 * @param {Number} lng1 经度1
 * @param {Number} lat2 纬度2
 * @param {Number} lng2 经度2
 */
export function calculateDistance(lat1, lng1, lat2, lng2) {
  const R = 6371; // 地球半径（公里）
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;
  
  if (distance < 1) {
    return Math.round(distance * 1000) + 'm';
  } else {
    return Math.round(distance * 10) / 10 + 'km';
  }
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return uni.getStorageSync('userInfo') || {};
}

/**
 * 设置用户信息
 * @param {Object} userInfo 用户信息
 */
export function setUserInfo(userInfo) {
  uni.setStorageSync('userInfo', userInfo);
}

/**
 * 清除用户信息
 */
export function clearUserInfo() {
  uni.removeStorageSync('uni_id_token');
  uni.removeStorageSync('userInfo');
}

/**
 * 检查登录状态
 */
export function checkLogin() {
  const token = uni.getStorageSync('uni_id_token');
  return !!token;
}

/**
 * 检查登录状态，如果未登录则跳转到登录页
 * @param {string} redirectUrl 登录成功后的重定向地址
 * @returns {boolean} 是否已登录
 */
export function requireLogin(redirectUrl) {
  if (checkLogin()) {
    return true;
  }

  // 获取当前页面路径作为默认重定向地址
  if (!redirectUrl) {
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      redirectUrl = '/' + currentPage.route;

      // 如果有参数，也要带上
      const options = currentPage.options;
      if (options && Object.keys(options).length > 0) {
        const params = Object.keys(options).map(key => `${key}=${options[key]}`).join('&');
        redirectUrl += '?' + params;
      }
    }
  }

  // 跳转到登录页
  uni.navigateTo({
    url: '/pages/login/login?redirect=' + encodeURIComponent(redirectUrl)
  });

  return false;
}

/**
 * 跳转到登录页
 */
export function toLogin() {
  uni.navigateTo({
    url: '/pages/login/login'
  });
}
