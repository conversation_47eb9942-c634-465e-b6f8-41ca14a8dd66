<view class="login-container data-v-b237504c"><view class="header data-v-b237504c"><image class="logo data-v-b237504c" src="/static/logo.png" mode="aspectFit"></image><text class="title data-v-b237504c">毕业租房平台</text><text class="subtitle data-v-b237504c">找到理想的住所</text></view><view class="form-container data-v-b237504c"><view class="form-item data-v-b237504c"><view class="input-wrapper data-v-b237504c"><uni-icons vue-id="35a7246c-1" type="person" size="20" color="#999" class="data-v-b237504c" bind:__l="__l"></uni-icons><input class="input data-v-b237504c" type="text" placeholder="请输入用户名/手机号" maxlength="20" data-event-opts="{{[['input',[['__set_model',['$0','username','$event',[]],['form']]]]]}}" value="{{form.username}}" bindinput="__e"/></view></view><view class="form-item data-v-b237504c"><view class="input-wrapper data-v-b237504c"><uni-icons vue-id="35a7246c-2" type="locked" size="20" color="#999" class="data-v-b237504c" bind:__l="__l"></uni-icons><input class="input data-v-b237504c" type="{{showPassword?'text':'password'}}" placeholder="请输入密码" maxlength="20" data-event-opts="{{[['input',[['__set_model',['$0','password','$event',[]],['form']]]]]}}" value="{{form.password}}" bindinput="__e"/><uni-icons vue-id="35a7246c-3" type="{{showPassword?'eye-slash':'eye'}}" size="20" color="#999" data-event-opts="{{[['^click',[['togglePassword']]]]}}" bind:click="__e" class="data-v-b237504c" bind:__l="__l"></uni-icons></view></view><button class="login-btn data-v-b237504c" disabled="{{!canLogin}}" data-event-opts="{{[['tap',[['handleLogin',['$event']]]]]}}" bindtap="__e">{{''+(loading?'登录中...':'登录')+''}}</button><view class="links data-v-b237504c"><text data-event-opts="{{[['tap',[['toRegister',['$event']]]]]}}" class="link data-v-b237504c" bindtap="__e">还没有账号？立即注册</text><text data-event-opts="{{[['tap',[['toForgotPassword',['$event']]]]]}}" class="link data-v-b237504c" bindtap="__e">忘记密码？</text></view></view><view class="footer data-v-b237504c"><text class="footer-text data-v-b237504c">登录即表示同意</text><text data-event-opts="{{[['tap',[['toPrivacy',['$event']]]]]}}" class="footer-link data-v-b237504c" bindtap="__e">《用户协议》</text><text class="footer-text data-v-b237504c">和</text><text data-event-opts="{{[['tap',[['toPrivacy',['$event']]]]]}}" class="footer-link data-v-b237504c" bindtap="__e">《隐私政策》</text></view></view>