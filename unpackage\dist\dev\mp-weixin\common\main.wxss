












































































/* 全局样式 */
page {
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}
/* 通用样式 */
.container {
  padding: 20rpx;
}
.section {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}
.section-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.section-content {
  padding: 30rpx;
}
/* 按钮样式 */
.btn {
  border-radius: 12rpx;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
  border: none;
}
.btn-primary {
  background: linear-gradient(45deg, #007aff, #0056d3);
  color: #fff;
}
.btn-secondary {
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e0e0e0;
}
.btn-success {
  background: #10c560;
  color: #fff;
}
.btn-warning {
  background: #ff9800;
  color: #fff;
}
.btn-danger {
  background: #f44336;
  color: #fff;
}
.btn-small {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}
.btn-large {
  padding: 30rpx 60rpx;
  font-size: 32rpx;
}
/* 文本样式 */
.text-primary {
  color: #007aff;
}
.text-secondary {
  color: #666;
}
.text-success {
  color: #10c560;
}
.text-warning {
  color: #ff9800;
}
.text-danger {
  color: #f44336;
}
.text-muted {
  color: #999;
}
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
/* 间距样式 */
.m-0 { margin: 0;
}
.m-1 { margin: 10rpx;
}
.m-2 { margin: 20rpx;
}
.m-3 { margin: 30rpx;
}
.m-4 { margin: 40rpx;
}
.mt-0 { margin-top: 0;
}
.mt-1 { margin-top: 10rpx;
}
.mt-2 { margin-top: 20rpx;
}
.mt-3 { margin-top: 30rpx;
}
.mt-4 { margin-top: 40rpx;
}
.mb-0 { margin-bottom: 0;
}
.mb-1 { margin-bottom: 10rpx;
}
.mb-2 { margin-bottom: 20rpx;
}
.mb-3 { margin-bottom: 30rpx;
}
.mb-4 { margin-bottom: 40rpx;
}
.p-0 { padding: 0;
}
.p-1 { padding: 10rpx;
}
.p-2 { padding: 20rpx;
}
.p-3 { padding: 30rpx;
}
.p-4 { padding: 40rpx;
}
.pt-0 { padding-top: 0;
}
.pt-1 { padding-top: 10rpx;
}
.pt-2 { padding-top: 20rpx;
}
.pt-3 { padding-top: 30rpx;
}
.pt-4 { padding-top: 40rpx;
}
.pb-0 { padding-bottom: 0;
}
.pb-1 { padding-bottom: 10rpx;
}
.pb-2 { padding-bottom: 20rpx;
}
.pb-3 { padding-bottom: 30rpx;
}
.pb-4 { padding-bottom: 40rpx;
}
/* 布局样式 */
.flex {
  display: flex;
}
.flex-column {
  flex-direction: column;
}
.flex-row {
  flex-direction: row;
}
.flex-wrap {
  flex-wrap: wrap;
}
.justify-start {
  justify-content: flex-start;
}
.justify-center {
  justify-content: center;
}
.justify-end {
  justify-content: flex-end;
}
.justify-between {
  justify-content: space-between;
}
.justify-around {
  justify-content: space-around;
}
.align-start {
  align-items: flex-start;
}
.align-center {
  align-items: center;
}
.align-end {
  align-items: flex-end;
}
.flex-1 {
  flex: 1;
}
/* 边框样式 */
.border {
  border: 1rpx solid #e0e0e0;
}
.border-top {
  border-top: 1rpx solid #e0e0e0;
}
.border-bottom {
  border-bottom: 1rpx solid #e0e0e0;
}
.border-radius {
  border-radius: 12rpx;
}
.border-radius-large {
  border-radius: 20rpx;
}
/* 阴影样式 */
.shadow {
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.shadow-large {
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}
/* 动画样式 */
.fade-in {
  -webkit-animation: fadeIn 0.3s ease-in-out;
          animation: fadeIn 0.3s ease-in-out;
}
.slide-up {
  -webkit-animation: slideUp 0.3s ease-in-out;
          animation: slideUp 0.3s ease-in-out;
}
@-webkit-keyframes fadeIn {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@keyframes fadeIn {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@-webkit-keyframes slideUp {
from {
    -webkit-transform: translateY(100rpx);
            transform: translateY(100rpx);
    opacity: 0;
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
    opacity: 1;
}
}
@keyframes slideUp {
from {
    -webkit-transform: translateY(100rpx);
            transform: translateY(100rpx);
    opacity: 0;
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
    opacity: 1;
}
}
/* 响应式样式 */
@media screen and (max-width: 750rpx) {
.container {
    padding: 15rpx;
}
.section {
    margin-bottom: 15rpx;
}
}

