<view class="appointments-container data-v-45ecc94f"><view class="status-tabs data-v-45ecc94f"><block wx:for="{{statusTabs}}" wx:for-item="tab" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['switchTab',[index]]]]]}}" class="{{['tab-item','data-v-45ecc94f',(currentTab===index)?'active':'']}}" bindtap="__e"><text class="tab-text data-v-45ecc94f">{{tab.label}}</text><block wx:if="{{tab.count>0}}"><text class="tab-count data-v-45ecc94f">{{tab.count}}</text></block></view></block></view><scroll-view class="appointment-list data-v-45ecc94f" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><block wx:for="{{$root.l0}}" wx:for-item="appointment" wx:for-index="__i0__" wx:key="_id"><view class="appointment-item data-v-45ecc94f"><view class="appointment-header data-v-45ecc94f"><view class="{{['status-badge','data-v-45ecc94f',appointment.$orig.status]}}">{{''+appointment.m0+''}}</view><text class="appointment-time data-v-45ecc94f">{{appointment.m1}}</text></view><block wx:if="{{appointment.$orig.house}}"><view data-event-opts="{{[['tap',[['toHouseDetail',['$0'],[[['appointmentList','_id',appointment.$orig._id,'house._id']]]]]]]}}" class="house-info data-v-45ecc94f" bindtap="__e"><view class="house-image data-v-45ecc94f"><image src="{{appointment.$orig.house.images&&appointment.$orig.house.images[0]||'/static/default-house.png'}}" mode="aspectFill" class="data-v-45ecc94f"></image></view><view class="house-details data-v-45ecc94f"><text class="house-title data-v-45ecc94f">{{appointment.$orig.house.title}}</text><view class="house-tags data-v-45ecc94f"><text class="tag data-v-45ecc94f">{{appointment.m2}}</text><block wx:if="{{appointment.$orig.house.room_count}}"><text class="tag data-v-45ecc94f">{{appointment.$orig.house.room_count+"室"}}</text></block><block wx:if="{{appointment.$orig.house.hall_count}}"><text class="tag data-v-45ecc94f">{{appointment.$orig.house.hall_count+"厅"}}</text></block></view><view class="house-location data-v-45ecc94f"><uni-icons vue-id="{{'77ace197-1-'+__i0__}}" type="location" size="12" color="#999" class="data-v-45ecc94f" bind:__l="__l"></uni-icons><text class="location-text data-v-45ecc94f">{{appointment.$orig.house.location.district}}</text></view><view class="house-price data-v-45ecc94f"><text class="price data-v-45ecc94f">{{"¥"+appointment.$orig.house.price}}</text><text class="price-unit data-v-45ecc94f">/月</text></view></view></view></block><view class="appointment-details data-v-45ecc94f"><view class="detail-row data-v-45ecc94f"><text class="detail-label data-v-45ecc94f">预约时间:</text><text class="detail-value data-v-45ecc94f">{{appointment.m3}}</text></view><view class="detail-row data-v-45ecc94f"><text class="detail-label data-v-45ecc94f">联系电话:</text><text class="detail-value data-v-45ecc94f">{{appointment.$orig.contact_phone}}</text></view><block wx:if="{{appointment.$orig.message}}"><view class="detail-row data-v-45ecc94f"><text class="detail-label data-v-45ecc94f">备注信息:</text><text class="detail-value data-v-45ecc94f">{{appointment.$orig.message}}</text></view></block></view><block wx:if="{{appointment.$orig.status==='pending'}}"><view class="appointment-actions data-v-45ecc94f"><button data-event-opts="{{[['tap',[['cancelAppointment',['$0'],[[['appointmentList','_id',appointment.$orig._id]]]]]]]}}" class="action-btn cancel data-v-45ecc94f" bindtap="__e">取消预约</button><button data-event-opts="{{[['tap',[['contactLandlord',['$0'],[[['appointmentList','_id',appointment.$orig._id,'house']]]]]]]}}" class="action-btn contact data-v-45ecc94f" bindtap="__e">联系房东</button></view></block><block wx:else><block wx:if="{{appointment.$orig.status==='confirmed'}}"><view class="appointment-actions data-v-45ecc94f"><button data-event-opts="{{[['tap',[['contactLandlord',['$0'],[[['appointmentList','_id',appointment.$orig._id,'house']]]]]]]}}" class="action-btn contact data-v-45ecc94f" bindtap="__e">联系房东</button><button data-event-opts="{{[['tap',[['completeAppointment',['$0'],[[['appointmentList','_id',appointment.$orig._id]]]]]]]}}" class="action-btn complete data-v-45ecc94f" bindtap="__e">完成看房</button></view></block></block></view></block><block wx:if="{{$root.g0>0}}"><view class="load-status data-v-45ecc94f"><block wx:if="{{loading}}"><text class="data-v-45ecc94f">加载中...</text></block><block wx:else><block wx:if="{{noMore}}"><text class="data-v-45ecc94f">没有更多了</text></block></block></view></block><block wx:if="{{$root.g1}}"><view class="empty-state data-v-45ecc94f"><image src="/static/empty-appointment.png" mode="aspectFit" class="data-v-45ecc94f"></image><text class="empty-text data-v-45ecc94f">暂无预约记录</text><text class="empty-tip data-v-45ecc94f">去看看有什么好房源吧</text><button data-event-opts="{{[['tap',[['toBrowse',['$event']]]]]}}" class="browse-btn data-v-45ecc94f" bindtap="__e">去看房</button></view></block></scroll-view></view>