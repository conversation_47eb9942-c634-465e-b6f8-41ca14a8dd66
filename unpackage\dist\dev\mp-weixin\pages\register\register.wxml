<view class="register-container data-v-891c2434"><view class="header data-v-891c2434"><text class="title data-v-891c2434">注册账号</text><text class="subtitle data-v-891c2434">加入毕业租房平台</text></view><view class="form-container data-v-891c2434"><view class="form-item data-v-891c2434"><text class="label data-v-891c2434">用户名</text><view class="input-wrapper data-v-891c2434"><input class="input data-v-891c2434" type="text" placeholder="请输入用户名" maxlength="20" data-event-opts="{{[['input',[['__set_model',['$0','username','$event',[]],['form']]]]]}}" value="{{form.username}}" bindinput="__e"/></view></view><view class="form-item data-v-891c2434"><text class="label data-v-891c2434">手机号</text><view class="input-wrapper data-v-891c2434"><input class="input data-v-891c2434" type="number" placeholder="请输入手机号" maxlength="11" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['form']]]]]}}" value="{{form.mobile}}" bindinput="__e"/></view></view><view class="form-item data-v-891c2434"><text class="label data-v-891c2434">邮箱（可选）</text><view class="input-wrapper data-v-891c2434"><input class="input data-v-891c2434" type="text" placeholder="请输入邮箱" data-event-opts="{{[['input',[['__set_model',['$0','email','$event',[]],['form']]]]]}}" value="{{form.email}}" bindinput="__e"/></view></view><view class="form-item data-v-891c2434"><text class="label data-v-891c2434">密码</text><view class="input-wrapper data-v-891c2434"><input class="input data-v-891c2434" type="{{showPassword?'text':'password'}}" placeholder="请输入密码（6-20位）" maxlength="20" data-event-opts="{{[['input',[['__set_model',['$0','password','$event',[]],['form']]]]]}}" value="{{form.password}}" bindinput="__e"/><uni-icons vue-id="9a676040-1" type="{{showPassword?'eye-slash':'eye'}}" size="20" color="#999" data-event-opts="{{[['^click',[['togglePassword']]]]}}" bind:click="__e" class="data-v-891c2434" bind:__l="__l"></uni-icons></view></view><view class="form-item data-v-891c2434"><text class="label data-v-891c2434">确认密码</text><view class="input-wrapper data-v-891c2434"><input class="input data-v-891c2434" type="{{showConfirmPassword?'text':'password'}}" placeholder="请再次输入密码" maxlength="20" data-event-opts="{{[['input',[['__set_model',['$0','confirmPassword','$event',[]],['form']]]]]}}" value="{{form.confirmPassword}}" bindinput="__e"/><uni-icons vue-id="9a676040-2" type="{{showConfirmPassword?'eye-slash':'eye'}}" size="20" color="#999" data-event-opts="{{[['^click',[['toggleConfirmPassword']]]]}}" bind:click="__e" class="data-v-891c2434" bind:__l="__l"></uni-icons></view></view><view class="form-item data-v-891c2434"><text class="label data-v-891c2434">身份类型</text><view class="role-selector data-v-891c2434"><view data-event-opts="{{[['tap',[['selectRole',['student']]]]]}}" class="{{['role-item','data-v-891c2434',(form.role==='student')?'active':'']}}" bindtap="__e"><uni-icons vue-id="9a676040-3" type="person" size="24" color="{{form.role==='student'?'#007aff':'#999'}}" class="data-v-891c2434" bind:__l="__l"></uni-icons><text class="role-text data-v-891c2434">学生</text></view><view data-event-opts="{{[['tap',[['selectRole',['landlord']]]]]}}" class="{{['role-item','data-v-891c2434',(form.role==='landlord')?'active':'']}}" bindtap="__e"><uni-icons vue-id="9a676040-4" type="home" size="24" color="{{form.role==='landlord'?'#007aff':'#999'}}" class="data-v-891c2434" bind:__l="__l"></uni-icons><text class="role-text data-v-891c2434">房东</text></view></view></view><view class="agreement data-v-891c2434"><checkbox-group data-event-opts="{{[['change',[['onAgreementChange',['$event']]]]]}}" bindchange="__e" class="data-v-891c2434"><label class="agreement-item data-v-891c2434"><checkbox value="agree" checked="{{agreed}}" class="data-v-891c2434"></checkbox><text class="agreement-text data-v-891c2434">我已阅读并同意</text><text data-event-opts="{{[['tap',[['toPrivacy',['$event']]]]]}}" class="agreement-link data-v-891c2434" bindtap="__e">《用户协议》</text><text class="agreement-text data-v-891c2434">和</text><text data-event-opts="{{[['tap',[['toPrivacy',['$event']]]]]}}" class="agreement-link data-v-891c2434" bindtap="__e">《隐私政策》</text></label></checkbox-group></view><button class="register-btn data-v-891c2434" disabled="{{!canRegister}}" data-event-opts="{{[['tap',[['handleRegister',['$event']]]]]}}" bindtap="__e">{{''+(loading?'注册中...':'注册')+''}}</button><view class="links data-v-891c2434"><text data-event-opts="{{[['tap',[['toLogin',['$event']]]]]}}" class="link data-v-891c2434" bindtap="__e">已有账号？立即登录</text></view></view></view>