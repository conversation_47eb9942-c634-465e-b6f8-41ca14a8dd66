{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端/pages/login/login.vue?084a", "webpack:///D:/web/project/前端/pages/login/login.vue?8935", "webpack:///D:/web/project/前端/pages/login/login.vue?09c4", "webpack:///D:/web/project/前端/pages/login/login.vue?a8ab", "uni-app:///pages/login/login.vue", "webpack:///D:/web/project/前端/pages/login/login.vue?394f", "webpack:///D:/web/project/前端/pages/login/login.vue?d2c6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "form", "username", "password", "showPassword", "loading", "computed", "canLogin", "methods", "togglePassword", "handleLogin", "uni", "title", "icon", "request", "action", "result", "setTimeout", "url", "console", "toRegister", "toForgotPassword", "toPrivacy", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAgoB,CAAgB,8nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC6DppB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,aAEA,4EAEA;gBAAA,MACAR;kBAAA;kBAAA;gBAAA;gBACAS;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,MAIAV;kBAAA;kBAAA;gBAAA;gBACAQ;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBAAA;gBAAA;gBAAA,OAGAC;kBACAC;kBACAf;oBACAE;oBACAC;kBACA;gBACA;cAAA;gBANAa;gBAQA;kBACA;kBACAL;kBACA;kBAEAA;oBACAC;oBACAC;kBACA;;kBAEA;kBACAI;oBACA;oBACA;oBACA;oBACA;oBAEA;sBACA;sBACAN;wBACAO;sBACA;oBACA;sBACA;sBACAP;oBACA;sBACA;sBACAA;wBACAO;sBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACAT;QACAO;MACA;IACA;IAEA;IACAG;MACAV;QACAC;QACAC;MACA;IACA;IAEA;IACAS;MACAX;QACAC;QACAC;MACA;IACA;EACA;EAEAU;IACA;IACA;IACA;MACAZ;QACAO;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjMA;AAAA;AAAA;AAAA;AAAu7B,CAAgB,i5BAAG,EAAC,C;;;;;;;;;;;ACA38B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/login/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=b237504c&scoped=true&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&id=b237504c&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b237504c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/login.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=b237504c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"login-container\">\n    <view class=\"header\">\n      <image class=\"logo\" src=\"/static/logo.png\" mode=\"aspectFit\"></image>\n      <text class=\"title\">毕业租房平台</text>\n      <text class=\"subtitle\">找到理想的住所</text>\n    </view>\n    \n    <view class=\"form-container\">\n      <view class=\"form-item\">\n        <view class=\"input-wrapper\">\n          <uni-icons type=\"person\" size=\"20\" color=\"#999\"></uni-icons>\n          <input \n            class=\"input\" \n            type=\"text\" \n            placeholder=\"请输入用户名/手机号\" \n            v-model=\"form.username\"\n            maxlength=\"20\"\n          />\n        </view>\n      </view>\n      \n      <view class=\"form-item\">\n        <view class=\"input-wrapper\">\n          <uni-icons type=\"locked\" size=\"20\" color=\"#999\"></uni-icons>\n          <input \n            class=\"input\" \n            :type=\"showPassword ? 'text' : 'password'\" \n            placeholder=\"请输入密码\" \n            v-model=\"form.password\"\n            maxlength=\"20\"\n          />\n          <uni-icons \n            :type=\"showPassword ? 'eye-slash' : 'eye'\" \n            size=\"20\" \n            color=\"#999\"\n            @click=\"togglePassword\"\n          ></uni-icons>\n        </view>\n      </view>\n      \n      <button class=\"login-btn\" @click=\"handleLogin\" :disabled=\"!canLogin\">\n        {{ loading ? '登录中...' : '登录' }}\n      </button>\n      \n      <view class=\"links\">\n        <text class=\"link\" @click=\"toRegister\">还没有账号？立即注册</text>\n        <text class=\"link\" @click=\"toForgotPassword\">忘记密码？</text>\n      </view>\n    </view>\n    \n    <view class=\"footer\">\n      <text class=\"footer-text\">登录即表示同意</text>\n      <text class=\"footer-link\" @click=\"toPrivacy\">《用户协议》</text>\n      <text class=\"footer-text\">和</text>\n      <text class=\"footer-link\" @click=\"toPrivacy\">《隐私政策》</text>\n    </view>\n  </view>\n</template>\n\n<script>\nimport request from '@/utils/request.js'\nimport { validatePhone, setUserInfo } from '@/utils/common.js'\n\nexport default {\n  data() {\n    return {\n      form: {\n        username: '',\n        password: ''\n      },\n      showPassword: false,\n      loading: false\n    }\n  },\n  computed: {\n    canLogin() {\n      return this.form.username.trim() && this.form.password.trim() && !this.loading\n    }\n  },\n  methods: {\n    // 切换密码显示状态\n    togglePassword() {\n      this.showPassword = !this.showPassword\n    },\n    \n    // 登录处理\n    async handleLogin() {\n      if (!this.canLogin) return\n      \n      const { username, password } = this.form\n      \n      // 基础验证\n      if (username.length < 2) {\n        uni.showToast({\n          title: '用户名至少2个字符',\n          icon: 'none'\n        })\n        return\n      }\n      \n      if (password.length < 6) {\n        uni.showToast({\n          title: '密码至少6个字符',\n          icon: 'none'\n        })\n        return\n      }\n      \n      this.loading = true\n      \n      try {\n        const result = await request.callFunction('user-auth', {\n          action: 'login',\n          data: {\n            username: username.trim(),\n            password: password\n          }\n        })\n        \n        if (result.code === 0) {\n          // 保存token和用户信息\n          uni.setStorageSync('uni_id_token', result.data.token)\n          setUserInfo(result.data.userInfo)\n\n          uni.showToast({\n            title: '登录成功',\n            icon: 'success'\n          })\n          \n          // 延迟跳转，让用户看到成功提示\n          setTimeout(() => {\n            // 获取来源页面参数，判断是否需要跳转到特定页面\n            const pages = getCurrentPages()\n            const currentPage = pages[pages.length - 1]\n            const options = currentPage.options || {}\n\n            if (options.redirect) {\n              // 如果有重定向参数，跳转到指定页面\n              uni.reLaunch({\n                url: decodeURIComponent(options.redirect)\n              })\n            } else if (pages.length > 1) {\n              // 如果有来源页面，返回上一页\n              uni.navigateBack()\n            } else {\n              // 否则跳转到个人中心页面\n              uni.switchTab({\n                url: '/pages/user/profile'\n              })\n            }\n          }, 1500)\n        }\n      } catch (error) {\n        console.error('登录失败:', error)\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 跳转到注册页\n    toRegister() {\n      uni.navigateTo({\n        url: '/pages/register/register'\n      })\n    },\n    \n    // 跳转到忘记密码页\n    toForgotPassword() {\n      uni.showToast({\n        title: '功能开发中',\n        icon: 'none'\n      })\n    },\n    \n    // 跳转到隐私政策页\n    toPrivacy() {\n      uni.showToast({\n        title: '功能开发中',\n        icon: 'none'\n      })\n    }\n  },\n  \n  onLoad(options) {\n    // 如果已登录，直接跳转\n    const token = uni.getStorageSync('uni_id_token')\n    if (token) {\n      uni.reLaunch({\n        url: '/pages/index/index'\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.login-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 60rpx 40rpx 40rpx;\n  display: flex;\n  flex-direction: column;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 80rpx;\n}\n\n.logo {\n  width: 120rpx;\n  height: 120rpx;\n  margin-bottom: 30rpx;\n}\n\n.title {\n  display: block;\n  font-size: 48rpx;\n  font-weight: bold;\n  color: #fff;\n  margin-bottom: 10rpx;\n}\n\n.subtitle {\n  display: block;\n  font-size: 28rpx;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.form-container {\n  flex: 1;\n}\n\n.form-item {\n  margin-bottom: 40rpx;\n}\n\n.input-wrapper {\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 50rpx;\n  padding: 0 30rpx;\n  display: flex;\n  align-items: center;\n  height: 100rpx;\n}\n\n.input {\n  flex: 1;\n  margin-left: 20rpx;\n  font-size: 32rpx;\n  color: #333;\n}\n\n.login-btn {\n  width: 100%;\n  height: 100rpx;\n  background: linear-gradient(45deg, #ff6b6b, #ee5a24);\n  color: #fff;\n  border: none;\n  border-radius: 50rpx;\n  font-size: 36rpx;\n  font-weight: bold;\n  margin-top: 40rpx;\n  margin-bottom: 40rpx;\n}\n\n.login-btn:disabled {\n  background: #ccc;\n}\n\n.links {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 40rpx;\n}\n\n.link {\n  color: rgba(255, 255, 255, 0.9);\n  font-size: 28rpx;\n}\n\n.footer {\n  text-align: center;\n  margin-top: auto;\n  padding-top: 40rpx;\n}\n\n.footer-text {\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 24rpx;\n}\n\n.footer-link {\n  color: rgba(255, 255, 255, 0.9);\n  font-size: 24rpx;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&id=b237504c&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&id=b237504c&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751999918\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}