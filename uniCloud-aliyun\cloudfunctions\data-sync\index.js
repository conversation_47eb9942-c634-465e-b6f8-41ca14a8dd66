'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
  console.log('event : ', event);
  
  const { action } = event;
  
  try {
    switch (action) {
      case 'syncUsers':
        return await syncUsers();
      case 'syncHouses':
        return await syncHouses();
      case 'syncAppointments':
        return await syncAppointments();
      case 'syncAll':
        return await syncAll();
      case 'initTestData':
        return await initTestData();
      default:
        return {
          code: 400,
          message: '无效的操作类型'
        };
    }
  } catch (error) {
    console.error('数据同步失败:', error);
    return {
      code: 500,
      message: '数据同步失败',
      error: error.message
    };
  }
};

// 同步用户数据
async function syncUsers() {
  try {
    // 这里应该从前端数据库获取用户数据
    // 由于无法直接访问前端数据库，我们创建一些测试数据
    const testUsers = [
      {
        _id: 'user_001',
        username: 'test_user_1',
        nickname: '张同学',
        avatar: '/static/avatar1.jpg',
        mobile: '13800138001',
        email: '<EMAIL>',
        status: 'active',
        register_date: new Date('2024-01-15'),
        last_login_date: new Date('2024-01-20'),
        create_time: new Date('2024-01-15'),
        update_time: new Date('2024-01-20')
      },
      {
        _id: 'user_002',
        username: 'test_user_2',
        nickname: '李老师',
        avatar: '/static/avatar2.jpg',
        mobile: '13800138002',
        email: '<EMAIL>',
        status: 'active',
        register_date: new Date('2024-01-16'),
        last_login_date: new Date('2024-01-21'),
        create_time: new Date('2024-01-16'),
        update_time: new Date('2024-01-21')
      },
      {
        _id: 'user_003',
        username: 'test_user_3',
        nickname: '王先生',
        avatar: '/static/avatar3.jpg',
        mobile: '13800138003',
        email: '<EMAIL>',
        status: 'active',
        register_date: new Date('2024-01-17'),
        last_login_date: new Date('2024-01-22'),
        create_time: new Date('2024-01-17'),
        update_time: new Date('2024-01-22')
      }
    ];

    const userCollection = db.collection('uni-id-users');
    
    for (const user of testUsers) {
      await userCollection.doc(user._id).set(user);
    }

    return {
      code: 0,
      message: '用户数据同步成功',
      data: {
        count: testUsers.length
      }
    };
  } catch (error) {
    throw error;
  }
}

// 同步房源数据
async function syncHouses() {
  try {
    const testHouses = [
      {
        _id: 'house_001',
        title: '阳光小区三室一厅',
        description: '房屋位于市中心，交通便利，周边配套设施齐全。',
        price: 3500,
        area: 120,
        room_count: 3,
        hall_count: 1,
        bathroom_count: 2,
        floor: 8,
        total_floors: 18,
        orientation: '南北',
        decoration: '精装修',
        images: ['/static/house1.jpg', '/static/house2.jpg'],
        facilities: ['空调', '洗衣机', '冰箱', '热水器', '宽带'],
        location: {
          province: '北京市',
          city: '北京市',
          district: '朝阳区',
          address: '阳光小区8号楼',
          longitude: 116.4074,
          latitude: 39.9042
        },
        contact: {
          name: '张先生',
          phone: '13800138001'
        },
        publisher_id: 'user_001',
        is_verified: true,
        status: 'published',
        view_count: 156,
        favorite_count: 23,
        appointment_count: 8,
        create_time: new Date('2024-01-15'),
        update_time: new Date('2024-01-20')
      },
      {
        _id: 'house_002',
        title: '市中心精装公寓',
        description: '现代化装修，家具家电齐全，拎包入住。',
        price: 4200,
        area: 85,
        room_count: 2,
        hall_count: 1,
        bathroom_count: 1,
        floor: 15,
        total_floors: 25,
        orientation: '南向',
        decoration: '精装修',
        images: ['/static/house3.jpg', '/static/house4.jpg'],
        facilities: ['空调', '洗衣机', '冰箱', '热水器', '宽带', '电视'],
        location: {
          province: '北京市',
          city: '北京市',
          district: '海淀区',
          address: '中关村大厦15层',
          longitude: 116.3074,
          latitude: 39.9842
        },
        contact: {
          name: '李女士',
          phone: '13800138002'
        },
        publisher_id: 'user_002',
        is_verified: true,
        status: 'published',
        view_count: 203,
        favorite_count: 31,
        appointment_count: 12,
        create_time: new Date('2024-01-16'),
        update_time: new Date('2024-01-21')
      },
      {
        _id: 'house_003',
        title: '学区房两室一厅',
        description: '临近重点学校，适合有孩子的家庭。',
        price: 2800,
        area: 95,
        room_count: 2,
        hall_count: 1,
        bathroom_count: 1,
        floor: 5,
        total_floors: 12,
        orientation: '东南',
        decoration: '简装修',
        images: ['/static/house5.jpg'],
        facilities: ['空调', '热水器', '宽带'],
        location: {
          province: '北京市',
          city: '北京市',
          district: '西城区',
          address: '学府路小区5号楼',
          longitude: 116.2074,
          latitude: 39.8842
        },
        contact: {
          name: '王老师',
          phone: '13800138003'
        },
        publisher_id: 'user_003',
        is_verified: true,
        status: 'published',
        view_count: 89,
        favorite_count: 15,
        appointment_count: 5,
        create_time: new Date('2024-01-17'),
        update_time: new Date('2024-01-22')
      }
    ];

    const houseCollection = db.collection('houses');
    
    for (const house of testHouses) {
      await houseCollection.doc(house._id).set(house);
    }

    return {
      code: 0,
      message: '房源数据同步成功',
      data: {
        count: testHouses.length
      }
    };
  } catch (error) {
    throw error;
  }
}

// 同步预约数据
async function syncAppointments() {
  try {
    const testAppointments = [
      {
        _id: 'appointment_001',
        house_id: 'house_001',
        user_id: 'user_002',
        contact_name: '李老师',
        contact_phone: '13800138002',
        appointment_time: new Date('2024-01-25 14:00:00'),
        message: '希望能看房，周末有时间。',
        status: 'confirmed',
        create_time: new Date('2024-01-20'),
        update_time: new Date('2024-01-21')
      },
      {
        _id: 'appointment_002',
        house_id: 'house_002',
        user_id: 'user_003',
        contact_name: '王先生',
        contact_phone: '13800138003',
        appointment_time: new Date('2024-01-26 10:00:00'),
        message: '想了解一下房屋详情。',
        status: 'pending',
        create_time: new Date('2024-01-21'),
        update_time: new Date('2024-01-21')
      },
      {
        _id: 'appointment_003',
        house_id: 'house_001',
        user_id: 'user_003',
        contact_name: '王先生',
        contact_phone: '13800138003',
        appointment_time: new Date('2024-01-24 16:00:00'),
        message: '下班后看房。',
        status: 'completed',
        create_time: new Date('2024-01-19'),
        update_time: new Date('2024-01-24')
      }
    ];

    const appointmentCollection = db.collection('appointments');
    
    for (const appointment of testAppointments) {
      await appointmentCollection.doc(appointment._id).set(appointment);
    }

    return {
      code: 0,
      message: '预约数据同步成功',
      data: {
        count: testAppointments.length
      }
    };
  } catch (error) {
    throw error;
  }
}

// 同步所有数据
async function syncAll() {
  try {
    const userResult = await syncUsers();
    const houseResult = await syncHouses();
    const appointmentResult = await syncAppointments();

    return {
      code: 0,
      message: '所有数据同步成功',
      data: {
        users: userResult.data.count,
        houses: houseResult.data.count,
        appointments: appointmentResult.data.count
      }
    };
  } catch (error) {
    throw error;
  }
}

// 初始化测试数据
async function initTestData() {
  try {
    // 清空现有数据
    await db.collection('uni-id-users').where({}).remove();
    await db.collection('houses').where({}).remove();
    await db.collection('appointments').where({}).remove();

    // 重新同步数据
    return await syncAll();
  } catch (error) {
    throw error;
  }
}
