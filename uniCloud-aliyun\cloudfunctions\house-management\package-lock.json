{"name": "house-management", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "house-management", "version": "1.0.0", "dependencies": {"uni-config-center": "file:../../../uni_modules/uni-config-center/uniCloud/cloudfunctions/common/uni-config-center", "uni-id-common": "file:../../../uni_modules/uni-id-common/uniCloud/cloudfunctions/common/uni-id-common"}}, "../../../uni_modules/uni-config-center/uniCloud/cloudfunctions/common/uni-config-center": {"version": "0.0.3", "license": "Apache-2.0"}, "../../../uni_modules/uni-id-common/uniCloud/cloudfunctions/common/uni-id-common": {"version": "1.0.18", "license": "Apache-2.0", "dependencies": {"uni-config-center": "file:../../../../../uni-config-center/uniCloud/cloudfunctions/common/uni-config-center"}}, "node_modules/uni-config-center": {"resolved": "../../../uni_modules/uni-config-center/uniCloud/cloudfunctions/common/uni-config-center", "link": true}, "node_modules/uni-id-common": {"resolved": "../../../uni_modules/uni-id-common/uniCloud/cloudfunctions/common/uni-id-common", "link": true}}}