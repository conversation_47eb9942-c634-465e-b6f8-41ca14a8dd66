
.search-container.data-v-4cedc0c6 {
  height: 100vh;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
}
.search-bar.data-v-4cedc0c6 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 30rpx;
  padding-top: 150rpx; /* 默认间距，会被动态样式覆盖 */
  padding-bottom: 30rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}
.search-input-wrapper.data-v-4cedc0c6 {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  height: 80rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}
.search-input-wrapper.data-v-4cedc0c6:focus-within {
  background: #fff;
  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.15);
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
}
.search-input.data-v-4cedc0c6 {
  flex: 1;
  margin-left: 15rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}
.search-input.data-v-4cedc0c6::-webkit-input-placeholder {
  color: #999;
}
.search-input.data-v-4cedc0c6::placeholder {
  color: #999;
}
.clear-btn.data-v-4cedc0c6 {
  padding: 10rpx;
  border-radius: 50%;
  transition: all 0.3s ease;
}
.clear-btn.data-v-4cedc0c6:active {
  background: rgba(0, 0, 0, 0.1);
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
}
.cancel-btn.data-v-4cedc0c6 {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
  padding: 16rpx 20rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}
.cancel-btn.data-v-4cedc0c6:active {
  background: rgba(255, 255, 255, 0.2);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.suggestions.data-v-4cedc0c6 {
  background: #fff;
  margin: 0 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
.suggestion-item.data-v-4cedc0c6 {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f8f9fa;
  transition: all 0.3s ease;
}
.suggestion-item.data-v-4cedc0c6:last-child {
  border-bottom: none;
}
.suggestion-item.data-v-4cedc0c6:active {
  background: #f8f9fa;
}
.suggestion-text.data-v-4cedc0c6 {
  font-size: 28rpx;
  color: #333;
  margin-left: 20rpx;
}
.search-history.data-v-4cedc0c6, .hot-search.data-v-4cedc0c6 {
  background: #fff;
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}
.history-header.data-v-4cedc0c6, .hot-header.data-v-4cedc0c6 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}
.history-title.data-v-4cedc0c6, .hot-title.data-v-4cedc0c6 {
  font-size: 30rpx;
  color: #303133;
  font-weight: 600;
}
.clear-history.data-v-4cedc0c6 {
  font-size: 26rpx;
  color: #909399;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}
.clear-history.data-v-4cedc0c6:active {
  background: #f5f7fa;
  color: #606266;
}
.history-tags.data-v-4cedc0c6, .hot-tags.data-v-4cedc0c6 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.history-tag.data-v-4cedc0c6, .hot-tag.data-v-4cedc0c6 {
  background: #f8f9fa;
  border: 1rpx solid transparent;
  border-radius: 20rpx;
  padding: 16rpx 24rpx;
  display: flex;
  align-items: center;
  gap: 10rpx;
  transition: all 0.3s ease;
}
.history-tag.data-v-4cedc0c6:active, .hot-tag.data-v-4cedc0c6:active {
  background: #e7f4ff;
  border-color: #007aff;
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.tag-text.data-v-4cedc0c6 {
  font-size: 26rpx;
  color: #666;
}
.search-results.data-v-4cedc0c6 {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.filter-bar.data-v-4cedc0c6 {
  background: #fff;
  padding: 20rpx;
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}
.filter-item.data-v-4cedc0c6 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 10rpx;
  gap: 8rpx;
}
.filter-text.data-v-4cedc0c6 {
  font-size: 28rpx;
  color: #333;
}
.result-list.data-v-4cedc0c6 {
  flex: 1;
  padding: 20rpx;
}
.result-header.data-v-4cedc0c6 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.result-count.data-v-4cedc0c6 {
  font-size: 26rpx;
  color: #666;
}
.sort-btn.data-v-4cedc0c6 {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.sort-text.data-v-4cedc0c6 {
  font-size: 26rpx;
  color: #666;
}
.house-item.data-v-4cedc0c6 {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  display: flex;
}
.house-image.data-v-4cedc0c6 {
  position: relative;
  width: 240rpx;
  height: 180rpx;
  flex-shrink: 0;
}
.house-image image.data-v-4cedc0c6 {
  width: 100%;
  height: 100%;
}
.house-type.data-v-4cedc0c6 {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  background: rgba(0, 122, 255, 0.9);
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}
.house-info.data-v-4cedc0c6 {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.house-title.data-v-4cedc0c6 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.house-tags.data-v-4cedc0c6 {
  display: flex;
  gap: 8rpx;
  margin-bottom: 10rpx;
}
.tag.data-v-4cedc0c6 {
  background: #f0f0f0;
  color: #666;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  font-size: 20rpx;
}
.house-location.data-v-4cedc0c6 {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}
.location-text.data-v-4cedc0c6 {
  font-size: 24rpx;
  color: #999;
  margin-left: 6rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.house-bottom.data-v-4cedc0c6 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.price-info.data-v-4cedc0c6 {
  display: flex;
  align-items: baseline;
}
.price.data-v-4cedc0c6 {
  font-size: 30rpx;
  font-weight: bold;
  color: #ff6b6b;
}
.price-unit.data-v-4cedc0c6 {
  font-size: 20rpx;
  color: #999;
  margin-left: 4rpx;
}
.house-stats.data-v-4cedc0c6 {
  display: flex;
  gap: 15rpx;
}
.stat-item.data-v-4cedc0c6 {
  font-size: 20rpx;
  color: #999;
  display: flex;
  align-items: center;
  gap: 4rpx;
}
.load-status.data-v-4cedc0c6 {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}
.empty-state.data-v-4cedc0c6 {
  text-align: center;
  padding: 100rpx 40rpx;
}
.empty-state image.data-v-4cedc0c6 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}
.empty-text.data-v-4cedc0c6 {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.empty-tip.data-v-4cedc0c6 {
  display: block;
  font-size: 24rpx;
  color: #ccc;
}

