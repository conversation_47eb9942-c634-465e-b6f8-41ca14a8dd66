'use strict';

const uniID = require('uni-id-common');

// 通用 checkToken 函数
async function checkUserToken(token, context) {
  const uniIdIns = uniID.createInstance({ context });
  const payload = await uniIdIns.checkToken(token);

  if (payload.errCode === 0) {
    return {
      code: 0,
      uid: payload.uid,
      userInfo: payload.userInfo
    };
  } else {
    return {
      code: payload.errCode || 401,
      message: payload.errMsg || '身份验证失败'
    };
  }
}

exports.main = async (event, context) => {
  const { action, data } = event;
  
  try {
    switch (action) {
      case 'addFavorite':
        return await addFavorite(event, context);
      case 'removeFavorite':
        return await removeFavorite(event, context);
      case 'getFavoriteList':
        return await getFavoriteList(event, context);
      case 'getFavorites':
        return await getFavoriteList(event, context);
      case 'checkFavoriteStatus':
        return await checkFavoriteStatus(event, context);
      default:
        return {
          code: 400,
          message: '无效的操作'
        };
    }
  } catch (error) {
    console.error('收藏管理云函数执行错误:', error);
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message
    };
  }
};

// 添加收藏
async function addFavorite(event, context) {
  const payload = await checkUserToken(event.uniIdToken, context);
  if (payload.code !== 0) {
    return payload;
  }
  
  const { house_id } = event.data;
  
  if (!house_id) {
    return {
      code: 400,
      message: '房源ID不能为空'
    };
  }
  
  try {
    const db = uniCloud.database();
    
    // 检查房源是否存在
    const houseRes = await db.collection('houses').doc(house_id).get();
    if (houseRes.data.length === 0) {
      return {
        code: 404,
        message: '房源不存在'
      };
    }
    
    // 检查是否已收藏
    const favoriteRes = await db.collection('favorites').where({
      user_id: payload.uid,
      house_id: house_id
    }).get();
    
    if (favoriteRes.data.length > 0) {
      return {
        code: 400,
        message: '已收藏该房源'
      };
    }
    
    // 添加收藏记录
    await db.collection('favorites').add({
      user_id: payload.uid,
      house_id: house_id,
      create_date: new Date()
    });
    
    // 更新房源收藏数
    await db.collection('houses').doc(house_id).update({
      favorite_count: db.command.inc(1)
    });
    
    return {
      code: 0,
      message: '收藏成功'
    };
  } catch (error) {
    return {
      code: 500,
      message: '收藏失败',
      error: error.message
    };
  }
}

// 取消收藏
async function removeFavorite(event, context) {
  const payload = await checkUserToken(event.uniIdToken, context);
  if (payload.code !== 0) {
    return payload;
  }
  
  const { house_id } = event.data;
  
  if (!house_id) {
    return {
      code: 400,
      message: '房源ID不能为空'
    };
  }
  
  try {
    const db = uniCloud.database();
    
    // 查找收藏记录
    const favoriteRes = await db.collection('favorites').where({
      user_id: payload.uid,
      house_id: house_id
    }).get();
    
    if (favoriteRes.data.length === 0) {
      return {
        code: 400,
        message: '未收藏该房源'
      };
    }
    
    // 删除收藏记录
    await db.collection('favorites').doc(favoriteRes.data[0]._id).remove();
    
    // 更新房源收藏数
    await db.collection('houses').doc(house_id).update({
      favorite_count: db.command.inc(-1)
    });
    
    return {
      code: 0,
      message: '取消收藏成功'
    };
  } catch (error) {
    return {
      code: 500,
      message: '取消收藏失败',
      error: error.message
    };
  }
}

// 获取收藏列表
async function getFavoriteList(event, context) {
  const payload = await checkUserToken(event.uniIdToken, context);
  if (payload.code !== 0) {
    return payload;
  }

  const { page = 1, pageSize = 10, countOnly = false } = event.data;
  
  try {
    const db = uniCloud.database();

    // 如果只需要数量，直接返回count结果
    if (countOnly) {
      const countResult = await db.collection('favorites').where({
        user_id: payload.uid
      }).count();

      return {
        code: 0,
        message: '获取成功',
        data: {
          total: countResult.total
        }
      };
    }

    // 获取收藏记录
    const skip = (page - 1) * pageSize;
    const favoriteRes = await db.collection('favorites')
      .where({
        user_id: payload.uid
      })
      .skip(skip)
      .limit(pageSize)
      .orderBy('create_date', 'desc')
      .get();
    
    // 获取房源详情
    const houseIds = favoriteRes.data.map(item => item.house_id);
    let houses = [];
    
    if (houseIds.length > 0) {
      const houseRes = await db.collection('houses')
        .where({
          _id: db.command.in(houseIds)
        })
        .get();
      
      houses = houseRes.data;
    }
    
    // 组合数据
    const list = favoriteRes.data.map(favorite => {
      const house = houses.find(h => h._id === favorite.house_id);
      return {
        favorite_id: favorite._id,
        house: house || null,
        create_date: favorite.create_date
      };
    }).filter(item => item.house); // 过滤掉房源不存在的收藏
    
    // 获取总数
    const countResult = await db.collection('favorites').where({
      user_id: payload.uid
    }).count();
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        list: list,
        total: countResult.total,
        page: page,
        pageSize: pageSize
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取失败',
      error: error.message
    };
  }
}

// 检查收藏状态
async function checkFavoriteStatus(event, context) {
  const payload = await checkUserToken(event.uniIdToken, context);
  if (payload.code !== 0) {
    return payload;
  }
  
  const { house_id } = event.data;
  
  if (!house_id) {
    return {
      code: 400,
      message: '房源ID不能为空'
    };
  }
  
  try {
    const db = uniCloud.database();
    
    const favoriteRes = await db.collection('favorites').where({
      user_id: payload.uid,
      house_id: house_id
    }).get();
    
    return {
      code: 0,
      message: '检查成功',
      data: {
        is_favorited: favoriteRes.data.length > 0
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '检查失败',
      error: error.message
    };
  }
}
