{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端/pages/user/appointments.vue?d8a8", "webpack:///D:/web/project/前端/pages/user/appointments.vue?a52f", "webpack:///D:/web/project/前端/pages/user/appointments.vue?2b04", "webpack:///D:/web/project/前端/pages/user/appointments.vue?6d58", "uni-app:///pages/user/appointments.vue", "webpack:///D:/web/project/前端/pages/user/appointments.vue?003f", "webpack:///D:/web/project/前端/pages/user/appointments.vue?c013"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "currentTab", "statusTabs", "label", "value", "count", "appointmentList", "loading", "refreshing", "noMore", "page", "pageSize", "methods", "formatTime", "formatDateTime", "getTypeText", "getStatusText", "switchTab", "loadAppointmentList", "refresh", "params", "action", "currentStatus", "request", "result", "console", "loadStatistics", "loadMore", "onRefresh", "cancelAppointment", "uni", "title", "content", "success", "res", "appointment_id", "icon", "appointment", "completeAppointment", "status", "contactLandlord", "itemList", "phoneNumber", "toHouseDetail", "url", "to<PERSON><PERSON>e", "onLoad", "onShow", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACqC;;;AAGhG;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClCA;AAAA;AAAA;AAAA;AAAuoB,CAAgB,qoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACsG3pB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC,aACA;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACAC;IAEA;IACAC;MACA;QAAA;MAAA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAEA;kBACA;kBACA;gBACA;gBAAA;gBAGAC;kBACAC;kBACArB;oBACAU;oBACAC;kBACA;gBACA,GAEA;gBACAW;gBACA;kBACAF;gBACA;gBAAA;gBAAA,OAEAG;cAAA;gBAAAC;gBAEA;kBAAA,eACAA;kBAEA;oBACA;kBACA;oBACA;kBACA;kBAEA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;gBACA;kBACAD;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAE;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACAC;kBACAC;kBACAC;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAC;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA;8BAAA,OAEAX;gCACAF;gCACArB;kCACAmC;gCACA;8BACA;4BAAA;8BALAX;8BAOA;gCACAM;kCACAC;kCACAK;gCACA;;gCAEA;gCACAC;;gCAEA;gCACA;8BACA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAEAZ;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAGA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAa;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAR;kBACAC;kBACAC;kBACAC;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAC;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA;8BAAA,OAEAX;gCACAF;gCACArB;kCACAmC;kCACAI;gCACA;8BACA;4BAAA;8BANAf;8BAQA;gCACAM;kCACAC;kCACAK;gCACA;;gCAEA;gCACAC;;gCAEA;gCACA;8BACA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAEAZ;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAGA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAe;MACA;QACAV;UACAC;UACAK;QACA;QACA;MACA;MAEAN;QACAW;QACAR;UACA;YACA;YACAH;cACAY;YACA;UACA;YACA;YACA;cACAZ;gBACA9B;gBACAiC;kBACAH;oBACAC;oBACAK;kBACA;gBACA;cACA;YACA;cACAN;gBACAC;gBACAK;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAO;MACAb;QACAc;MACA;IACA;IAEA;IACAC;MACAf;QACAc;MACA;IACA;EACA;EAEAE;IACA;IACA;EACA;EAEAC;IACA;IACA;IACA;EACA;EAEAC;IACA;IACAlB;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7WA;AAAA;AAAA;AAAA;AAA87B,CAAgB,w5BAAG,EAAC,C;;;;;;;;;;;ACAl9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/appointments.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/user/appointments.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./appointments.vue?vue&type=template&id=45ecc94f&scoped=true&\"\nvar renderjs\nimport script from \"./appointments.vue?vue&type=script&lang=js&\"\nexport * from \"./appointments.vue?vue&type=script&lang=js&\"\nimport style0 from \"./appointments.vue?vue&type=style&index=0&id=45ecc94f&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"45ecc94f\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/appointments.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./appointments.vue?vue&type=template&id=45ecc94f&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.appointmentList, function (appointment, __i0__) {\n    var $orig = _vm.__get_orig(appointment)\n    var m0 = _vm.getStatusText(appointment.status)\n    var m1 = _vm.formatTime(appointment.create_date)\n    var m2 = appointment.house ? _vm.getTypeText(appointment.house.type) : null\n    var m3 = _vm.formatDateTime(appointment.appointment_date)\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n      m2: m2,\n      m3: m3,\n    }\n  })\n  var g0 = _vm.appointmentList.length\n  var g1 = !_vm.loading && _vm.appointmentList.length === 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./appointments.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./appointments.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"appointments-container\">\n    <!-- 状态筛选 -->\n    <view class=\"status-tabs\">\n      <view \n        class=\"tab-item\" \n        v-for=\"(tab, index) in statusTabs\" \n        :key=\"index\"\n        :class=\"{ active: currentTab === index }\"\n        @click=\"switchTab(index)\"\n      >\n        <text class=\"tab-text\">{{ tab.label }}</text>\n        <text class=\"tab-count\" v-if=\"tab.count > 0\">{{ tab.count }}</text>\n      </view>\n    </view>\n    \n    <!-- 预约列表 -->\n    <scroll-view \n      class=\"appointment-list\" \n      scroll-y \n      @scrolltolower=\"loadMore\"\n      refresher-enabled\n      @refresherrefresh=\"onRefresh\"\n      :refresher-triggered=\"refreshing\"\n    >\n      <view class=\"appointment-item\" v-for=\"appointment in appointmentList\" :key=\"appointment._id\">\n        <view class=\"appointment-header\">\n          <view class=\"status-badge\" :class=\"appointment.status\">\n            {{ getStatusText(appointment.status) }}\n          </view>\n          <text class=\"appointment-time\">{{ formatTime(appointment.create_date) }}</text>\n        </view>\n        \n        <view class=\"house-info\" @click=\"toHouseDetail(appointment.house._id)\" v-if=\"appointment.house\">\n          <view class=\"house-image\">\n            <image \n              :src=\"appointment.house.images && appointment.house.images[0] || '/static/default-house.png'\" \n              mode=\"aspectFill\"\n            ></image>\n          </view>\n          <view class=\"house-details\">\n            <text class=\"house-title\">{{ appointment.house.title }}</text>\n            <view class=\"house-tags\">\n              <text class=\"tag\">{{ getTypeText(appointment.house.type) }}</text>\n              <text class=\"tag\" v-if=\"appointment.house.room_count\">{{ appointment.house.room_count }}室</text>\n              <text class=\"tag\" v-if=\"appointment.house.hall_count\">{{ appointment.house.hall_count }}厅</text>\n            </view>\n            <view class=\"house-location\">\n              <uni-icons type=\"location\" size=\"12\" color=\"#999\"></uni-icons>\n              <text class=\"location-text\">{{ appointment.house.location.district }}</text>\n            </view>\n            <view class=\"house-price\">\n              <text class=\"price\">¥{{ appointment.house.price }}</text>\n              <text class=\"price-unit\">/月</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"appointment-details\">\n          <view class=\"detail-row\">\n            <text class=\"detail-label\">预约时间:</text>\n            <text class=\"detail-value\">{{ formatDateTime(appointment.appointment_date) }}</text>\n          </view>\n          <view class=\"detail-row\">\n            <text class=\"detail-label\">联系电话:</text>\n            <text class=\"detail-value\">{{ appointment.contact_phone }}</text>\n          </view>\n          <view class=\"detail-row\" v-if=\"appointment.message\">\n            <text class=\"detail-label\">备注信息:</text>\n            <text class=\"detail-value\">{{ appointment.message }}</text>\n          </view>\n        </view>\n        \n        <view class=\"appointment-actions\" v-if=\"appointment.status === 'pending'\">\n          <button class=\"action-btn cancel\" @click=\"cancelAppointment(appointment)\">取消预约</button>\n          <button class=\"action-btn contact\" @click=\"contactLandlord(appointment.house)\">联系房东</button>\n        </view>\n        \n        <view class=\"appointment-actions\" v-else-if=\"appointment.status === 'confirmed'\">\n          <button class=\"action-btn contact\" @click=\"contactLandlord(appointment.house)\">联系房东</button>\n          <button class=\"action-btn complete\" @click=\"completeAppointment(appointment)\">完成看房</button>\n        </view>\n      </view>\n      \n      <!-- 加载状态 -->\n      <view class=\"load-status\" v-if=\"appointmentList.length > 0\">\n        <text v-if=\"loading\">加载中...</text>\n        <text v-else-if=\"noMore\">没有更多了</text>\n      </view>\n      \n      <!-- 空状态 -->\n      <view class=\"empty-state\" v-if=\"!loading && appointmentList.length === 0\">\n        <image src=\"/static/empty-appointment.png\" mode=\"aspectFit\"></image>\n        <text class=\"empty-text\">暂无预约记录</text>\n        <text class=\"empty-tip\">去看看有什么好房源吧</text>\n        <button class=\"browse-btn\" @click=\"toBrowse\">去看房</button>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nimport request from '@/utils/request.js'\nimport { formatTime, formatDateTime } from '@/utils/common.js'\nimport { HOUSE_TYPES, APPOINTMENT_STATUS } from '@/common/config.js'\n\nexport default {\n  data() {\n    return {\n      currentTab: 0,\n      statusTabs: [\n        { label: '全部', value: '', count: 0 },\n        { label: '待确认', value: 'pending', count: 0 },\n        { label: '已确认', value: 'confirmed', count: 0 },\n        { label: '已完成', value: 'completed', count: 0 },\n        { label: '已取消', value: 'cancelled', count: 0 }\n      ],\n      appointmentList: [],\n      loading: false,\n      refreshing: false,\n      noMore: false,\n      page: 1,\n      pageSize: 10\n    }\n  },\n  methods: {\n    formatTime,\n    formatDateTime,\n    \n    // 获取房源类型文本\n    getTypeText(type) {\n      const typeItem = HOUSE_TYPES.find(item => item.value === type)\n      return typeItem ? typeItem.label : type\n    },\n    \n    // 获取状态文本\n    getStatusText(status) {\n      return APPOINTMENT_STATUS[status] || status\n    },\n    \n    // 切换标签\n    switchTab(index) {\n      this.currentTab = index\n      this.loadAppointmentList(true)\n    },\n    \n    // 加载预约列表\n    async loadAppointmentList(refresh = false) {\n      if (this.loading) return\n      \n      this.loading = true\n      \n      if (refresh) {\n        this.page = 1\n        this.noMore = false\n      }\n      \n      try {\n        const params = {\n          action: 'getAppointmentList',\n          data: {\n            page: this.page,\n            pageSize: this.pageSize\n          }\n        }\n        \n        // 添加状态筛选\n        const currentStatus = this.statusTabs[this.currentTab].value\n        if (currentStatus) {\n          params.data.status = currentStatus\n        }\n        \n        const result = await request.callFunction('appointment-management', params)\n        \n        if (result.code === 0) {\n          const { list, total } = result.data\n          \n          if (refresh) {\n            this.appointmentList = list\n          } else {\n            this.appointmentList.push(...list)\n          }\n          \n          this.page++\n          this.noMore = this.appointmentList.length >= total\n        }\n      } catch (error) {\n        console.error('加载预约列表失败:', error)\n      } finally {\n        this.loading = false\n        this.refreshing = false\n      }\n    },\n    \n    // 加载统计数据\n    async loadStatistics() {\n      try {\n        // 这里可以调用API获取各状态的预约数量\n        // 暂时使用模拟数据\n        this.statusTabs[0].count = 8\n        this.statusTabs[1].count = 2\n        this.statusTabs[2].count = 3\n        this.statusTabs[3].count = 2\n        this.statusTabs[4].count = 1\n      } catch (error) {\n        console.error('加载统计数据失败:', error)\n      }\n    },\n    \n    // 加载更多\n    loadMore() {\n      if (!this.noMore && !this.loading) {\n        this.loadAppointmentList()\n      }\n    },\n    \n    // 下拉刷新\n    onRefresh() {\n      this.refreshing = true\n      this.loadAppointmentList(true)\n      this.loadStatistics()\n    },\n    \n    // 取消预约\n    async cancelAppointment(appointment) {\n      uni.showModal({\n        title: '提示',\n        content: '确定要取消此预约吗？',\n        success: async (res) => {\n          if (res.confirm) {\n            try {\n              const result = await request.callFunction('appointment-management', {\n                action: 'cancelAppointment',\n                data: {\n                  appointment_id: appointment._id\n                }\n              })\n              \n              if (result.code === 0) {\n                uni.showToast({\n                  title: '取消成功',\n                  icon: 'success'\n                })\n                \n                // 更新本地数据\n                appointment.status = 'cancelled'\n                \n                // 重新加载统计数据\n                this.loadStatistics()\n              }\n            } catch (error) {\n              console.error('取消预约失败:', error)\n            }\n          }\n        }\n      })\n    },\n    \n    // 完成预约\n    async completeAppointment(appointment) {\n      uni.showModal({\n        title: '提示',\n        content: '确认已完成看房吗？',\n        success: async (res) => {\n          if (res.confirm) {\n            try {\n              const result = await request.callFunction('appointment-management', {\n                action: 'updateAppointment',\n                data: {\n                  appointment_id: appointment._id,\n                  status: 'completed'\n                }\n              })\n              \n              if (result.code === 0) {\n                uni.showToast({\n                  title: '已完成',\n                  icon: 'success'\n                })\n                \n                // 更新本地数据\n                appointment.status = 'completed'\n                \n                // 重新加载统计数据\n                this.loadStatistics()\n              }\n            } catch (error) {\n              console.error('更新状态失败:', error)\n            }\n          }\n        }\n      })\n    },\n    \n    // 联系房东\n    contactLandlord(house) {\n      if (!house.contact) {\n        uni.showToast({\n          title: '暂无联系方式',\n          icon: 'none'\n        })\n        return\n      }\n      \n      uni.showActionSheet({\n        itemList: ['拨打电话', '复制微信号'],\n        success: (res) => {\n          if (res.tapIndex === 0) {\n            // 拨打电话\n            uni.makePhoneCall({\n              phoneNumber: house.contact.phone\n            })\n          } else if (res.tapIndex === 1) {\n            // 复制微信号\n            if (house.contact.wechat) {\n              uni.setClipboardData({\n                data: house.contact.wechat,\n                success: () => {\n                  uni.showToast({\n                    title: '微信号已复制',\n                    icon: 'success'\n                  })\n                }\n              })\n            } else {\n              uni.showToast({\n                title: '暂无微信号',\n                icon: 'none'\n              })\n            }\n          }\n        }\n      })\n    },\n    \n    // 跳转到房源详情\n    toHouseDetail(houseId) {\n      uni.navigateTo({\n        url: `/pages/house/detail?id=${houseId}`\n      })\n    },\n    \n    // 跳转到浏览页面\n    toBrowse() {\n      uni.switchTab({\n        url: '/pages/house/list'\n      })\n    }\n  },\n  \n  onLoad() {\n    this.loadAppointmentList(true)\n    this.loadStatistics()\n  },\n  \n  onShow() {\n    // 从详情页返回时刷新列表\n    this.loadAppointmentList(true)\n    this.loadStatistics()\n  },\n  \n  onPullDownRefresh() {\n    this.onRefresh()\n    uni.stopPullDownRefresh()\n  }\n}\n</script>\n\n<style scoped>\n.appointments-container {\n  height: 100vh;\n  background: #f8f9fa;\n  display: flex;\n  flex-direction: column;\n}\n\n.status-tabs {\n  background: #fff;\n  display: flex;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.tab-item {\n  flex: 1;\n  padding: 30rpx 10rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8rpx;\n  position: relative;\n}\n\n.tab-item.active {\n  color: #007aff;\n}\n\n.tab-item.active::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 60rpx;\n  height: 4rpx;\n  background: #007aff;\n  border-radius: 2rpx;\n}\n\n.tab-text {\n  font-size: 26rpx;\n  color: #333;\n}\n\n.tab-item.active .tab-text {\n  color: #007aff;\n  font-weight: 500;\n}\n\n.tab-count {\n  background: #ff4757;\n  color: #fff;\n  font-size: 18rpx;\n  padding: 4rpx 8rpx;\n  border-radius: 10rpx;\n  min-width: 28rpx;\n  text-align: center;\n}\n\n.appointment-list {\n  flex: 1;\n  padding: 20rpx;\n}\n\n.appointment-item {\n  background: #fff;\n  border-radius: 20rpx;\n  margin-bottom: 20rpx;\n  padding: 30rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.appointment-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 20rpx;\n}\n\n.status-badge {\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  font-size: 24rpx;\n  color: #fff;\n}\n\n.status-badge.pending {\n  background: #ff9800;\n}\n\n.status-badge.confirmed {\n  background: #10c560;\n}\n\n.status-badge.completed {\n  background: #007aff;\n}\n\n.status-badge.cancelled {\n  background: #999;\n}\n\n.appointment-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.house-info {\n  display: flex;\n  margin-bottom: 20rpx;\n  padding: 20rpx;\n  background: #f8f9fa;\n  border-radius: 12rpx;\n}\n\n.house-image {\n  width: 120rpx;\n  height: 90rpx;\n  border-radius: 8rpx;\n  overflow: hidden;\n  margin-right: 20rpx;\n  flex-shrink: 0;\n}\n\n.house-image image {\n  width: 100%;\n  height: 100%;\n}\n\n.house-details {\n  flex: 1;\n}\n\n.house-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.house-tags {\n  display: flex;\n  gap: 8rpx;\n  margin-bottom: 8rpx;\n}\n\n.tag {\n  background: #e0e0e0;\n  color: #666;\n  padding: 4rpx 8rpx;\n  border-radius: 6rpx;\n  font-size: 20rpx;\n}\n\n.house-location {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8rpx;\n}\n\n.location-text {\n  font-size: 22rpx;\n  color: #999;\n  margin-left: 6rpx;\n}\n\n.house-price {\n  display: flex;\n  align-items: baseline;\n}\n\n.price {\n  font-size: 26rpx;\n  font-weight: bold;\n  color: #ff6b6b;\n}\n\n.price-unit {\n  font-size: 18rpx;\n  color: #999;\n  margin-left: 4rpx;\n}\n\n.appointment-details {\n  margin-bottom: 20rpx;\n}\n\n.detail-row {\n  display: flex;\n  margin-bottom: 10rpx;\n}\n\n.detail-label {\n  font-size: 26rpx;\n  color: #666;\n  width: 160rpx;\n  flex-shrink: 0;\n}\n\n.detail-value {\n  font-size: 26rpx;\n  color: #333;\n  flex: 1;\n}\n\n.appointment-actions {\n  display: flex;\n  gap: 20rpx;\n}\n\n.action-btn {\n  flex: 1;\n  padding: 15rpx 20rpx;\n  border-radius: 12rpx;\n  font-size: 26rpx;\n  border: none;\n}\n\n.action-btn.cancel {\n  background: #ffebee;\n  color: #f44336;\n}\n\n.action-btn.contact {\n  background: #e3f2fd;\n  color: #007aff;\n}\n\n.action-btn.complete {\n  background: #e8f5e8;\n  color: #10c560;\n}\n\n.load-status {\n  text-align: center;\n  padding: 40rpx;\n  color: #999;\n  font-size: 28rpx;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 100rpx 40rpx;\n}\n\n.empty-state image {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 40rpx;\n}\n\n.empty-text {\n  display: block;\n  font-size: 28rpx;\n  color: #999;\n  margin-bottom: 10rpx;\n}\n\n.empty-tip {\n  display: block;\n  font-size: 24rpx;\n  color: #ccc;\n  margin-bottom: 40rpx;\n}\n\n.browse-btn {\n  background: #007aff;\n  color: #fff;\n  border: none;\n  border-radius: 25rpx;\n  padding: 20rpx 40rpx;\n  font-size: 28rpx;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./appointments.vue?vue&type=style&index=0&id=45ecc94f&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./appointments.vue?vue&type=style&index=0&id=45ecc94f&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751999937\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}