{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端/pages/house/detail.vue?6690", "webpack:///D:/web/project/前端/pages/house/detail.vue?eda6", "webpack:///D:/web/project/前端/pages/house/detail.vue?e41d", "webpack:///D:/web/project/前端/pages/house/detail.vue?f31c", "uni-app:///pages/house/detail.vue", "webpack:///D:/web/project/前端/pages/house/detail.vue?869c", "webpack:///D:/web/project/前端/pages/house/detail.vue?97ca"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "houseId", "houseInfo", "loading", "isFavorited", "mapMarkers", "methods", "getTypeText", "getFacilityText", "loadHouseDetail", "request", "action", "house_id", "result", "id", "latitude", "longitude", "title", "iconPath", "width", "height", "console", "checkFavoriteStatus", "toggleFavorite", "uni", "icon", "url", "previewImage", "urls", "current", "contactPublisher", "itemList", "success", "phoneNumber", "makeAppointment", "shareHouse", "goBack", "onLoad", "onShow"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACqC;;;AAG1F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrCA;AAAA;AAAA;AAAA;AAAioB,CAAgB,+nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACoIrpB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;QAAA;MAAA;MACA;IACA;IAEA;IACAC;MACA;QAAA;MAAA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAGAC;kBACAC;kBACAX;oBACAY;kBACA;gBACA;cAAA;gBALAC;gBAOA;kBACA;;kBAEA;kBACA;oBACA;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;oBACA;kBACA;;kBAEA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAZ;kBACAC;kBACAX;oBACAY;kBACA;gBACA;cAAA;gBALAC;gBAOA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAQ;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAC;kBACAP;kBACAQ;gBACA;gBACAD;kBACAE;gBACA;gBAAA;cAAA;gBAAA;gBAKAf;gBAAA;gBAAA,OACAD;kBACAC;kBACAX;oBACAY;kBACA;gBACA;cAAA;gBALAC;gBAOA;kBACA;kBACAW;oBACAP;oBACAQ;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAJ;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAM;MACAH;QACAI;QACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACAN;UACAP;UACAQ;QACA;QACA;MACA;MAEAD;QACAO;QACAC;UACA;YACA;YACAR;cACAS;YACA;UACA;YACA;YACA;cACAT;gBACAxB;gBACAgC;kBACAR;oBACAP;oBACAQ;kBACA;gBACA;cACA;YACA;cACAD;gBACAP;gBACAQ;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAS;MACA;QACAV;UACAP;UACAQ;QACA;QACAD;UACAE;QACA;QACA;MACA;MAEAF;QACAP;QACAQ;MACA;IACA;IAEA;IACAU;MACAX;QACAP;QACAQ;MACA;IACA;IAEA;IACAW;MACAZ;IACA;EACA;EAEAa;IACA;IACA;MACA;IACA;MACA;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5VA;AAAA;AAAA;AAAA;AAAw7B,CAAgB,k5BAAG,EAAC,C;;;;;;;;;;;ACA58B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/house/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/house/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=4265f319&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=4265f319&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4265f319\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/house/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=4265f319&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.houseInfo._id\n    ? !_vm.houseInfo.images || _vm.houseInfo.images.length === 0\n    : null\n  var m0 = _vm.houseInfo._id ? _vm.getTypeText(_vm.houseInfo.type) : null\n  var g1 = _vm.houseInfo._id\n    ? _vm.houseInfo.facilities && _vm.houseInfo.facilities.length > 0\n    : null\n  var l0 =\n    _vm.houseInfo._id && g1\n      ? _vm.__map(_vm.houseInfo.facilities, function (facility, __i0__) {\n          var $orig = _vm.__get_orig(facility)\n          var m1 = _vm.getFacilityText(facility)\n          return {\n            $orig: $orig,\n            m1: m1,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        g1: g1,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"detail-container\" v-if=\"houseInfo._id\">\n    <!-- 图片轮播 -->\n    <swiper class=\"house-swiper\" indicator-dots indicator-color=\"rgba(255,255,255,0.5)\" indicator-active-color=\"#fff\">\n      <swiper-item v-for=\"(image, index) in houseInfo.images\" :key=\"index\">\n        <image :src=\"image\" mode=\"aspectFill\" @click=\"previewImage(index)\"></image>\n      </swiper-item>\n      <swiper-item v-if=\"!houseInfo.images || houseInfo.images.length === 0\">\n        <image src=\"/static/default-house.png\" mode=\"aspectFill\"></image>\n      </swiper-item>\n    </swiper>\n    \n    <!-- 基本信息 -->\n    <view class=\"info-section\">\n      <view class=\"price-info\">\n        <text class=\"price\">¥{{ houseInfo.price }}</text>\n        <text class=\"price-unit\">/月</text>\n        <text class=\"deposit\" v-if=\"houseInfo.deposit\">押金: ¥{{ houseInfo.deposit }}</text>\n      </view>\n      <text class=\"title\">{{ houseInfo.title }}</text>\n      <view class=\"tags\">\n        <text class=\"tag type\">{{ getTypeText(houseInfo.type) }}</text>\n        <text class=\"tag\" v-if=\"houseInfo.room_count\">{{ houseInfo.room_count }}室</text>\n        <text class=\"tag\" v-if=\"houseInfo.hall_count\">{{ houseInfo.hall_count }}厅</text>\n        <text class=\"tag\" v-if=\"houseInfo.bathroom_count\">{{ houseInfo.bathroom_count }}卫</text>\n        <text class=\"tag\" v-if=\"houseInfo.area\">{{ houseInfo.area }}㎡</text>\n      </view>\n      <view class=\"location\">\n        <uni-icons type=\"location\" size=\"14\" color=\"#999\"></uni-icons>\n        <text class=\"location-text\">{{ houseInfo.location.district }} {{ houseInfo.location.address }}</text>\n      </view>\n    </view>\n    \n    <!-- 房屋详情 -->\n    <view class=\"detail-section\">\n      <view class=\"section-title\">房屋详情</view>\n      <view class=\"detail-grid\">\n        <view class=\"detail-item\" v-if=\"houseInfo.orientation\">\n          <text class=\"detail-label\">朝向</text>\n          <text class=\"detail-value\">{{ houseInfo.orientation }}</text>\n        </view>\n        <view class=\"detail-item\" v-if=\"houseInfo.floor\">\n          <text class=\"detail-label\">楼层</text>\n          <text class=\"detail-value\">{{ houseInfo.floor }}/{{ houseInfo.total_floors }}层</text>\n        </view>\n        <view class=\"detail-item\" v-if=\"houseInfo.decoration\">\n          <text class=\"detail-label\">装修</text>\n          <text class=\"detail-value\">{{ houseInfo.decoration }}</text>\n        </view>\n      </view>\n      \n      <view class=\"description\" v-if=\"houseInfo.description\">\n        <text class=\"desc-title\">房源描述</text>\n        <text class=\"desc-content\">{{ houseInfo.description }}</text>\n      </view>\n    </view>\n    \n    <!-- 房屋设施 -->\n    <view class=\"facilities-section\" v-if=\"houseInfo.facilities && houseInfo.facilities.length > 0\">\n      <view class=\"section-title\">房屋设施</view>\n      <view class=\"facilities-grid\">\n        <view class=\"facility-item\" v-for=\"facility in houseInfo.facilities\" :key=\"facility\">\n          <uni-icons type=\"checkmarkempty\" size=\"16\" color=\"#10c560\"></uni-icons>\n          <text class=\"facility-text\">{{ getFacilityText(facility) }}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 发布者信息 -->\n    <view class=\"publisher-section\" v-if=\"houseInfo.publisher_info\">\n      <view class=\"section-title\">发布者信息</view>\n      <view class=\"publisher-info\">\n        <image\n          class=\"publisher-avatar\"\n          :src=\"houseInfo.publisher_info.avatar || '/static/default-avatar.png'\"\n          mode=\"aspectFill\"\n        ></image>\n        <view class=\"publisher-details\">\n          <text class=\"publisher-name\">{{ houseInfo.publisher_info.nickname || '发布者' }}</text>\n          <text class=\"publisher-verified\" v-if=\"houseInfo.publisher_info.landlord_info && houseInfo.publisher_info.landlord_info.verified\">\n            <uni-icons type=\"checkmarkempty\" size=\"12\" color=\"#10c560\"></uni-icons>\n            已认证\n          </text>\n        </view>\n        <button class=\"contact-btn\" @click=\"contactPublisher\">联系发布者</button>\n      </view>\n    </view>\n    \n    <!-- 地图位置 -->\n    <view class=\"map-section\">\n      <view class=\"section-title\">位置信息</view>\n      <map \n        class=\"house-map\"\n        :latitude=\"houseInfo.location.latitude\"\n        :longitude=\"houseInfo.location.longitude\"\n        :markers=\"mapMarkers\"\n        :show-location=\"true\"\n      ></map>\n      <text class=\"map-address\">{{ houseInfo.location.address }}</text>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"bottom-actions\">\n      <view class=\"action-item\" @click=\"toggleFavorite\">\n        <uni-icons \n          :type=\"isFavorited ? 'heart-filled' : 'heart'\" \n          size=\"24\" \n          :color=\"isFavorited ? '#ff6b6b' : '#666'\"\n        ></uni-icons>\n        <text class=\"action-text\">{{ isFavorited ? '已收藏' : '收藏' }}</text>\n      </view>\n      <view class=\"action-item\" @click=\"shareHouse\">\n        <uni-icons type=\"redo\" size=\"24\" color=\"#666\"></uni-icons>\n        <text class=\"action-text\">分享</text>\n      </view>\n      <button class=\"appointment-btn\" @click=\"makeAppointment\">预约看房</button>\n    </view>\n  </view>\n  \n  <!-- 加载状态 -->\n  <view class=\"loading-container\" v-else-if=\"loading\">\n    <text class=\"loading-text\">加载中...</text>\n  </view>\n  \n  <!-- 空状态 -->\n  <view class=\"empty-container\" v-else>\n    <text class=\"empty-text\">房源不存在或已下线</text>\n    <button class=\"back-btn\" @click=\"goBack\">返回</button>\n  </view>\n</template>\n\n<script>\nimport request from '@/utils/request.js'\nimport { checkLogin, formatRelativeTime } from '@/utils/common.js'\nimport { HOUSE_TYPES, FACILITIES } from '@/common/config.js'\n\nexport default {\n  data() {\n    return {\n      houseId: '',\n      houseInfo: {},\n      loading: true,\n      isFavorited: false,\n      mapMarkers: []\n    }\n  },\n  methods: {\n    // 获取房源类型文本\n    getTypeText(type) {\n      const typeItem = HOUSE_TYPES.find(item => item.value === type)\n      return typeItem ? typeItem.label : type\n    },\n    \n    // 获取设施文本\n    getFacilityText(facility) {\n      const facilityItem = FACILITIES.find(item => item.value === facility)\n      return facilityItem ? facilityItem.label : facility\n    },\n    \n    // 加载房源详情\n    async loadHouseDetail() {\n      this.loading = true\n      \n      try {\n        const result = await request.callFunction('house-management', {\n          action: 'getHouseDetail',\n          data: {\n            house_id: this.houseId\n          }\n        })\n        \n        if (result.code === 0) {\n          this.houseInfo = result.data\n          \n          // 设置地图标记\n          if (this.houseInfo.location.latitude && this.houseInfo.location.longitude) {\n            this.mapMarkers = [{\n              id: 1,\n              latitude: this.houseInfo.location.latitude,\n              longitude: this.houseInfo.location.longitude,\n              title: this.houseInfo.title,\n              iconPath: '/static/map-marker.png',\n              width: 30,\n              height: 30\n            }]\n          }\n          \n          // 检查收藏状态\n          this.checkFavoriteStatus()\n        }\n      } catch (error) {\n        console.error('加载房源详情失败:', error)\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 检查收藏状态\n    async checkFavoriteStatus() {\n      if (!checkLogin()) return\n      \n      try {\n        const result = await request.callFunction('favorite-management', {\n          action: 'checkFavoriteStatus',\n          data: {\n            house_id: this.houseId\n          }\n        })\n        \n        if (result.code === 0) {\n          this.isFavorited = result.data.is_favorited\n        }\n      } catch (error) {\n        console.error('检查收藏状态失败:', error)\n      }\n    },\n    \n    // 切换收藏状态\n    async toggleFavorite() {\n      if (!checkLogin()) {\n        uni.showToast({\n          title: '请先登录',\n          icon: 'none'\n        })\n        uni.navigateTo({\n          url: '/pages/login/login'\n        })\n        return\n      }\n      \n      try {\n        const action = this.isFavorited ? 'removeFavorite' : 'addFavorite'\n        const result = await request.callFunction('favorite-management', {\n          action,\n          data: {\n            house_id: this.houseId\n          }\n        })\n        \n        if (result.code === 0) {\n          this.isFavorited = !this.isFavorited\n          uni.showToast({\n            title: this.isFavorited ? '收藏成功' : '取消收藏',\n            icon: 'success'\n          })\n        }\n      } catch (error) {\n        console.error('收藏操作失败:', error)\n      }\n    },\n    \n    // 预览图片\n    previewImage(index) {\n      uni.previewImage({\n        urls: this.houseInfo.images,\n        current: index\n      })\n    },\n    \n    // 联系发布者\n    contactPublisher() {\n      if (!this.houseInfo.contact) {\n        uni.showToast({\n          title: '暂无联系方式',\n          icon: 'none'\n        })\n        return\n      }\n      \n      uni.showActionSheet({\n        itemList: ['拨打电话', '复制微信号'],\n        success: (res) => {\n          if (res.tapIndex === 0) {\n            // 拨打电话\n            uni.makePhoneCall({\n              phoneNumber: this.houseInfo.contact.phone\n            })\n          } else if (res.tapIndex === 1) {\n            // 复制微信号\n            if (this.houseInfo.contact.wechat) {\n              uni.setClipboardData({\n                data: this.houseInfo.contact.wechat,\n                success: () => {\n                  uni.showToast({\n                    title: '微信号已复制',\n                    icon: 'success'\n                  })\n                }\n              })\n            } else {\n              uni.showToast({\n                title: '暂无微信号',\n                icon: 'none'\n              })\n            }\n          }\n        }\n      })\n    },\n    \n    // 预约看房\n    makeAppointment() {\n      if (!checkLogin()) {\n        uni.showToast({\n          title: '请先登录',\n          icon: 'none'\n        })\n        uni.navigateTo({\n          url: '/pages/login/login'\n        })\n        return\n      }\n      \n      uni.showToast({\n        title: '功能开发中',\n        icon: 'none'\n      })\n    },\n    \n    // 分享房源\n    shareHouse() {\n      uni.showToast({\n        title: '功能开发中',\n        icon: 'none'\n      })\n    },\n    \n    // 返回\n    goBack() {\n      uni.navigateBack()\n    }\n  },\n  \n  onLoad(options) {\n    this.houseId = options.id\n    if (this.houseId) {\n      this.loadHouseDetail()\n    } else {\n      this.loading = false\n    }\n  },\n  \n  onShow() {\n    // 从其他页面返回时重新检查收藏状态\n    if (this.houseInfo._id) {\n      this.checkFavoriteStatus()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.detail-container {\n  background: #f8f9fa;\n  min-height: 100vh;\n  padding-bottom: 120rpx;\n}\n\n.house-swiper {\n  height: 500rpx;\n}\n\n.house-swiper image {\n  width: 100%;\n  height: 100%;\n}\n\n.info-section {\n  background: #fff;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n}\n\n.price-info {\n  display: flex;\n  align-items: baseline;\n  margin-bottom: 20rpx;\n}\n\n.price {\n  font-size: 48rpx;\n  font-weight: bold;\n  color: #ff6b6b;\n}\n\n.price-unit {\n  font-size: 28rpx;\n  color: #999;\n  margin-left: 8rpx;\n}\n\n.deposit {\n  font-size: 24rpx;\n  color: #666;\n  margin-left: 30rpx;\n  background: #f0f0f0;\n  padding: 8rpx 12rpx;\n  border-radius: 8rpx;\n}\n\n.title {\n  display: block;\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n  line-height: 1.4;\n}\n\n.tags {\n  display: flex;\n  gap: 10rpx;\n  margin-bottom: 20rpx;\n  flex-wrap: wrap;\n}\n\n.tag {\n  background: #f0f0f0;\n  color: #666;\n  padding: 8rpx 12rpx;\n  border-radius: 8rpx;\n  font-size: 24rpx;\n}\n\n.tag.type {\n  background: #e3f2fd;\n  color: #007aff;\n}\n\n.location {\n  display: flex;\n  align-items: center;\n}\n\n.location-text {\n  font-size: 28rpx;\n  color: #666;\n  margin-left: 8rpx;\n}\n\n.detail-section, .facilities-section, .landlord-section, .map-section {\n  background: #fff;\n  margin-bottom: 20rpx;\n  padding: 30rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 30rpx;\n  padding-bottom: 20rpx;\n  border-bottom: 2rpx solid #f0f0f0;\n}\n\n.detail-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 30rpx;\n  margin-bottom: 30rpx;\n}\n\n.detail-item {\n  flex: 1;\n  min-width: 200rpx;\n  text-align: center;\n}\n\n.detail-label {\n  display: block;\n  font-size: 24rpx;\n  color: #999;\n  margin-bottom: 10rpx;\n}\n\n.detail-value {\n  display: block;\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n.description {\n  margin-top: 30rpx;\n}\n\n.desc-title {\n  display: block;\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n  margin-bottom: 15rpx;\n}\n\n.desc-content {\n  display: block;\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n}\n\n.facilities-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 20rpx;\n}\n\n.facility-item {\n  display: flex;\n  align-items: center;\n  background: #f8f9fa;\n  padding: 15rpx 20rpx;\n  border-radius: 25rpx;\n  min-width: 120rpx;\n}\n\n.facility-text {\n  font-size: 26rpx;\n  color: #333;\n  margin-left: 10rpx;\n}\n\n.publisher-info {\n  display: flex;\n  align-items: center;\n}\n\n.publisher-avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 40rpx;\n  margin-right: 20rpx;\n}\n\n.publisher-details {\n  flex: 1;\n}\n\n.publisher-name {\n  display: block;\n  font-size: 30rpx;\n  color: #333;\n  font-weight: 500;\n  margin-bottom: 8rpx;\n}\n\n.landlord-verified {\n  display: flex;\n  align-items: center;\n  font-size: 24rpx;\n  color: #10c560;\n}\n\n.contact-btn {\n  background: #007aff;\n  color: #fff;\n  border: none;\n  border-radius: 25rpx;\n  padding: 15rpx 30rpx;\n  font-size: 26rpx;\n}\n\n.house-map {\n  width: 100%;\n  height: 300rpx;\n  border-radius: 12rpx;\n  margin-bottom: 20rpx;\n}\n\n.map-address {\n  display: block;\n  font-size: 28rpx;\n  color: #666;\n  text-align: center;\n}\n\n.bottom-actions {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: #fff;\n  padding: 20rpx 30rpx;\n  border-top: 1rpx solid #f0f0f0;\n  display: flex;\n  align-items: center;\n  gap: 30rpx;\n}\n\n.action-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8rpx;\n}\n\n.action-text {\n  font-size: 24rpx;\n  color: #666;\n}\n\n.appointment-btn {\n  flex: 1;\n  height: 80rpx;\n  background: linear-gradient(45deg, #007aff, #0056d3);\n  color: #fff;\n  border: none;\n  border-radius: 40rpx;\n  font-size: 30rpx;\n  font-weight: bold;\n}\n\n.loading-container, .empty-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100vh;\n  padding: 40rpx;\n}\n\n.loading-text, .empty-text {\n  font-size: 28rpx;\n  color: #999;\n  margin-bottom: 40rpx;\n}\n\n.back-btn {\n  background: #007aff;\n  color: #fff;\n  border: none;\n  border-radius: 25rpx;\n  padding: 20rpx 40rpx;\n  font-size: 28rpx;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=4265f319&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=4265f319&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753754594746\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}