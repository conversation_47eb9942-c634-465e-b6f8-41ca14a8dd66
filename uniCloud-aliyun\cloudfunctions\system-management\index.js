'use strict';

const uniID = require('uni-id-common');

// 通用 checkToken 函数
async function checkUserToken(token, context) {
  const uniIdIns = uniID.createInstance({ context });
  const payload = await uniIdIns.checkToken(token);

  if (payload.errCode === 0) {
    return {
      code: 0,
      uid: payload.uid,
      userInfo: payload.userInfo
    };
  } else {
    return {
      code: payload.errCode || 401,
      message: payload.errMsg || '身份验证失败'
    };
  }
}

exports.main = async (event, context) => {
  const { action, data } = event;
  
  try {
    switch (action) {
      case 'getSystemConfig':
        return await getSystemConfig(event, context);
      case 'updateSystemConfig':
        return await updateSystemConfig(event, context);
      case 'getStatistics':
        return await getStatistics(event, context);
      case 'auditHouse':
        return await auditHouse(event, context);
      case 'manageUser':
        return await manageUser(event, context);
      case 'handleReport':
        return await handleReport(event, context);
      case 'getHouseList':
        return await getHouseList(event, context);
      case 'getUserList':
        return await getUserList(event, context);
      case 'getReportList':
        return await getReportList(event, context);
      default:
        return {
          code: 400,
          message: '无效的操作'
        };
    }
  } catch (error) {
    console.error('系统管理云函数执行错误:', error);
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message
    };
  }
};

// 检查管理员权限
async function checkAdminPermission(token, context) {
  const payload = await checkUserToken(token, context);
  if (payload.code !== 0) {
    return payload;
  }
  
  // 检查是否是管理员
  const db = uniCloud.database();
  const userRes = await db.collection('uni-id-users').doc(payload.uid).get();
  
  if (userRes.data.length === 0) {
    return {
      code: 404,
      message: '用户不存在'
    };
  }
  
  const user = userRes.data[0];
  if (!user.role || !user.role.includes('admin')) {
    return {
      code: 403,
      message: '无管理员权限'
    };
  }
  
  return {
    code: 0,
    uid: payload.uid,
    user: user
  };
}

// 获取系统配置
async function getSystemConfig(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }
  
  try {
    const db = uniCloud.database();
    const result = await db.collection('system_config').get();
    
    const config = {};
    result.data.forEach(item => {
      config[item.key] = item.value;
    });
    
    return {
      code: 0,
      message: '获取成功',
      data: config
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取失败',
      error: error.message
    };
  }
}

// 更新系统配置
async function updateSystemConfig(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  const { configs } = event.data;
  
  if (!configs || typeof configs !== 'object') {
    return {
      code: 400,
      message: '配置数据格式错误'
    };
  }
  
  try {
    const db = uniCloud.database();
    
    // 批量更新配置
    const promises = Object.keys(configs).map(key => {
      return db.collection('system_config').where({
        key: key
      }).update({
        value: configs[key],
        update_date: new Date()
      });
    });
    
    await Promise.all(promises);
    
    return {
      code: 0,
      message: '更新成功'
    };
  } catch (error) {
    return {
      code: 500,
      message: '更新失败',
      error: error.message
    };
  }
}

// 获取统计数据
async function getStatistics(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  try {
    const db = uniCloud.database();
    
    // 用户统计
    const totalUsersResult = await db.collection('uni-id-users').count();
    const studentsResult = await db.collection('uni-id-users').where({
      role: db.command.in(['student'])
    }).count();
    const landlordsResult = await db.collection('uni-id-users').where({
      role: db.command.in(['landlord'])
    }).count();
    
    // 房源统计
    const totalHousesResult = await db.collection('houses').count();
    const availableHousesResult = await db.collection('houses').where({
      status: 'available'
    }).count();
    const rentedHousesResult = await db.collection('houses').where({
      status: 'rented'
    }).count();
    const pendingHousesResult = await db.collection('houses').where({
      is_verified: false
    }).count();
    
    // 预约统计
    const totalAppointmentsResult = await db.collection('appointments').count();
    const pendingAppointmentsResult = await db.collection('appointments').where({
      status: 'pending'
    }).count();
    
    // 收藏统计
    const totalFavoritesResult = await db.collection('favorites').count();
    
    // 举报统计
    const totalReportsResult = await db.collection('reports').count();
    const pendingReportsResult = await db.collection('reports').where({
      status: 'pending'
    }).count();
    
    // 今日新增统计
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const todayUsersResult = await db.collection('uni-id-users').where({
      register_date: db.command.gte(today)
    }).count();
    
    const todayHousesResult = await db.collection('houses').where({
      publish_date: db.command.gte(today)
    }).count();
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        users: {
          total: totalUsersResult.total,
          students: studentsResult.total,
          landlords: landlordsResult.total,
          todayNew: todayUsersResult.total
        },
        houses: {
          total: totalHousesResult.total,
          available: availableHousesResult.total,
          rented: rentedHousesResult.total,
          pending: pendingHousesResult.total,
          todayNew: todayHousesResult.total
        },
        appointments: {
          total: totalAppointmentsResult.total,
          pending: pendingAppointmentsResult.total
        },
        favorites: {
          total: totalFavoritesResult.total
        },
        reports: {
          total: totalReportsResult.total,
          pending: pendingReportsResult.total
        }
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取失败',
      error: error.message
    };
  }
}

// 房源审核
async function auditHouse(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  const { house_id, is_verified, reject_reason } = event.data;
  
  if (!house_id || is_verified === undefined) {
    return {
      code: 400,
      message: '房源ID和审核结果不能为空'
    };
  }
  
  try {
    const db = uniCloud.database();
    
    // 检查房源是否存在
    const houseRes = await db.collection('houses').doc(house_id).get();
    if (houseRes.data.length === 0) {
      return {
        code: 404,
        message: '房源不存在'
      };
    }
    
    const house = houseRes.data[0];
    
    // 更新审核状态
    await db.collection('houses').doc(house_id).update({
      is_verified: is_verified,
      audit_date: new Date(),
      auditor_id: adminCheck.uid,
      reject_reason: reject_reason || ''
    });
    
    // 发送审核结果通知给房东
    const notificationContent = is_verified ? 
      '您的房源已通过审核，现在可以正常展示了' : 
      `您的房源审核未通过，原因：${reject_reason || '不符合平台规范'}`;
    
    await db.collection('messages').add({
      from_user_id: adminCheck.uid,
      to_user_id: house.landlord_id,
      type: 'system',
      title: '房源审核结果',
      content: notificationContent,
      related_id: house_id,
      is_read: false,
      create_date: new Date()
    });
    
    return {
      code: 0,
      message: '审核完成'
    };
  } catch (error) {
    return {
      code: 500,
      message: '审核失败',
      error: error.message
    };
  }
}

// 用户管理
async function manageUser(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  const { user_id, action: userAction, reason } = event.data;
  
  if (!user_id || !userAction) {
    return {
      code: 400,
      message: '用户ID和操作类型不能为空'
    };
  }
  
  const validActions = ['disable', 'enable', 'delete'];
  if (!validActions.includes(userAction)) {
    return {
      code: 400,
      message: '无效的操作类型'
    };
  }
  
  try {
    const db = uniCloud.database();
    
    // 检查用户是否存在
    const userRes = await db.collection('uni-id-users').doc(user_id).get();
    if (userRes.data.length === 0) {
      return {
        code: 404,
        message: '用户不存在'
      };
    }
    
    const user = userRes.data[0];
    
    // 不能操作管理员账户
    if (user.role && user.role.includes('admin')) {
      return {
        code: 403,
        message: '不能操作管理员账户'
      };
    }
    
    let updateData = {};
    let notificationContent = '';
    
    switch (userAction) {
      case 'disable':
        updateData.status = 1; // 禁用
        notificationContent = `您的账户已被禁用，原因：${reason || '违反平台规定'}`;
        break;
      case 'enable':
        updateData.status = 0; // 启用
        notificationContent = '您的账户已恢复正常使用';
        break;
      case 'delete':
        // 删除用户相关数据
        await db.collection('houses').where({
          landlord_id: user_id
        }).remove();
        await db.collection('favorites').where({
          user_id: user_id
        }).remove();
        await db.collection('appointments').where({
          user_id: user_id
        }).remove();
        await db.collection('messages').where({
          to_user_id: user_id
        }).remove();
        
        // 删除用户
        await db.collection('uni-id-users').doc(user_id).remove();
        
        return {
          code: 0,
          message: '用户删除成功'
        };
    }
    
    // 更新用户状态
    await db.collection('uni-id-users').doc(user_id).update(updateData);
    
    // 发送通知
    if (notificationContent) {
      await db.collection('messages').add({
        from_user_id: adminCheck.uid,
        to_user_id: user_id,
        type: 'system',
        title: '账户状态变更',
        content: notificationContent,
        related_id: '',
        is_read: false,
        create_date: new Date()
      });
    }
    
    return {
      code: 0,
      message: '操作成功'
    };
  } catch (error) {
    return {
      code: 500,
      message: '操作失败',
      error: error.message
    };
  }
}

// 处理举报
async function handleReport(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  const { report_id, status, result } = event.data;

  if (!report_id || !status) {
    return {
      code: 400,
      message: '举报ID和处理状态不能为空'
    };
  }

  const validStatus = ['processed', 'rejected'];
  if (!validStatus.includes(status)) {
    return {
      code: 400,
      message: '无效的处理状态'
    };
  }

  try {
    const db = uniCloud.database();

    // 检查举报是否存在
    const reportRes = await db.collection('reports').doc(report_id).get();
    if (reportRes.data.length === 0) {
      return {
        code: 404,
        message: '举报不存在'
      };
    }

    const report = reportRes.data[0];

    // 更新举报状态
    await db.collection('reports').doc(report_id).update({
      status: status,
      result: result || '',
      process_date: new Date(),
      processor_id: adminCheck.uid
    });

    // 发送处理结果通知给举报者
    const notificationContent = status === 'processed' ?
      `您的举报已处理完成，处理结果：${result || '已核实并处理'}` :
      `您的举报经核实不成立，原因：${result || '未发现违规行为'}`;

    await db.collection('messages').add({
      from_user_id: adminCheck.uid,
      to_user_id: report.reporter_id,
      type: 'system',
      title: '举报处理结果',
      content: notificationContent,
      related_id: report_id,
      is_read: false,
      create_date: new Date()
    });

    return {
      code: 0,
      message: '处理完成'
    };
  } catch (error) {
    return {
      code: 500,
      message: '处理失败',
      error: error.message
    };
  }
}

// 获取房源列表（管理员）
async function getHouseList(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  const { page = 1, pageSize = 10, status, is_verified, keyword } = event.data;

  try {
    const db = uniCloud.database();
    let query = db.collection('houses');

    // 状态筛选
    if (status) {
      query = query.where({
        status: status
      });
    }

    // 审核状态筛选
    if (is_verified !== undefined) {
      query = query.where({
        is_verified: is_verified
      });
    }

    // 关键词搜索
    if (keyword) {
      const regex = new RegExp(keyword, 'i');
      query = query.where(db.command.or([
        { title: regex },
        { description: regex },
        { 'location.address': regex }
      ]));
    }

    const skip = (page - 1) * pageSize;
    const result = await query.skip(skip).limit(pageSize).orderBy('publish_date', 'desc').get();

    // 获取房东信息
    const landlordIds = result.data.map(item => item.landlord_id);
    let landlords = [];

    if (landlordIds.length > 0) {
      const landlordRes = await db.collection('uni-id-users').where({
        _id: db.command.in(landlordIds)
      }).field({
        nickname: true,
        username: true,
        mobile: true
      }).get();
      landlords = landlordRes.data;
    }

    // 组合数据
    const list = result.data.map(house => {
      const landlord = landlords.find(l => l._id === house.landlord_id);
      return {
        ...house,
        landlord_info: landlord || null
      };
    });

    // 获取总数
    const countResult = await query.count();

    return {
      code: 0,
      message: '获取成功',
      data: {
        list: list,
        total: countResult.total,
        page: page,
        pageSize: pageSize
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取失败',
      error: error.message
    };
  }
}

// 获取用户列表（管理员）
async function getUserList(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  const { page = 1, pageSize = 10, role, status, keyword } = event.data;

  try {
    const db = uniCloud.database();
    let query = db.collection('uni-id-users');

    // 角色筛选
    if (role) {
      query = query.where({
        role: db.command.in([role])
      });
    }

    // 状态筛选
    if (status !== undefined) {
      query = query.where({
        status: status
      });
    }

    // 关键词搜索
    if (keyword) {
      const regex = new RegExp(keyword, 'i');
      query = query.where(db.command.or([
        { username: regex },
        { nickname: regex },
        { mobile: regex }
      ]));
    }

    const skip = (page - 1) * pageSize;
    const result = await query.skip(skip).limit(pageSize).orderBy('register_date', 'desc').field({
      password: false,
      token: false
    }).get();

    // 获取总数
    const countResult = await query.count();

    return {
      code: 0,
      message: '获取成功',
      data: {
        list: result.data,
        total: countResult.total,
        page: page,
        pageSize: pageSize
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取失败',
      error: error.message
    };
  }
}

// 获取举报列表（管理员）
async function getReportList(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  const { page = 1, pageSize = 10, status, target_type } = event.data;

  try {
    const db = uniCloud.database();
    let query = db.collection('reports');

    // 状态筛选
    if (status) {
      query = query.where({
        status: status
      });
    }

    // 举报类型筛选
    if (target_type) {
      query = query.where({
        target_type: target_type
      });
    }

    const skip = (page - 1) * pageSize;
    const result = await query.skip(skip).limit(pageSize).orderBy('create_date', 'desc').get();

    // 获取举报者信息
    const reporterIds = result.data.map(item => item.reporter_id);
    let reporters = [];

    if (reporterIds.length > 0) {
      const reporterRes = await db.collection('uni-id-users').where({
        _id: db.command.in(reporterIds)
      }).field({
        nickname: true,
        username: true
      }).get();
      reporters = reporterRes.data;
    }

    // 组合数据
    const list = result.data.map(report => {
      const reporter = reporters.find(r => r._id === report.reporter_id);
      return {
        ...report,
        reporter_info: reporter || null
      };
    });

    // 获取总数
    const countResult = await query.count();

    return {
      code: 0,
      message: '获取成功',
      data: {
        list: list,
        total: countResult.total,
        page: page,
        pageSize: pageSize
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取失败',
      error: error.message
    };
  }
}
