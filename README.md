# 毕业租房信息平台

基于 UniApp + Vue2 + uniCloud 开发的微信小程序毕业租房信息平台

## 项目简介

这是一个专为毕业生打造的租房信息平台，提供房源发布、搜索、收藏、预约看房等功能。采用前后端分离架构，前端使用 UniApp 开发，后端使用 uniCloud 云开发。

**🎯 核心特性：统一用户登录系统**
- ✅ 统一的用户注册/登录系统
- ✅ 多身份认证（学生/房东身份可选）
- ✅ 所有用户均可发布房源
- ✅ 后端管理员统一管理所有用户

## 技术栈

### 前端
- **框架**: UniApp (基于 Vue2)
- **开发工具**: HBuilderX
- **UI组件**: uni-ui
- **状态管理**: Vuex (可选)
- **网络请求**: uniCloud.callFunction

### 后端
- **云服务**: uniCloud (阿里云版本)
- **数据库**: 云数据库 (MongoDB)
- **云函数**: Node.js
- **云存储**: 云存储服务
- **用户认证**: uni-id-common

## 功能特性

### 统一用户系统
- ✅ 统一的用户注册/登录（无学生/房东区分）
- ✅ 个人信息管理
- ✅ 可选身份认证（学生/房东身份信息）
- ✅ 房源浏览与搜索
- ✅ 房源收藏
- ✅ 预约看房
- ✅ 消息通知
- ✅ 房源发布（所有用户均可发布）
- ✅ 房源管理（编辑/删除/上下线）
- ✅ 预约管理
- ✅ 房源数据统计

### 管理员功能
- ✅ 统一用户管理
- ✅ 房源审核
- ✅ 数据统计
- ✅ 举报处理
- ✅ 系统配置

## 数据库设计

### 主要数据表
- `uni-id-users`: 用户表（统一用户系统）
- `houses`: 房源表（publisher_id 替代 landlord_id）
- `favorites`: 收藏表
- `appointments`: 预约表
- `messages`: 消息表
- `system_config`: 系统配置表
- `reports`: 举报表

详细的数据库设计请参考 `docs/database-design.md`

## 云函数设计

### 云函数列表
- `user-auth`: 统一用户认证相关
- `house-management`: 房源管理
- `favorite-management`: 收藏管理
- `appointment-management`: 预约管理
- `message-management`: 消息管理
- `file-upload`: 文件上传
- `system-management`: 系统管理

详细的云函数设计请参考 `docs/cloudfunctions-design.md`

## 开发环境搭建

### 前置要求
- HBuilderX 3.0+
- 微信开发者工具
- uniCloud 账号

### 安装步骤

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd 后端
   ```

2. **配置 uniCloud**
   - 在 HBuilderX 中打开项目
   - 右键 `uniCloud-aliyun` 目录
   - 选择"关联云服务空间"
   - 创建或选择已有的云服务空间

3. **上传云函数**
   - 右键各个云函数目录
   - 选择"上传并运行"

4. **初始化数据库**
   - 在 uniCloud 控制台创建数据库集合
   - 导入初始数据（可选）

5. **配置小程序**
   - 在微信公众平台注册小程序
   - 配置服务器域名
   - 在 `manifest.json` 中配置 AppID

6. **运行项目**
   - 在 HBuilderX 中运行到微信开发者工具
   - 或运行到浏览器进行调试

## 系统架构说明

### 统一用户系统架构
```
用户注册/登录 → 统一用户表 → 可选身份认证 → 功能权限
     ↓              ↓              ↓           ↓
  单一入口      role: ['user']   student_info   房源发布
                                landlord_info   房源管理
                                              预约管理
```

### 主要改进
1. **删除学生/房东分离**：统一为用户登录系统
2. **后端联动**：管理员可统一管理所有用户
3. **身份认证可选**：用户可选择认证学生或房东身份
4. **功能统一**：所有用户均可发布和管理房源

## 更新日志

### v2.0.0 (2024-12-XX) - 统一用户系统
- 🔄 重构：统一学生和房东登录系统
- ✨ 新增：统一用户认证云函数
- 🔧 修改：房源表 landlord_id → publisher_id
- 📝 更新：配置文件和文档

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础的房源发布和浏览功能
- 实现用户注册登录
- 实现收藏和预约功能

## 许可证

MIT License
