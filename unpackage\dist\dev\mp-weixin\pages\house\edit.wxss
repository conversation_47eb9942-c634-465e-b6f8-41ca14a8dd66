
.edit-container.data-v-239e3052 {
  background: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 120rpx;
}
.form-section.data-v-239e3052 {
  background: #fff;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
}
.section-title.data-v-239e3052 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.form-item.data-v-239e3052 {
  margin-bottom: 30rpx;
}
.form-row.data-v-239e3052 {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.form-item.half.data-v-239e3052 {
  flex: 1;
}
.form-item.third.data-v-239e3052 {
  flex: 1;
}
.label.data-v-239e3052 {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}
.input.data-v-239e3052, .textarea.data-v-239e3052 {
  width: 100%;
  background: #f8f9fa;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}
.input.data-v-239e3052:focus, .textarea.data-v-239e3052:focus {
  border-color: #007aff;
  background: #fff;
}
.textarea.data-v-239e3052 {
  height: 120rpx;
  resize: none;
}
.picker.data-v-239e3052 {
  background: #f8f9fa;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  color: #333;
}
.facilities-grid.data-v-239e3052 {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}
.facility-item.data-v-239e3052 {
  background: #f8f9fa;
  border: 2rpx solid transparent;
  border-radius: 25rpx;
  padding: 15rpx 25rpx;
  font-size: 26rpx;
  color: #666;
}
.facility-item.active.data-v-239e3052 {
  background: #e3f2fd;
  border-color: #007aff;
  color: #007aff;
}
.facility-text.data-v-239e3052 {
  font-size: 26rpx;
}
.submit-section.data-v-239e3052 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}
.submit-btn.data-v-239e3052 {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(45deg, #007aff, #0056d3);
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
}
.submit-btn.data-v-239e3052:disabled {
  background: #ccc;
}
.loading-container.data-v-239e3052, .error-container.data-v-239e3052 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 40rpx;
}
.loading-text.data-v-239e3052, .error-text.data-v-239e3052 {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}
.back-btn.data-v-239e3052 {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

