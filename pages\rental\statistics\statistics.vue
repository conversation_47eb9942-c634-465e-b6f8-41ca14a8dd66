<template>
  <view class="statistics">
    <!-- 筛选条件 -->
    <view class="filter-section">
      <view class="filter-row">
        <view class="filter-item">
          <text class="filter-label">时间范围</text>
          <uni-datetime-picker 
            v-model="dateRange" 
            type="daterange"
            :clear-icon="false"
            @change="onDateRangeChange"
          />
        </view>
        <view class="filter-item">
          <text class="filter-label">统计维度</text>
          <uni-data-picker 
            v-model="dimension" 
            :localdata="dimensionOptions"
            @change="onDimensionChange"
          />
        </view>
        <view class="filter-item">
          <button class="filter-btn" @click="exportData">导出数据</button>
        </view>
      </view>
    </view>

    <!-- 统计图表 -->
    <view class="charts-container">
      <!-- 房源统计 -->
      <view class="chart-section">
        <view class="section-title">房源统计分析</view>
        <view class="chart-grid">
          <view class="chart-item">
            <view class="chart-header">
              <text class="chart-title">房源发布趋势</text>
              <view class="chart-legend">
                <view class="legend-item">
                  <view class="legend-color" style="background: #007AFF;"></view>
                  <text>发布数量</text>
                </view>
                <view class="legend-item">
                  <view class="legend-color" style="background: #34C759;"></view>
                  <text>审核通过</text>
                </view>
              </view>
            </view>
            <view class="chart-content">
              <qiun-data-charts 
                type="line"
                :opts="houseTrendOpts"
                :chartData="houseTrendData"
                :loading="chartLoading"
              />
            </view>
          </view>

          <view class="chart-item">
            <view class="chart-header">
              <text class="chart-title">房源类型分布</text>
            </view>
            <view class="chart-content">
              <qiun-data-charts 
                type="pie"
                :opts="houseTypeOpts"
                :chartData="houseTypeData"
                :loading="chartLoading"
              />
            </view>
          </view>

          <view class="chart-item">
            <view class="chart-header">
              <text class="chart-title">房源价格分布</text>
            </view>
            <view class="chart-content">
              <qiun-data-charts 
                type="column"
                :opts="housePriceOpts"
                :chartData="housePriceData"
                :loading="chartLoading"
              />
            </view>
          </view>

          <view class="chart-item">
            <view class="chart-header">
              <text class="chart-title">房源地区分布</text>
            </view>
            <view class="chart-content">
              <qiun-data-charts 
                type="bar"
                :opts="houseAreaOpts"
                :chartData="houseAreaData"
                :loading="chartLoading"
              />
            </view>
          </view>
        </view>
      </view>

      <!-- 用户统计 -->
      <view class="chart-section">
        <view class="section-title">用户统计分析</view>
        <view class="chart-grid">
          <view class="chart-item">
            <view class="chart-header">
              <text class="chart-title">用户注册趋势</text>
            </view>
            <view class="chart-content">
              <qiun-data-charts 
                type="line"
                :opts="userTrendOpts"
                :chartData="userTrendData"
                :loading="chartLoading"
              />
            </view>
          </view>

          <view class="chart-item">
            <view class="chart-header">
              <text class="chart-title">用户活跃度</text>
            </view>
            <view class="chart-content">
              <qiun-data-charts 
                type="area"
                :opts="userActiveOpts"
                :chartData="userActiveData"
                :loading="chartLoading"
              />
            </view>
          </view>
        </view>
      </view>

      <!-- 预约统计 -->
      <view class="chart-section">
        <view class="section-title">预约统计分析</view>
        <view class="chart-grid">
          <view class="chart-item">
            <view class="chart-header">
              <text class="chart-title">预约趋势</text>
            </view>
            <view class="chart-content">
              <qiun-data-charts 
                type="line"
                :opts="appointmentTrendOpts"
                :chartData="appointmentTrendData"
                :loading="chartLoading"
              />
            </view>
          </view>

          <view class="chart-item">
            <view class="chart-header">
              <text class="chart-title">预约成功率</text>
            </view>
            <view class="chart-content">
              <qiun-data-charts 
                type="gauge"
                :opts="appointmentRateOpts"
                :chartData="appointmentRateData"
                :loading="chartLoading"
              />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 数据表格 -->
    <view class="data-table-section">
      <view class="section-title">详细数据</view>
      <uni-table 
        :loading="tableLoading"
        border
        stripe
        emptyText="暂无数据"
      >
        <uni-tr>
          <uni-th align="center">日期</uni-th>
          <uni-th align="center">新增房源</uni-th>
          <uni-th align="center">新增用户</uni-th>
          <uni-th align="center">新增预约</uni-th>
          <uni-th align="center">预约成功率</uni-th>
        </uni-tr>
        <uni-tr v-for="(item, index) in tableData" :key="index">
          <uni-td align="center">{{ item.date }}</uni-td>
          <uni-td align="center">{{ item.houses }}</uni-td>
          <uni-td align="center">{{ item.users }}</uni-td>
          <uni-td align="center">{{ item.appointments }}</uni-td>
          <uni-td align="center">{{ item.successRate }}%</uni-td>
        </uni-tr>
      </uni-table>
      
      <!-- 分页 -->
      <view class="pagination">
        <uni-pagination 
          :current="currentPage"
          :total="totalCount"
          :pageSize="pageSize"
          @change="onPageChange"
        />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'RentalStatistics',
  data() {
    return {
      chartLoading: true,
      tableLoading: true,
      
      // 筛选条件
      dateRange: [],
      dimension: 'day',
      dimensionOptions: [
        { value: 'day', text: '按天' },
        { value: 'week', text: '按周' },
        { value: 'month', text: '按月' }
      ],

      // 图表配置和数据
      houseTrendOpts: {
        color: ['#007AFF', '#34C759'],
        padding: [15, 15, 0, 15],
        enableScroll: false,
        legend: {
          show: true
        },
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2
        },
        extra: {
          line: {
            type: 'curve',
            width: 2
          }
        }
      },
      houseTrendData: {},

      houseTypeOpts: {
        color: ['#007AFF', '#34C759', '#FF9500', '#FF3B30'],
        padding: [5, 5, 5, 5],
        enableScroll: false,
        legend: {
          show: true,
          position: 'right'
        },
        extra: {
          pie: {
            activeOpacity: 0.5,
            activeRadius: 10,
            offsetAngle: 0,
            labelWidth: 15,
            border: true,
            borderWidth: 3,
            borderColor: '#FFFFFF'
          }
        }
      },
      houseTypeData: {},

      housePriceOpts: {
        color: ['#007AFF'],
        padding: [15, 15, 0, 15],
        enableScroll: false,
        legend: {
          show: false
        },
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2
        }
      },
      housePriceData: {},

      houseAreaOpts: {
        color: ['#34C759'],
        padding: [15, 15, 0, 15],
        enableScroll: false,
        legend: {
          show: false
        },
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2
        }
      },
      houseAreaData: {},

      userTrendOpts: {
        color: ['#FF9500'],
        padding: [15, 15, 0, 15],
        enableScroll: false,
        legend: {
          show: false
        },
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2
        },
        extra: {
          line: {
            type: 'curve',
            width: 2
          }
        }
      },
      userTrendData: {},

      userActiveOpts: {
        color: ['#8E8E93'],
        padding: [15, 15, 0, 15],
        enableScroll: false,
        legend: {
          show: false
        },
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2
        },
        extra: {
          area: {
            type: 'curve',
            opacity: 0.2,
            addLine: true,
            width: 2
          }
        }
      },
      userActiveData: {},

      appointmentTrendOpts: {
        color: ['#FF3B30'],
        padding: [15, 15, 0, 15],
        enableScroll: false,
        legend: {
          show: false
        },
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2
        },
        extra: {
          line: {
            type: 'curve',
            width: 2
          }
        }
      },
      appointmentTrendData: {},

      appointmentRateOpts: {
        color: ['#34C759'],
        padding: [5, 5, 5, 5],
        enableScroll: false,
        extra: {
          gauge: {
            type: 'default',
            width: 30,
            labelColor: '#666666',
            startAngle: 0.75,
            endAngle: 0.25,
            startNumber: 0,
            endNumber: 100,
            labelFormat: '',
            splitLine: {
              fixRadius: 0,
              splitNumber: 10,
              width: 15,
              color: '#CCCCCC',
              childNumber: 5,
              childWidth: 5
            },
            pointer: {
              width: 24,
              color: 'auto'
            }
          }
        }
      },
      appointmentRateData: {},

      // 表格数据
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0
    }
  },

  onLoad() {
    this.initDateRange()
    this.loadStatisticsData()
  },

  methods: {
    // 初始化日期范围
    initDateRange() {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 30)
      
      this.dateRange = [
        start.toISOString().split('T')[0],
        end.toISOString().split('T')[0]
      ]
    },

    // 加载统计数据
    async loadStatisticsData() {
      try {
        this.chartLoading = true
        this.tableLoading = true

        // 并发加载图表数据和表格数据
        await Promise.all([
          this.loadChartData(),
          this.loadTableData()
        ])

        this.chartLoading = false
        this.tableLoading = false
      } catch (error) {
        console.error('加载统计数据失败:', error)
        uni.showToast({
          title: '加载数据失败',
          icon: 'none'
        })
        this.chartLoading = false
        this.tableLoading = false
      }
    },

    // 加载图表数据
    async loadChartData() {
      // 这里应该调用云函数获取真实数据
      // 暂时使用模拟数据
      
      // 房源趋势数据
      this.houseTrendData = {
        categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
        series: [
          {
            name: '发布数量',
            data: [35, 36, 31, 33, 13, 34]
          },
          {
            name: '审核通过',
            data: [28, 30, 25, 28, 10, 29]
          }
        ]
      }

      // 房源类型分布
      this.houseTypeData = {
        series: [{
          name: '房源类型',
          data: [
            { name: '整租', value: 45 },
            { name: '合租', value: 35 },
            { name: '单间', value: 20 }
          ]
        }]
      }

      // 房源价格分布
      this.housePriceData = {
        categories: ['1000以下', '1000-2000', '2000-3000', '3000-4000', '4000以上'],
        series: [{
          name: '房源数量',
          data: [12, 25, 35, 18, 10]
        }]
      }

      // 房源地区分布
      this.houseAreaData = {
        categories: ['朝阳区', '海淀区', '丰台区', '西城区', '东城区'],
        series: [{
          name: '房源数量',
          data: [45, 38, 32, 28, 25]
        }]
      }

      // 用户注册趋势
      this.userTrendData = {
        categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
        series: [{
          name: '注册用户',
          data: [18, 25, 22, 28, 15, 32]
        }]
      }

      // 用户活跃度
      this.userActiveData = {
        categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
        series: [{
          name: '活跃用户',
          data: [15, 20, 18, 22, 12, 25]
        }]
      }

      // 预约趋势
      this.appointmentTrendData = {
        categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
        series: [{
          name: '预约数量',
          data: [25, 30, 28, 35, 20, 40]
        }]
      }

      // 预约成功率
      this.appointmentRateData = {
        series: [{
          name: '成功率',
          data: 75
        }]
      }
    },

    // 加载表格数据
    async loadTableData() {
      // 模拟表格数据
      this.tableData = [
        {
          date: '2024-01-01',
          houses: 5,
          users: 8,
          appointments: 12,
          successRate: 75
        },
        {
          date: '2024-01-02',
          houses: 3,
          users: 6,
          appointments: 8,
          successRate: 80
        },
        {
          date: '2024-01-03',
          houses: 7,
          users: 10,
          appointments: 15,
          successRate: 70
        }
      ]
      this.totalCount = 100
    },

    // 日期范围改变
    onDateRangeChange(value) {
      this.dateRange = value
      this.loadStatisticsData()
    },

    // 统计维度改变
    onDimensionChange(value) {
      this.dimension = value
      this.loadStatisticsData()
    },

    // 分页改变
    onPageChange(page) {
      this.currentPage = page
      this.loadTableData()
    },

    // 导出数据
    exportData() {
      uni.showToast({
        title: '导出功能开发中',
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.statistics {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.filter-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 40rpx;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  
  .filter-label {
    font-size: 28rpx;
    color: #333;
    white-space: nowrap;
  }
  
  .filter-btn {
    background: #007AFF;
    color: #fff;
    border: none;
    border-radius: 8rpx;
    padding: 16rpx 32rpx;
    font-size: 28rpx;
  }
}

.charts-container {
  margin-bottom: 30rpx;
}

.chart-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.chart-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500rpx, 1fr));
  gap: 30rpx;
}

.chart-item {
  border: 1rpx solid #f0f0f0;
  border-radius: 8rpx;
  padding: 30rpx;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  
  .chart-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
  
  .chart-legend {
    display: flex;
    gap: 20rpx;
    
    .legend-item {
      display: flex;
      align-items: center;
      gap: 8rpx;
      font-size: 24rpx;
      color: #666;
      
      .legend-color {
        width: 20rpx;
        height: 20rpx;
        border-radius: 50%;
      }
    }
  }
}

.chart-content {
  height: 400rpx;
}

.data-table-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.pagination {
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .chart-grid {
    grid-template-columns: 1fr;
  }
}
</style>
