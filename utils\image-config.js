// 图片资源配置管理

// 图片资源映射
export const IMAGE_CONFIG = {
  // 应用图标和Logo
  logo: '/static/logo.png',
  icon: '/static/icon.png',

  // 默认占位图
  defaultAvatar: '/static/default-avatar.png',
  defaultHouse: '/static/default-house.png',

  // 空状态图片
  emptyDefault: '/static/empty-default.png',
  emptyHouse: '/static/empty-house.png',
  emptyFavorite: '/static/empty-favorite.png',
  emptySearch: '/static/empty-search.png',

  // 轮播图
  banner1: '/static/banner1.jpg',
  banner2: '/static/banner2.jpg',
  banner3: '/static/banner3.jpg',

  // 地图标记
  mapMarker: '/static/map-marker.png',

  // 测试图片
  testHouse: '/static/test-house.jpg'
};

// 占位符图片配置（当本地图片不存在时使用）
export const PLACEHOLDER_CONFIG = {
  // 应用图标和Logo
  logo: 'https://via.placeholder.com/120x120/007aff/ffffff?text=Logo',
  icon: 'https://via.placeholder.com/512x512/007aff/ffffff?text=Icon',

  // 默认占位图
  defaultAvatar: 'https://via.placeholder.com/200x200/cccccc/ffffff?text=Avatar',
  defaultHouse: 'https://via.placeholder.com/400x300/f0f0f0/666666?text=House',

  // 空状态图片
  emptyDefault: 'https://via.placeholder.com/200x200/f8f9fa/999999?text=Empty',
  emptyHouse: 'https://via.placeholder.com/200x200/f8f9fa/999999?text=No+House',
  emptyFavorite: 'https://via.placeholder.com/200x200/f8f9fa/999999?text=No+Favorite',
  emptySearch: 'https://via.placeholder.com/200x200/f8f9fa/999999?text=No+Result',

  // 轮播图
  banner1: 'https://via.placeholder.com/750x300/667eea/ffffff?text=Banner+1',
  banner2: 'https://via.placeholder.com/750x300/764ba2/ffffff?text=Banner+2',
  banner3: 'https://via.placeholder.com/750x300/f093fb/ffffff?text=Banner+3',

  // 地图标记
  mapMarker: 'https://via.placeholder.com/30x30/ff6b6b/ffffff?text=📍',

  // 测试图片
  testHouse: 'https://via.placeholder.com/400x300/10c560/ffffff?text=Test+House'
};

// 图片尺寸配置
export const IMAGE_SIZES = {
  avatar: {
    small: '60rpx',
    medium: '120rpx',
    large: '200rpx'
  },
  house: {
    thumbnail: '200rpx',
    card: '300rpx',
    detail: '500rpx'
  },
  banner: {
    height: '300rpx'
  },
  empty: {
    size: '200rpx'
  }
};

// 图片加载状态
export const IMAGE_STATUS = {
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error'
};

// 图片工具类
export class ImageUtils {
  
  /**
   * 获取图片完整路径
   * @param {string} imageName 图片名称
   * @param {boolean} usePlaceholder 是否使用占位符
   * @returns {string} 完整路径
   */
  static getImageUrl(imageName, usePlaceholder = false) {
    if (usePlaceholder && PLACEHOLDER_CONFIG[imageName]) {
      return PLACEHOLDER_CONFIG[imageName];
    }
    return IMAGE_CONFIG[imageName] || imageName;
  }

  /**
   * 获取图片URL（自动回退到占位符）
   * @param {string} imageName 图片名称
   * @returns {string} 图片URL
   */
  static getImageUrlWithFallback(imageName) {
    return IMAGE_CONFIG[imageName] || PLACEHOLDER_CONFIG[imageName] || imageName;
  }
  
  /**
   * 获取默认头像
   * @param {string} userAvatar 用户头像URL
   * @param {boolean} usePlaceholder 是否使用占位符
   * @returns {string} 头像URL
   */
  static getAvatar(userAvatar, usePlaceholder = false) {
    if (userAvatar) return userAvatar;
    return usePlaceholder ? PLACEHOLDER_CONFIG.defaultAvatar : IMAGE_CONFIG.defaultAvatar;
  }
  
  /**
   * 获取房源图片
   * @param {Array|string} houseImages 房源图片数组或单张图片
   * @param {number} index 图片索引
   * @param {boolean} usePlaceholder 是否使用占位符
   * @returns {string} 图片URL
   */
  static getHouseImage(houseImages, index = 0, usePlaceholder = false) {
    const defaultImage = usePlaceholder ? PLACEHOLDER_CONFIG.defaultHouse : IMAGE_CONFIG.defaultHouse;

    if (!houseImages) {
      return defaultImage;
    }

    if (Array.isArray(houseImages)) {
      return houseImages[index] || houseImages[0] || defaultImage;
    }

    return houseImages || defaultImage;
  }
  
  /**
   * 获取空状态图片
   * @param {string} type 空状态类型
   * @param {boolean} usePlaceholder 是否使用占位符
   * @returns {string} 图片URL
   */
  static getEmptyImage(type = 'default', usePlaceholder = false) {
    const emptyImages = usePlaceholder ? {
      default: PLACEHOLDER_CONFIG.emptyDefault,
      house: PLACEHOLDER_CONFIG.emptyHouse,
      favorite: PLACEHOLDER_CONFIG.emptyFavorite,
      search: PLACEHOLDER_CONFIG.emptySearch
    } : {
      default: IMAGE_CONFIG.emptyDefault,
      house: IMAGE_CONFIG.emptyHouse,
      favorite: IMAGE_CONFIG.emptyFavorite,
      search: IMAGE_CONFIG.emptySearch
    };

    return emptyImages[type] || emptyImages.default;
  }
  
  /**
   * 获取轮播图列表
   * @param {boolean} usePlaceholder 是否使用占位符
   * @returns {Array} 轮播图数组
   */
  static getBannerImages(usePlaceholder = false) {
    const config = usePlaceholder ? PLACEHOLDER_CONFIG : IMAGE_CONFIG;

    return [
      {
        url: config.banner1,
        title: '优质房源推荐',
        link: '/pages/house/list'
      },
      {
        url: config.banner2,
        title: '毕业生租房指南',
        link: '/pages/help/guide'
      },
      {
        url: config.banner3,
        title: '安全租房提醒',
        link: '/pages/help/safety'
      }
    ];
  }
  
  /**
   * 压缩图片
   * @param {string} imagePath 图片路径
   * @param {Object} options 压缩选项
   * @returns {Promise} 压缩后的图片
   */
  static compressImage(imagePath, options = {}) {
    const defaultOptions = {
      quality: 0.8,
      width: 800,
      height: 600
    };
    
    const compressOptions = { ...defaultOptions, ...options };
    
    return new Promise((resolve, reject) => {
      uni.compressImage({
        src: imagePath,
        quality: compressOptions.quality,
        width: compressOptions.width,
        height: compressOptions.height,
        success: (res) => {
          resolve(res.tempFilePath);
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  }
  
  /**
   * 选择图片
   * @param {Object} options 选择选项
   * @returns {Promise} 选择的图片
   */
  static chooseImage(options = {}) {
    const defaultOptions = {
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera']
    };
    
    const chooseOptions = { ...defaultOptions, ...options };
    
    return new Promise((resolve, reject) => {
      uni.chooseImage({
        ...chooseOptions,
        success: (res) => {
          resolve(res.tempFilePaths);
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  }
  
  /**
   * 预览图片
   * @param {Array} urls 图片URL数组
   * @param {number} current 当前图片索引
   */
  static previewImage(urls, current = 0) {
    if (!Array.isArray(urls) || urls.length === 0) {
      return;
    }
    
    uni.previewImage({
      urls: urls,
      current: current
    });
  }
  
  /**
   * 检查图片是否存在
   * @param {string} imagePath 图片路径
   * @returns {Promise<boolean>} 是否存在
   */
  static checkImageExists(imagePath) {
    return new Promise((resolve) => {
      uni.getImageInfo({
        src: imagePath,
        success: () => {
          resolve(true);
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  }
  
  /**
   * 获取图片信息
   * @param {string} imagePath 图片路径
   * @returns {Promise} 图片信息
   */
  static getImageInfo(imagePath) {
    return new Promise((resolve, reject) => {
      uni.getImageInfo({
        src: imagePath,
        success: (res) => {
          resolve(res);
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  }
}

// 图片懒加载混入
export const imageLazyLoadMixin = {
  data() {
    return {
      imageLoadStatus: {}
    };
  },
  methods: {
    // 图片加载成功
    onImageLoad(imageId) {
      this.$set(this.imageLoadStatus, imageId, IMAGE_STATUS.SUCCESS);
    },
    
    // 图片加载失败
    onImageError(imageId, fallbackUrl) {
      this.$set(this.imageLoadStatus, imageId, IMAGE_STATUS.ERROR);
      
      // 使用备用图片
      if (fallbackUrl) {
        this.$set(this.imageLoadStatus, imageId + '_fallback', fallbackUrl);
      }
    },
    
    // 获取图片加载状态
    getImageStatus(imageId) {
      return this.imageLoadStatus[imageId] || IMAGE_STATUS.LOADING;
    },
    
    // 是否显示占位图
    shouldShowPlaceholder(imageId) {
      return this.getImageStatus(imageId) === IMAGE_STATUS.LOADING;
    }
  }
};

// 导出默认配置
export default {
  IMAGE_CONFIG,
  PLACEHOLDER_CONFIG,
  IMAGE_SIZES,
  IMAGE_STATUS,
  ImageUtils,
  imageLazyLoadMixin
};
