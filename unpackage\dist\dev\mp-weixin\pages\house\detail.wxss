
.detail-container.data-v-4265f319 {
  background: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 120rpx;
}
.house-swiper.data-v-4265f319 {
  height: 500rpx;
}
.house-swiper image.data-v-4265f319 {
  width: 100%;
  height: 100%;
}
.info-section.data-v-4265f319 {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.price-info.data-v-4265f319 {
  display: flex;
  align-items: baseline;
  margin-bottom: 20rpx;
}
.price.data-v-4265f319 {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff6b6b;
}
.price-unit.data-v-4265f319 {
  font-size: 28rpx;
  color: #999;
  margin-left: 8rpx;
}
.deposit.data-v-4265f319 {
  font-size: 24rpx;
  color: #666;
  margin-left: 30rpx;
  background: #f0f0f0;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
}
.title.data-v-4265f319 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.4;
}
.tags.data-v-4265f319 {
  display: flex;
  gap: 10rpx;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
}
.tag.data-v-4265f319 {
  background: #f0f0f0;
  color: #666;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}
.tag.type.data-v-4265f319 {
  background: #e3f2fd;
  color: #007aff;
}
.location.data-v-4265f319 {
  display: flex;
  align-items: center;
}
.location-text.data-v-4265f319 {
  font-size: 28rpx;
  color: #666;
  margin-left: 8rpx;
}
.detail-section.data-v-4265f319, .facilities-section.data-v-4265f319, .landlord-section.data-v-4265f319, .map-section.data-v-4265f319 {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}
.section-title.data-v-4265f319 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.detail-grid.data-v-4265f319 {
  display: flex;
  flex-wrap: wrap;
  gap: 30rpx;
  margin-bottom: 30rpx;
}
.detail-item.data-v-4265f319 {
  flex: 1;
  min-width: 200rpx;
  text-align: center;
}
.detail-label.data-v-4265f319 {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.detail-value.data-v-4265f319 {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.description.data-v-4265f319 {
  margin-top: 30rpx;
}
.desc-title.data-v-4265f319 {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 15rpx;
}
.desc-content.data-v-4265f319 {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
.facilities-grid.data-v-4265f319 {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}
.facility-item.data-v-4265f319 {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  padding: 15rpx 20rpx;
  border-radius: 25rpx;
  min-width: 120rpx;
}
.facility-text.data-v-4265f319 {
  font-size: 26rpx;
  color: #333;
  margin-left: 10rpx;
}
.publisher-info.data-v-4265f319 {
  display: flex;
  align-items: center;
}
.publisher-avatar.data-v-4265f319 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
}
.publisher-details.data-v-4265f319 {
  flex: 1;
}
.publisher-name.data-v-4265f319 {
  display: block;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.landlord-verified.data-v-4265f319 {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #10c560;
}
.contact-btn.data-v-4265f319 {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 15rpx 30rpx;
  font-size: 26rpx;
}
.house-map.data-v-4265f319 {
  width: 100%;
  height: 300rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}
.map-address.data-v-4265f319 {
  display: block;
  font-size: 28rpx;
  color: #666;
  text-align: center;
}
.bottom-actions.data-v-4265f319 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 30rpx;
}
.action-item.data-v-4265f319 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}
.action-text.data-v-4265f319 {
  font-size: 24rpx;
  color: #666;
}
.appointment-btn.data-v-4265f319 {
  flex: 1;
  height: 80rpx;
  background: linear-gradient(45deg, #007aff, #0056d3);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: bold;
}
.loading-container.data-v-4265f319, .empty-container.data-v-4265f319 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 40rpx;
}
.loading-text.data-v-4265f319, .empty-text.data-v-4265f319 {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}
.back-btn.data-v-4265f319 {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

