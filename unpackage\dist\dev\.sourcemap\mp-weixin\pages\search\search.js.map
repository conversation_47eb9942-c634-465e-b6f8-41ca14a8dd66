{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端/pages/search/search.vue?8d7d", "webpack:///D:/web/project/前端/pages/search/search.vue?fbc1", "webpack:///D:/web/project/前端/pages/search/search.vue?4601", "webpack:///D:/web/project/前端/pages/search/search.vue?cea8", "uni-app:///pages/search/search.vue", "webpack:///D:/web/project/前端/pages/search/search.vue?149c", "webpack:///D:/web/project/前端/pages/search/search.vue?90bc"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "keyword", "showSuggestions", "suggestions", "searchHistory", "hotKeywords", "statusBarHeight", "showResults", "searchResults", "totalCount", "loading", "refreshing", "noMore", "hasSearched", "page", "pageSize", "selectedLocation", "selectedPrice", "selectedType", "selected<PERSON>ort", "value", "label", "priceOptions", "typeOptions", "sortOptions", "methods", "onInput", "getSuggestions", "mockSuggestions", "handleSearch", "loadSearchResults", "loadMore", "params", "action", "request", "result", "console", "getTypeText", "selectSuggestion", "selectHistory", "selectHot", "clearKeyword", "saveSearchHistory", "history", "uni", "removeHistory", "clearHistory", "title", "content", "success", "onRefresh", "showLocationFilter", "icon", "showPriceFilter", "showTypeFilter", "showMoreFilter", "showSortFilter", "toHouseDetail", "url", "goBack", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACqC;;;AAG1F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAioB,CAAgB,+nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACsKrpB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;QAAAC;QAAAC;MAAA;MAEA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACAC,mBACA,uBACA,uBACA,uBACA,sBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;gBACA;gBACA;gBAAA;gBAAA,OAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA;gBAGAC;kBACAC;kBACAjC;oBACAC;oBACAa;oBACAC;kBACA;gBACA,GAEA;gBACA;kBACAiB;gBACA;gBAEA;kBACAA;gBACA;gBACA;kBACAA;gBACA;gBAEA;kBACAA;gBACA;gBAAA;gBAAA,OAEAE;cAAA;gBAAAC;gBAEA;kBAAA,eACAA;kBAEA;oBACA;kBACA;oBACA;kBACA;kBAEA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QAAA;MAAA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACAC;QAAA;MAAA;;MAEA;MACAA;;MAEA;MACA;QACAA;MACA;MAEA;MACAC;IACA;IAEA;IACAC;MACA;MACAD;IACA;IAEA;IACAE;MAAA;MACAF;QACAG;QACAC;QACAC;UACA;YACA;YACAL;UACA;QACA;MACA;IACA;IAEA;IACAb;MACA;QACA;MACA;IACA;IAEA;IACAmB;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACAP;QACAG;QACAK;MACA;IACA;IAEA;IACAC;MACAT;QACAG;QACAK;MACA;IACA;IAEA;IACAE;MACAV;QACAG;QACAK;MACA;IACA;IAEA;IACAG;MACAX;QACAG;QACAK;MACA;IACA;IAEA;IACAI;MACAZ;QACAG;QACAK;MACA;IACA;IAEA;IACAK;MACAb;QACAc;MACA;IACA;IAEA;IACAC;MACAf;IACA;EACA;EAEAgB;IACA;IACA;IACA;;IAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChcA;AAAA;AAAA;AAAA;AAAw7B,CAAgB,k5BAAG,EAAC,C;;;;;;;;;;;ACA58B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/search/search.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/search/search.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./search.vue?vue&type=template&id=4cedc0c6&scoped=true&\"\nvar renderjs\nimport script from \"./search.vue?vue&type=script&lang=js&\"\nexport * from \"./search.vue?vue&type=script&lang=js&\"\nimport style0 from \"./search.vue?vue&type=style&index=0&id=4cedc0c6&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4cedc0c6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/search/search.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=template&id=4cedc0c6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.showSuggestions && _vm.suggestions.length > 0\n  var g1 = !_vm.keyword && _vm.searchHistory.length > 0\n  var g2 = _vm.showResults ? _vm.searchResults.length : null\n  var l0 = _vm.showResults\n    ? _vm.__map(_vm.searchResults, function (house, __i0__) {\n        var $orig = _vm.__get_orig(house)\n        var m0 = _vm.getTypeText(house.type)\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  var g3 = _vm.showResults ? _vm.searchResults.length : null\n  var g4 = _vm.showResults\n    ? !_vm.loading && _vm.searchResults.length === 0 && _vm.hasSearched\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        l0: l0,\n        g3: g3,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"search-container\">\n    <!-- 搜索栏 -->\n    <view class=\"search-bar\" :style=\"{ paddingTop: (statusBarHeight * 2 + 60) + 'rpx' }\">\n      <view class=\"search-input-wrapper\">\n        <uni-icons type=\"search\" size=\"18\" color=\"#999\"></uni-icons>\n        <input \n          class=\"search-input\" \n          type=\"text\" \n          placeholder=\"搜索房源、地址、关键词\" \n          v-model=\"keyword\"\n          @input=\"onInput\"\n          @confirm=\"handleSearch\"\n          focus\n        />\n        <view class=\"clear-btn\" v-if=\"keyword\" @click=\"clearKeyword\">\n          <uni-icons type=\"clear\" size=\"16\" color=\"#999\"></uni-icons>\n        </view>\n      </view>\n      <text class=\"cancel-btn\" @click=\"goBack\">取消</text>\n    </view>\n    \n    <!-- 搜索建议 -->\n    <view class=\"suggestions\" v-if=\"showSuggestions && suggestions.length > 0\">\n      <view \n        class=\"suggestion-item\" \n        v-for=\"(item, index) in suggestions\" \n        :key=\"index\"\n        @click=\"selectSuggestion(item)\"\n      >\n        <uni-icons type=\"search\" size=\"16\" color=\"#999\"></uni-icons>\n        <text class=\"suggestion-text\">{{ item }}</text>\n      </view>\n    </view>\n    \n    <!-- 搜索历史 -->\n    <view class=\"search-history\" v-if=\"!keyword && searchHistory.length > 0\">\n      <view class=\"history-header\">\n        <text class=\"history-title\">搜索历史</text>\n        <text class=\"clear-history\" @click=\"clearHistory\">清空</text>\n      </view>\n      <view class=\"history-tags\">\n        <view \n          class=\"history-tag\" \n          v-for=\"(item, index) in searchHistory\" \n          :key=\"index\"\n          @click=\"selectHistory(item)\"\n        >\n          <text class=\"tag-text\">{{ item }}</text>\n          <uni-icons type=\"close\" size=\"14\" color=\"#999\" @click.stop=\"removeHistory(index)\"></uni-icons>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 热门搜索 -->\n    <view class=\"hot-search\" v-if=\"!keyword\">\n      <view class=\"hot-header\">\n        <text class=\"hot-title\">热门搜索</text>\n      </view>\n      <view class=\"hot-tags\">\n        <view \n          class=\"hot-tag\" \n          v-for=\"(item, index) in hotKeywords\" \n          :key=\"index\"\n          @click=\"selectHot(item)\"\n        >\n          <text class=\"tag-text\">{{ item }}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 搜索结果 -->\n    <view class=\"search-results\" v-if=\"showResults\">\n      <!-- 筛选栏 -->\n      <view class=\"filter-bar\">\n        <view class=\"filter-item\" @click=\"showLocationFilter\">\n          <text class=\"filter-text\">{{ selectedLocation || '位置' }}</text>\n          <uni-icons type=\"arrowdown\" size=\"12\" color=\"#666\"></uni-icons>\n        </view>\n        <view class=\"filter-item\" @click=\"showPriceFilter\">\n          <text class=\"filter-text\">{{ selectedPrice.label || '价格' }}</text>\n          <uni-icons type=\"arrowdown\" size=\"12\" color=\"#666\"></uni-icons>\n        </view>\n        <view class=\"filter-item\" @click=\"showTypeFilter\">\n          <text class=\"filter-text\">{{ selectedType.label || '类型' }}</text>\n          <uni-icons type=\"arrowdown\" size=\"12\" color=\"#666\"></uni-icons>\n        </view>\n        <view class=\"filter-item\" @click=\"showMoreFilter\">\n          <text class=\"filter-text\">筛选</text>\n          <uni-icons type=\"tune\" size=\"14\" color=\"#666\"></uni-icons>\n        </view>\n      </view>\n      \n      <!-- 结果列表 -->\n      <scroll-view \n        class=\"result-list\" \n        scroll-y \n        @scrolltolower=\"loadMore\"\n        refresher-enabled\n        @refresherrefresh=\"onRefresh\"\n        :refresher-triggered=\"refreshing\"\n      >\n        <view class=\"result-header\" v-if=\"searchResults.length > 0\">\n          <text class=\"result-count\">找到 {{ totalCount }} 套房源</text>\n          <view class=\"sort-btn\" @click=\"showSortFilter\">\n            <text class=\"sort-text\">{{ selectedSort.label }}</text>\n            <uni-icons type=\"arrowdown\" size=\"12\" color=\"#666\"></uni-icons>\n          </view>\n        </view>\n        \n        <view class=\"house-item\" v-for=\"house in searchResults\" :key=\"house._id\" @click=\"toHouseDetail(house._id)\">\n          <view class=\"house-image\">\n            <image \n              :src=\"house.images && house.images[0] || '/static/default-house.png'\" \n              mode=\"aspectFill\"\n            ></image>\n            <view class=\"house-type\">{{ getTypeText(house.type) }}</view>\n          </view>\n          <view class=\"house-info\">\n            <text class=\"house-title\">{{ house.title }}</text>\n            <view class=\"house-tags\">\n              <text class=\"tag\" v-if=\"house.room_count\">{{ house.room_count }}室</text>\n              <text class=\"tag\" v-if=\"house.hall_count\">{{ house.hall_count }}厅</text>\n              <text class=\"tag\" v-if=\"house.area\">{{ house.area }}㎡</text>\n            </view>\n            <view class=\"house-location\">\n              <uni-icons type=\"location\" size=\"12\" color=\"#999\"></uni-icons>\n              <text class=\"location-text\">{{ house.location.district }} {{ house.location.address }}</text>\n            </view>\n            <view class=\"house-bottom\">\n              <view class=\"price-info\">\n                <text class=\"price\">¥{{ house.price }}</text>\n                <text class=\"price-unit\">/月</text>\n              </view>\n              <view class=\"house-stats\">\n                <text class=\"stat-item\">\n                  <uni-icons type=\"eye\" size=\"12\" color=\"#999\"></uni-icons>\n                  {{ house.view_count || 0 }}\n                </text>\n                <text class=\"stat-item\">\n                  <uni-icons type=\"heart\" size=\"12\" color=\"#999\"></uni-icons>\n                  {{ house.favorite_count || 0 }}\n                </text>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 加载状态 -->\n        <view class=\"load-status\" v-if=\"searchResults.length > 0\">\n          <text v-if=\"loading\">加载中...</text>\n          <text v-else-if=\"noMore\">没有更多了</text>\n        </view>\n        \n        <!-- 空状态 -->\n        <view class=\"empty-state\" v-if=\"!loading && searchResults.length === 0 && hasSearched\">\n          <image src=\"/static/empty-search.png\" mode=\"aspectFit\"></image>\n          <text class=\"empty-text\">没有找到相关房源</text>\n          <text class=\"empty-tip\">试试其他关键词或调整筛选条件</text>\n        </view>\n      </scroll-view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport request from '@/utils/request.js'\nimport { debounce } from '@/utils/common.js'\nimport { HOUSE_TYPES, PRICE_RANGES, SORT_OPTIONS } from '@/common/config.js'\n\nexport default {\n  data() {\n    return {\n      keyword: '',\n      showSuggestions: false,\n      suggestions: [],\n      searchHistory: [],\n      hotKeywords: ['整租', '合租', '单间', '地铁附近', '精装修', '拎包入住'],\n      statusBarHeight: 0, // 状态栏高度\n      \n      // 搜索结果\n      showResults: false,\n      searchResults: [],\n      totalCount: 0,\n      loading: false,\n      refreshing: false,\n      noMore: false,\n      hasSearched: false,\n      page: 1,\n      pageSize: 10,\n      \n      // 筛选条件\n      selectedLocation: '',\n      selectedPrice: {},\n      selectedType: {},\n      selectedSort: { value: 'publish_date_desc', label: '最新发布' },\n      \n      // 筛选选项\n      priceOptions: PRICE_RANGES,\n      typeOptions: HOUSE_TYPES,\n      sortOptions: SORT_OPTIONS\n    }\n  },\n  methods: {\n    // 输入处理（防抖）\n    onInput: debounce(function() {\n      if (this.keyword.trim()) {\n        this.getSuggestions()\n        this.showSuggestions = true\n      } else {\n        this.showSuggestions = false\n        this.suggestions = []\n      }\n    }, 300),\n    \n    // 获取搜索建议\n    async getSuggestions() {\n      // 这里可以调用API获取搜索建议\n      // 暂时使用模拟数据\n      const mockSuggestions = [\n        this.keyword + ' 整租',\n        this.keyword + ' 合租',\n        this.keyword + ' 附近',\n        this.keyword + ' 地铁'\n      ]\n      this.suggestions = mockSuggestions.slice(0, 5)\n    },\n    \n    // 执行搜索\n    async handleSearch() {\n      if (!this.keyword.trim()) return\n      \n      this.showSuggestions = false\n      this.showResults = true\n      this.hasSearched = true\n      \n      // 保存搜索历史\n      this.saveSearchHistory(this.keyword.trim())\n      \n      // 重置搜索结果\n      this.searchResults = []\n      this.page = 1\n      this.noMore = false\n      \n      await this.loadSearchResults()\n    },\n    \n    // 加载搜索结果\n    async loadSearchResults(loadMore = false) {\n      if (this.loading) return\n      \n      this.loading = true\n      \n      try {\n        const params = {\n          action: 'searchHouses',\n          data: {\n            keyword: this.keyword.trim(),\n            page: this.page,\n            pageSize: this.pageSize\n          }\n        }\n        \n        // 添加筛选条件\n        if (this.selectedType.value) {\n          params.data.type = this.selectedType.value\n        }\n        \n        if (this.selectedPrice.min !== undefined) {\n          params.data.minPrice = this.selectedPrice.min\n        }\n        if (this.selectedPrice.max !== undefined) {\n          params.data.maxPrice = this.selectedPrice.max\n        }\n        \n        if (this.selectedSort.value) {\n          params.data.sort = this.selectedSort.value\n        }\n        \n        const result = await request.callFunction('house-management', params)\n        \n        if (result.code === 0) {\n          const { list, total } = result.data\n          \n          if (loadMore) {\n            this.searchResults.push(...list)\n          } else {\n            this.searchResults = list\n          }\n          \n          this.totalCount = total\n          this.page++\n          this.noMore = this.searchResults.length >= total\n        }\n      } catch (error) {\n        console.error('搜索失败:', error)\n      } finally {\n        this.loading = false\n        this.refreshing = false\n      }\n    },\n    \n    // 获取房源类型文本\n    getTypeText(type) {\n      const typeItem = HOUSE_TYPES.find(item => item.value === type)\n      return typeItem ? typeItem.label : type\n    },\n    \n    // 选择搜索建议\n    selectSuggestion(suggestion) {\n      this.keyword = suggestion\n      this.handleSearch()\n    },\n    \n    // 选择搜索历史\n    selectHistory(history) {\n      this.keyword = history\n      this.handleSearch()\n    },\n    \n    // 选择热门搜索\n    selectHot(hot) {\n      this.keyword = hot\n      this.handleSearch()\n    },\n    \n    // 清空关键词\n    clearKeyword() {\n      this.keyword = ''\n      this.showSuggestions = false\n      this.showResults = false\n    },\n    \n    // 保存搜索历史\n    saveSearchHistory(keyword) {\n      let history = uni.getStorageSync('searchHistory') || []\n      \n      // 移除重复项\n      history = history.filter(item => item !== keyword)\n      \n      // 添加到开头\n      history.unshift(keyword)\n      \n      // 限制数量\n      if (history.length > 10) {\n        history = history.slice(0, 10)\n      }\n      \n      this.searchHistory = history\n      uni.setStorageSync('searchHistory', history)\n    },\n    \n    // 移除搜索历史\n    removeHistory(index) {\n      this.searchHistory.splice(index, 1)\n      uni.setStorageSync('searchHistory', this.searchHistory)\n    },\n    \n    // 清空搜索历史\n    clearHistory() {\n      uni.showModal({\n        title: '提示',\n        content: '确定要清空搜索历史吗？',\n        success: (res) => {\n          if (res.confirm) {\n            this.searchHistory = []\n            uni.removeStorageSync('searchHistory')\n          }\n        }\n      })\n    },\n    \n    // 加载更多\n    loadMore() {\n      if (!this.noMore && !this.loading) {\n        this.loadSearchResults(true)\n      }\n    },\n    \n    // 下拉刷新\n    onRefresh() {\n      this.refreshing = true\n      this.page = 1\n      this.noMore = false\n      this.loadSearchResults()\n    },\n    \n    // 显示位置筛选\n    showLocationFilter() {\n      uni.showToast({\n        title: '功能开发中',\n        icon: 'none'\n      })\n    },\n    \n    // 显示价格筛选\n    showPriceFilter() {\n      uni.showToast({\n        title: '功能开发中',\n        icon: 'none'\n      })\n    },\n    \n    // 显示类型筛选\n    showTypeFilter() {\n      uni.showToast({\n        title: '功能开发中',\n        icon: 'none'\n      })\n    },\n    \n    // 显示更多筛选\n    showMoreFilter() {\n      uni.showToast({\n        title: '功能开发中',\n        icon: 'none'\n      })\n    },\n    \n    // 显示排序筛选\n    showSortFilter() {\n      uni.showToast({\n        title: '功能开发中',\n        icon: 'none'\n      })\n    },\n    \n    // 跳转到房源详情\n    toHouseDetail(houseId) {\n      uni.navigateTo({\n        url: `/pages/house/detail?id=${houseId}`\n      })\n    },\n    \n    // 返回\n    goBack() {\n      uni.navigateBack()\n    }\n  },\n  \n  onLoad() {\n    // 获取系统信息，包括状态栏高度\n    const systemInfo = uni.getSystemInfoSync()\n    this.statusBarHeight = systemInfo.statusBarHeight || 44 // 默认44px\n\n    // 加载搜索历史\n    this.searchHistory = uni.getStorageSync('searchHistory') || []\n  }\n}\n</script>\n\n<style scoped>\n.search-container {\n  height: 100vh;\n  background: #f5f7fa;\n  display: flex;\n  flex-direction: column;\n}\n\n.search-bar {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20rpx 30rpx;\n  padding-top: 150rpx; /* 默认间距，会被动态样式覆盖 */\n  padding-bottom: 30rpx;\n  display: flex;\n  align-items: center;\n  gap: 20rpx;\n  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);\n}\n\n.search-input-wrapper {\n  flex: 1;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 30rpx;\n  padding: 0 30rpx;\n  display: flex;\n  align-items: center;\n  height: 80rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(10rpx);\n  transition: all 0.3s ease;\n}\n\n.search-input-wrapper:focus-within {\n  background: #fff;\n  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.15);\n  transform: translateY(-2rpx);\n}\n\n.search-input {\n  flex: 1;\n  margin-left: 15rpx;\n  font-size: 28rpx;\n  color: #333;\n  background: transparent;\n  border: none;\n  outline: none;\n}\n\n.search-input::placeholder {\n  color: #999;\n}\n\n.clear-btn {\n  padding: 10rpx;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n}\n\n.clear-btn:active {\n  background: rgba(0, 0, 0, 0.1);\n  transform: scale(0.9);\n}\n\n.cancel-btn {\n  font-size: 28rpx;\n  color: #fff;\n  font-weight: 500;\n  padding: 16rpx 20rpx;\n  border-radius: 20rpx;\n  transition: all 0.3s ease;\n}\n\n.cancel-btn:active {\n  background: rgba(255, 255, 255, 0.2);\n  transform: scale(0.95);\n}\n\n.suggestions {\n  background: #fff;\n  margin: 0 30rpx;\n  border-radius: 16rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n}\n\n.suggestion-item {\n  display: flex;\n  align-items: center;\n  padding: 25rpx 30rpx;\n  border-bottom: 1rpx solid #f8f9fa;\n  transition: all 0.3s ease;\n}\n\n.suggestion-item:last-child {\n  border-bottom: none;\n}\n\n.suggestion-item:active {\n  background: #f8f9fa;\n}\n\n.suggestion-text {\n  font-size: 28rpx;\n  color: #333;\n  margin-left: 20rpx;\n}\n\n.search-history, .hot-search {\n  background: #fff;\n  margin: 20rpx 30rpx;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);\n}\n\n.history-header, .hot-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 30rpx;\n}\n\n.history-title, .hot-title {\n  font-size: 30rpx;\n  color: #303133;\n  font-weight: 600;\n}\n\n.clear-history {\n  font-size: 26rpx;\n  color: #909399;\n  padding: 8rpx 16rpx;\n  border-radius: 12rpx;\n  transition: all 0.3s ease;\n}\n\n.clear-history:active {\n  background: #f5f7fa;\n  color: #606266;\n}\n\n.history-tags, .hot-tags {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16rpx;\n}\n\n.history-tag, .hot-tag {\n  background: #f8f9fa;\n  border: 1rpx solid transparent;\n  border-radius: 20rpx;\n  padding: 16rpx 24rpx;\n  display: flex;\n  align-items: center;\n  gap: 10rpx;\n  transition: all 0.3s ease;\n}\n\n.history-tag:active, .hot-tag:active {\n  background: #e7f4ff;\n  border-color: #007aff;\n  transform: scale(0.95);\n}\n\n.tag-text {\n  font-size: 26rpx;\n  color: #666;\n}\n\n.search-results {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.filter-bar {\n  background: #fff;\n  padding: 20rpx;\n  display: flex;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.filter-item {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20rpx 10rpx;\n  gap: 8rpx;\n}\n\n.filter-text {\n  font-size: 28rpx;\n  color: #333;\n}\n\n.result-list {\n  flex: 1;\n  padding: 20rpx;\n}\n\n.result-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 20rpx;\n}\n\n.result-count {\n  font-size: 26rpx;\n  color: #666;\n}\n\n.sort-btn {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n}\n\n.sort-text {\n  font-size: 26rpx;\n  color: #666;\n}\n\n.house-item {\n  background: #fff;\n  border-radius: 20rpx;\n  margin-bottom: 20rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n  display: flex;\n}\n\n.house-image {\n  position: relative;\n  width: 240rpx;\n  height: 180rpx;\n  flex-shrink: 0;\n}\n\n.house-image image {\n  width: 100%;\n  height: 100%;\n}\n\n.house-type {\n  position: absolute;\n  top: 10rpx;\n  left: 10rpx;\n  background: rgba(0, 122, 255, 0.9);\n  color: #fff;\n  padding: 4rpx 12rpx;\n  border-radius: 12rpx;\n  font-size: 20rpx;\n}\n\n.house-info {\n  flex: 1;\n  padding: 20rpx;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.house-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 10rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.house-tags {\n  display: flex;\n  gap: 8rpx;\n  margin-bottom: 10rpx;\n}\n\n.tag {\n  background: #f0f0f0;\n  color: #666;\n  padding: 4rpx 8rpx;\n  border-radius: 6rpx;\n  font-size: 20rpx;\n}\n\n.house-location {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15rpx;\n}\n\n.location-text {\n  font-size: 24rpx;\n  color: #999;\n  margin-left: 6rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.house-bottom {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.price-info {\n  display: flex;\n  align-items: baseline;\n}\n\n.price {\n  font-size: 30rpx;\n  font-weight: bold;\n  color: #ff6b6b;\n}\n\n.price-unit {\n  font-size: 20rpx;\n  color: #999;\n  margin-left: 4rpx;\n}\n\n.house-stats {\n  display: flex;\n  gap: 15rpx;\n}\n\n.stat-item {\n  font-size: 20rpx;\n  color: #999;\n  display: flex;\n  align-items: center;\n  gap: 4rpx;\n}\n\n.load-status {\n  text-align: center;\n  padding: 40rpx;\n  color: #999;\n  font-size: 28rpx;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 100rpx 40rpx;\n}\n\n.empty-state image {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 40rpx;\n}\n\n.empty-text {\n  display: block;\n  font-size: 28rpx;\n  color: #999;\n  margin-bottom: 10rpx;\n}\n\n.empty-tip {\n  display: block;\n  font-size: 24rpx;\n  color: #ccc;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=style&index=0&id=4cedc0c6&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=style&index=0&id=4cedc0c6&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751999922\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}