<template>
  <view class="house-audit">
    <!-- 筛选条件 -->
    <view class="filter-section">
      <view class="filter-row">
        <view class="search-item">
          <uni-easyinput 
            v-model="searchKeyword" 
            placeholder="搜索房源标题、地址"
            @confirm="onSearch"
            clearable
          >
            <template #right>
              <uni-icons type="search" size="18" color="#999" @click="onSearch" />
            </template>
          </uni-easyinput>
        </view>
        
        <view class="filter-item">
          <uni-data-picker 
            v-model="filters.type" 
            :localdata="typeOptions"
            placeholder="房源类型"
            @change="onFilterChange"
          />
        </view>
        
        <view class="filter-item">
          <uni-datetime-picker 
            v-model="filters.dateRange" 
            type="daterange"
            placeholder="发布时间"
            @change="onFilterChange"
          />
        </view>
        
        <button class="reset-btn" @click="resetFilters">重置</button>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-bar">
      <view class="stat-item">
        <text class="stat-label">待审核</text>
        <text class="stat-value pending">{{ statistics.pending }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">今日提交</text>
        <text class="stat-value">{{ statistics.todaySubmitted }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">今日审核</text>
        <text class="stat-value">{{ statistics.todayAudited }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">审核通过率</text>
        <text class="stat-value">{{ statistics.passRate }}%</text>
      </view>
    </view>

    <!-- 待审核房源列表 -->
    <view class="audit-list">
      <view v-if="loading" class="loading">
        <uni-load-more status="loading" />
      </view>
      
      <view v-else-if="houseList.length === 0" class="empty-state">
        <uni-icons type="home" size="60" color="#ccc" />
        <text class="empty-text">暂无待审核房源</text>
      </view>
      
      <view v-else>
        <view 
          v-for="(house, index) in houseList" 
          :key="house._id"
          class="audit-item"
        >
          <!-- 房源基本信息 -->
          <view class="house-header">
            <view class="house-basic">
              <image 
                class="house-cover" 
                :src="house.images && house.images[0] || '/static/placeholder.png'"
                mode="aspectFill"
                @click="previewImages(house.images)"
              />
              <view class="house-info">
                <text class="house-title">{{ house.title }}</text>
                <text class="house-type">{{ house.type }}</text>
                <text class="house-price">¥{{ house.price }}/月</text>
                <text class="house-address">{{ house.location?.address }}</text>
              </view>
            </view>
            
            <view class="house-meta">
              <text class="submit-time">提交时间：{{ formatDateTime(house.publish_date) }}</text>
              <text class="publisher">发布者：{{ house.publisher_info?.nickname || '未知' }}</text>
            </view>
          </view>

          <!-- 房源详细信息 -->
          <view class="house-details">
            <view class="detail-row">
              <view class="detail-item">
                <text class="detail-label">面积</text>
                <text class="detail-value">{{ house.area }}㎡</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">房间</text>
                <text class="detail-value">{{ house.room_count }}室{{ house.hall_count }}厅{{ house.bathroom_count }}卫</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">楼层</text>
                <text class="detail-value">{{ house.floor }}/{{ house.total_floors }}层</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">朝向</text>
                <text class="detail-value">{{ house.orientation }}</text>
              </view>
            </view>
            
            <view class="description">
              <text class="description-label">房源描述：</text>
              <text class="description-text">{{ house.description }}</text>
            </view>
            
            <view class="facilities" v-if="house.facilities && house.facilities.length > 0">
              <text class="facilities-label">配套设施：</text>
              <view class="facilities-list">
                <uni-tag 
                  v-for="(facility, fIndex) in house.facilities" 
                  :key="fIndex"
                  :text="facility" 
                  type="default"
                  size="small"
                />
              </view>
            </view>
          </view>

          <!-- 联系信息 -->
          <view class="contact-info">
            <text class="contact-label">联系信息：</text>
            <text class="contact-item">联系人：{{ house.contact?.name }}</text>
            <text class="contact-item">电话：{{ house.contact?.phone }}</text>
            <text class="contact-item" v-if="house.contact?.wechat">微信：{{ house.contact?.wechat }}</text>
          </view>

          <!-- 审核操作 -->
          <view class="audit-actions">
            <button class="action-btn view-btn" @click="viewDetail(house)">查看详情</button>
            <button class="action-btn approve-btn" @click="approveHouse(house)">审核通过</button>
            <button class="action-btn reject-btn" @click="rejectHouse(house)">审核拒绝</button>
          </view>
        </view>
      </view>
      
      <!-- 分页 -->
      <view class="pagination" v-if="totalCount > pageSize">
        <uni-pagination 
          :current="currentPage"
          :total="totalCount"
          :pageSize="pageSize"
          @change="onPageChange"
        />
      </view>
    </view>

    <!-- 审核拒绝弹窗 -->
    <uni-popup ref="rejectPopup" type="dialog">
      <uni-popup-dialog 
        mode="input"
        title="审核拒绝"
        placeholder="请输入拒绝原因"
        @confirm="confirmReject"
        @close="closeRejectPopup"
      />
    </uni-popup>
  </view>
</template>

<script>
export default {
  name: 'HouseAudit',
  data() {
    return {
      loading: false,
      searchKeyword: '',
      
      // 筛选条件
      filters: {
        type: '',
        dateRange: []
      },
      
      // 筛选选项
      typeOptions: [
        { value: '', text: '全部类型' },
        { value: '整租', text: '整租' },
        { value: '合租', text: '合租' },
        { value: '单间', text: '单间' }
      ],
      
      // 统计数据
      statistics: {
        pending: 0,
        todaySubmitted: 0,
        todayAudited: 0,
        passRate: 0
      },
      
      // 列表数据
      houseList: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      
      // 当前操作的房源
      currentHouse: null
    }
  },

  onLoad() {
    this.loadAuditList()
    this.loadStatistics()
  },

  onPullDownRefresh() {
    this.currentPage = 1
    this.loadAuditList().then(() => {
      uni.stopPullDownRefresh()
    })
  },

  methods: {
    // 加载待审核列表
    async loadAuditList() {
      try {
        this.loading = true
        
        const result = await uniCloud.callFunction({
          name: 'house-management',
          data: {
            action: 'getPendingHouseList',
            page: this.currentPage,
            pageSize: this.pageSize,
            keyword: this.searchKeyword,
            filters: this.filters
          }
        })

        if (result.result.code === 0) {
          this.houseList = result.result.data.list
          this.totalCount = result.result.data.total
        } else {
          uni.showToast({
            title: result.result.message,
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('加载待审核列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 加载统计数据
    async loadStatistics() {
      try {
        const result = await uniCloud.callFunction({
          name: 'system-management',
          data: {
            action: 'getAuditStatistics'
          }
        })

        if (result.result.code === 0) {
          this.statistics = result.result.data
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    // 搜索
    onSearch() {
      this.currentPage = 1
      this.loadAuditList()
    },

    // 筛选改变
    onFilterChange() {
      this.currentPage = 1
      this.loadAuditList()
    },

    // 重置筛选
    resetFilters() {
      this.searchKeyword = ''
      this.filters = {
        type: '',
        dateRange: []
      }
      this.currentPage = 1
      this.loadAuditList()
    },

    // 查看详情
    viewDetail(house) {
      uni.navigateTo({
        url: `/pages/rental/house/detail?id=${house._id}`
      })
    },

    // 审核通过
    async approveHouse(house) {
      uni.showModal({
        title: '确认审核',
        content: `确定要审核通过房源"${house.title}"吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await uniCloud.callFunction({
                name: 'house-management',
                data: {
                  action: 'approveHouse',
                  house_id: house._id
                }
              })

              if (result.result.code === 0) {
                uni.showToast({
                  title: '审核通过',
                  icon: 'success'
                })
                this.loadAuditList()
                this.loadStatistics()
              } else {
                uni.showToast({
                  title: result.result.message,
                  icon: 'none'
                })
              }
            } catch (error) {
              console.error('审核失败:', error)
              uni.showToast({
                title: '审核失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    // 审核拒绝
    rejectHouse(house) {
      this.currentHouse = house
      this.$refs.rejectPopup.open()
    },

    // 确认拒绝
    async confirmReject(value) {
      if (!value || value.trim() === '') {
        uni.showToast({
          title: '请输入拒绝原因',
          icon: 'none'
        })
        return
      }

      try {
        const result = await uniCloud.callFunction({
          name: 'house-management',
          data: {
            action: 'rejectHouse',
            house_id: this.currentHouse._id,
            reason: value.trim()
          }
        })

        if (result.result.code === 0) {
          uni.showToast({
            title: '审核拒绝',
            icon: 'success'
          })
          this.loadAuditList()
          this.loadStatistics()
          this.$refs.rejectPopup.close()
        } else {
          uni.showToast({
            title: result.result.message,
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('审核拒绝失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        })
      }
    },

    // 关闭拒绝弹窗
    closeRejectPopup() {
      this.currentHouse = null
    },

    // 预览图片
    previewImages(images) {
      if (images && images.length > 0) {
        uni.previewImage({
          urls: images,
          current: 0
        })
      }
    },

    // 分页改变
    onPageChange(page) {
      this.currentPage = page
      this.loadAuditList()
    },

    // 格式化日期时间
    formatDateTime(date) {
      if (!date) return '-'
      const d = new Date(date)
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}`
    }
  }
}
</script>

<style lang="scss" scoped>
.house-audit {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.filter-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex-wrap: wrap;
}

.search-item {
  flex: 1;
  min-width: 300rpx;
}

.filter-item {
  min-width: 200rpx;
}

.reset-btn {
  background: #8E8E93;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.stats-bar {
  display: flex;
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  gap: 60rpx;
}

.stat-item {
  text-align: center;
  
  .stat-label {
    display: block;
    font-size: 28rpx;
    color: #666;
    margin-bottom: 10rpx;
  }
  
  .stat-value {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    
    &.pending {
      color: #FF9500;
    }
  }
}

.audit-list {
  .loading, .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400rpx;
    background: #fff;
    border-radius: 12rpx;
    
    .empty-text {
      margin-top: 20rpx;
      font-size: 28rpx;
      color: #999;
    }
  }
}

.audit-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.house-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.house-basic {
  display: flex;
  gap: 20rpx;
  
  .house-cover {
    width: 120rpx;
    height: 120rpx;
    border-radius: 8rpx;
    cursor: pointer;
  }
  
  .house-info {
    flex: 1;
    
    .house-title {
      display: block;
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .house-type {
      display: inline-block;
      background: #f0f0f0;
      color: #666;
      padding: 4rpx 12rpx;
      border-radius: 4rpx;
      font-size: 24rpx;
      margin-bottom: 8rpx;
    }
    
    .house-price {
      display: block;
      font-size: 28rpx;
      color: #FF3B30;
      font-weight: 600;
      margin-bottom: 8rpx;
    }
    
    .house-address {
      display: block;
      font-size: 24rpx;
      color: #999;
    }
  }
}

.house-meta {
  text-align: right;
  
  .submit-time, .publisher {
    display: block;
    font-size: 24rpx;
    color: #666;
    margin-bottom: 8rpx;
  }
}

.house-details {
  margin-bottom: 20rpx;
  
  .detail-row {
    display: flex;
    gap: 40rpx;
    margin-bottom: 15rpx;
    flex-wrap: wrap;
  }
  
  .detail-item {
    display: flex;
    gap: 10rpx;
    
    .detail-label {
      font-size: 26rpx;
      color: #666;
    }
    
    .detail-value {
      font-size: 26rpx;
      color: #333;
    }
  }
  
  .description {
    margin-bottom: 15rpx;
    
    .description-label {
      font-size: 26rpx;
      color: #666;
    }
    
    .description-text {
      font-size: 26rpx;
      color: #333;
      line-height: 1.5;
    }
  }
  
  .facilities {
    .facilities-label {
      font-size: 26rpx;
      color: #666;
      margin-bottom: 10rpx;
    }
    
    .facilities-list {
      display: flex;
      gap: 10rpx;
      flex-wrap: wrap;
    }
  }
}

.contact-info {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  
  .contact-label {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 10rpx;
  }
  
  .contact-item {
    display: block;
    font-size: 26rpx;
    color: #333;
    margin-bottom: 5rpx;
  }
}

.audit-actions {
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
}

.action-btn {
  padding: 16rpx 32rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  cursor: pointer;
  
  &.view-btn {
    background: #007AFF;
    color: #fff;
  }
  
  &.approve-btn {
    background: #34C759;
    color: #fff;
  }
  
  &.reject-btn {
    background: #FF3B30;
    color: #fff;
  }
}

.pagination {
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .stats-bar {
    flex-wrap: wrap;
    gap: 30rpx;
  }
  
  .house-header {
    flex-direction: column;
    gap: 20rpx;
  }
  
  .house-meta {
    text-align: left;
  }
  
  .detail-row {
    flex-direction: column;
    gap: 15rpx;
  }
  
  .audit-actions {
    flex-direction: column;
  }
}
</style>
