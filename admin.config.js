export default {
	login: {
		url: '/uni_modules/uni-id-pages/pages/login/login-withpwd' // 登录页面路径
	},
	index: {
		url: '/pages/index/index' // 登录后跳转的第一个页面
	},
	error: {
		url: '/pages/error/404' // 404 Not Found 错误页面路径
	},
	navBar: { // 顶部导航
		logo: '/static/logo.png', // 左侧 Logo
		title: '租房平台管理后台', // 系统标题
		langs: [{
			text: '中文简体',
			lang: 'zh-Hans'
		}],
		themes: [{
			text: '默认',
			value: 'default'
		}, {
			text: '绿柔',
			value: 'green'
		}],
		debug: {
			enable: process.env.NODE_ENV !== 'production', //是否显示错误信息
			engine: [{ // 搜索引擎配置（每条错误信息后，会自动生成搜索链接，点击后跳转至搜索引擎）
				name: '百度',
				url: 'https://www.baidu.com/baidu?wd=ERR_MSG'
			}, {
				name: '谷歌',
				url: 'https://www.google.com/search?q=ERR_MSG'
			}]
		}
	},
	sideBar: { // 左侧菜单
		// 配置静态菜单列表（放置在用户被授权的菜单列表下边）
		staticMenu: [{
			menu_id: "rental-overview",
			text: '数据概览',
			icon: 'admin-icons-tongji',
			url: "",
			children: [{
				menu_id: "dashboard",
				text: '平台概况',
				icon: 'admin-icons-dashboard',
				value: '/pages/rental/dashboard/dashboard',
			}, {
				menu_id: "statistics",
				text: '数据报表',
				icon: 'admin-icons-chart',
				value: '/pages/rental/statistics/statistics',
			}]
		}, {
			menu_id: "house-management",
			text: '房源管理',
			icon: 'admin-icons-house',
			url: "",
			children: [{
				menu_id: "house-list",
				text: '房源列表',
				icon: 'admin-icons-list',
				value: '/pages/rental/house/list',
			}, {
				menu_id: "house-audit",
				text: '房源审核',
				icon: 'admin-icons-audit',
				value: '/pages/rental/house/audit',
			}, {
				menu_id: "house-stats",
				text: '房源统计',
				icon: 'admin-icons-chart',
				value: '/pages/rental/house/stats',
			}]
		}, {
			menu_id: "user-management",
			text: '用户管理',
			icon: 'admin-icons-user',
			url: "",
			children: [{
				menu_id: "user-list",
				text: '用户列表',
				icon: 'admin-icons-list',
				value: '/pages/rental/user/list',
			}, {
				menu_id: "user-stats",
				text: '用户统计',
				icon: 'admin-icons-chart',
				value: '/pages/rental/user/stats',
			}]
		}, {
			menu_id: "appointment-management",
			text: '预约管理',
			icon: 'admin-icons-calendar',
			url: "",
			children: [{
				menu_id: "appointment-list",
				text: '预约列表',
				icon: 'admin-icons-list',
				value: '/pages/rental/appointment/list',
			}, {
				menu_id: "appointment-stats",
				text: '预约统计',
				icon: 'admin-icons-chart',
				value: '/pages/rental/appointment/stats',
			}]
		}, {
			menu_id: "data-sync",
			text: '数据同步',
			icon: 'admin-icons-refresh',
			value: '/pages/rental/data-sync/sync',
		}]
	},
	uniStat: {
		
	}
}
