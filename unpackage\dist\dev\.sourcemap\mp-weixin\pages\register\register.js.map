{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端/pages/register/register.vue?5702", "webpack:///D:/web/project/前端/pages/register/register.vue?adf9", "webpack:///D:/web/project/前端/pages/register/register.vue?0947", "webpack:///D:/web/project/前端/pages/register/register.vue?9b9f", "uni-app:///pages/register/register.vue", "webpack:///D:/web/project/前端/pages/register/register.vue?5298", "webpack:///D:/web/project/前端/pages/register/register.vue?d8d9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "form", "username", "mobile", "email", "password", "confirmPassword", "role", "showPassword", "showConfirmPassword", "agreed", "loading", "computed", "canRegister", "methods", "togglePassword", "toggleConfirmPassword", "selectRole", "onAgreementChange", "handleRegister", "uni", "title", "icon", "request", "action", "result", "setTimeout", "console", "<PERSON><PERSON><PERSON><PERSON>", "toPrivacy"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACqC;;;AAG5F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmoB,CAAgB,ioBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACkIvpB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;QAAAX;QAAAC;QAAAE;QAAAC;MACA,0BACAH,iBACAE,mBACAC,0BACA,eACA;IACA;EACA;EACAQ;IACA;IACAC;MACA;IACA;IAEAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,cAEA,8MAEA;gBAAA,MACAjB;kBAAA;kBAAA;gBAAA;gBACAkB;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,MAIAlB;kBAAA;kBAAA;gBAAA;gBACAgB;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,MAIAjB;kBAAA;kBAAA;gBAAA;gBACAe;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,MAIAjB;kBAAA;kBAAA;gBAAA;gBACAe;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBAAA;gBAAA;gBAAA,OAGAC;kBACAC;kBACAxB;oBACAE;oBACAC;oBACAC;oBACAC;oBACAE;kBACA;gBACA;cAAA;gBATAkB;gBAWA;kBACAL;oBACAC;oBACAC;kBACA;;kBAEA;kBACAI;oBACAN;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAO;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACAR;IACA;IAEA;IACAS;MACAT;QACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjRA;AAAA;AAAA;AAAA;AAA07B,CAAgB,o5BAAG,EAAC,C;;;;;;;;;;;ACA98B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/register/register.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/register/register.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./register.vue?vue&type=template&id=891c2434&scoped=true&\"\nvar renderjs\nimport script from \"./register.vue?vue&type=script&lang=js&\"\nexport * from \"./register.vue?vue&type=script&lang=js&\"\nimport style0 from \"./register.vue?vue&type=style&index=0&id=891c2434&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"891c2434\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/register/register.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=template&id=891c2434&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"register-container\">\n    <view class=\"header\">\n      <text class=\"title\">注册账号</text>\n      <text class=\"subtitle\">加入毕业租房平台</text>\n    </view>\n    \n    <view class=\"form-container\">\n      <view class=\"form-item\">\n        <text class=\"label\">用户名</text>\n        <view class=\"input-wrapper\">\n          <input \n            class=\"input\" \n            type=\"text\" \n            placeholder=\"请输入用户名\" \n            v-model=\"form.username\"\n            maxlength=\"20\"\n          />\n        </view>\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"label\">手机号</text>\n        <view class=\"input-wrapper\">\n          <input \n            class=\"input\" \n            type=\"number\" \n            placeholder=\"请输入手机号\" \n            v-model=\"form.mobile\"\n            maxlength=\"11\"\n          />\n        </view>\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"label\">邮箱（可选）</text>\n        <view class=\"input-wrapper\">\n          <input \n            class=\"input\" \n            type=\"text\" \n            placeholder=\"请输入邮箱\" \n            v-model=\"form.email\"\n          />\n        </view>\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"label\">密码</text>\n        <view class=\"input-wrapper\">\n          <input \n            class=\"input\" \n            :type=\"showPassword ? 'text' : 'password'\" \n            placeholder=\"请输入密码（6-20位）\" \n            v-model=\"form.password\"\n            maxlength=\"20\"\n          />\n          <uni-icons \n            :type=\"showPassword ? 'eye-slash' : 'eye'\" \n            size=\"20\" \n            color=\"#999\"\n            @click=\"togglePassword\"\n          ></uni-icons>\n        </view>\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"label\">确认密码</text>\n        <view class=\"input-wrapper\">\n          <input \n            class=\"input\" \n            :type=\"showConfirmPassword ? 'text' : 'password'\" \n            placeholder=\"请再次输入密码\" \n            v-model=\"form.confirmPassword\"\n            maxlength=\"20\"\n          />\n          <uni-icons \n            :type=\"showConfirmPassword ? 'eye-slash' : 'eye'\" \n            size=\"20\" \n            color=\"#999\"\n            @click=\"toggleConfirmPassword\"\n          ></uni-icons>\n        </view>\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"label\">身份类型</text>\n        <view class=\"role-selector\">\n          <view \n            class=\"role-item\" \n            :class=\"{ active: form.role === 'student' }\"\n            @click=\"selectRole('student')\"\n          >\n            <uni-icons type=\"person\" size=\"24\" :color=\"form.role === 'student' ? '#007aff' : '#999'\"></uni-icons>\n            <text class=\"role-text\">学生</text>\n          </view>\n          <view \n            class=\"role-item\" \n            :class=\"{ active: form.role === 'landlord' }\"\n            @click=\"selectRole('landlord')\"\n          >\n            <uni-icons type=\"home\" size=\"24\" :color=\"form.role === 'landlord' ? '#007aff' : '#999'\"></uni-icons>\n            <text class=\"role-text\">房东</text>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"agreement\">\n        <checkbox-group @change=\"onAgreementChange\">\n          <label class=\"agreement-item\">\n            <checkbox value=\"agree\" :checked=\"agreed\" />\n            <text class=\"agreement-text\">我已阅读并同意</text>\n            <text class=\"agreement-link\" @click=\"toPrivacy\">《用户协议》</text>\n            <text class=\"agreement-text\">和</text>\n            <text class=\"agreement-link\" @click=\"toPrivacy\">《隐私政策》</text>\n          </label>\n        </checkbox-group>\n      </view>\n      \n      <button class=\"register-btn\" @click=\"handleRegister\" :disabled=\"!canRegister\">\n        {{ loading ? '注册中...' : '注册' }}\n      </button>\n      \n      <view class=\"links\">\n        <text class=\"link\" @click=\"toLogin\">已有账号？立即登录</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport request from '@/utils/request.js'\nimport { validatePhone, validateEmail, setUserInfo } from '@/utils/common.js'\n\nexport default {\n  data() {\n    return {\n      form: {\n        username: '',\n        mobile: '',\n        email: '',\n        password: '',\n        confirmPassword: '',\n        role: 'student'\n      },\n      showPassword: false,\n      showConfirmPassword: false,\n      agreed: false,\n      loading: false\n    }\n  },\n  computed: {\n    canRegister() {\n      const { username, mobile, password, confirmPassword } = this.form\n      return username.trim() && \n             mobile.trim() && \n             password.trim() && \n             confirmPassword.trim() && \n             this.agreed && \n             !this.loading\n    }\n  },\n  methods: {\n    // 切换密码显示状态\n    togglePassword() {\n      this.showPassword = !this.showPassword\n    },\n    \n    toggleConfirmPassword() {\n      this.showConfirmPassword = !this.showConfirmPassword\n    },\n    \n    // 选择身份类型\n    selectRole(role) {\n      this.form.role = role\n    },\n    \n    // 协议勾选变化\n    onAgreementChange(e) {\n      this.agreed = e.detail.value.includes('agree')\n    },\n    \n    // 注册处理\n    async handleRegister() {\n      if (!this.canRegister) return\n      \n      const { username, mobile, email, password, confirmPassword, role } = this.form\n      \n      // 表单验证\n      if (username.length < 2) {\n        uni.showToast({\n          title: '用户名至少2个字符',\n          icon: 'none'\n        })\n        return\n      }\n      \n      if (!validatePhone(mobile)) {\n        uni.showToast({\n          title: '手机号格式不正确',\n          icon: 'none'\n        })\n        return\n      }\n      \n      if (email && !validateEmail(email)) {\n        uni.showToast({\n          title: '邮箱格式不正确',\n          icon: 'none'\n        })\n        return\n      }\n      \n      if (password.length < 6) {\n        uni.showToast({\n          title: '密码至少6个字符',\n          icon: 'none'\n        })\n        return\n      }\n      \n      if (password !== confirmPassword) {\n        uni.showToast({\n          title: '两次密码输入不一致',\n          icon: 'none'\n        })\n        return\n      }\n      \n      this.loading = true\n      \n      try {\n        const result = await request.callFunction('user-auth', {\n          action: 'register',\n          data: {\n            username: username.trim(),\n            mobile: mobile.trim(),\n            email: email.trim() || undefined,\n            password: password,\n            role: role\n          }\n        })\n        \n        if (result.code === 0) {\n          uni.showToast({\n            title: '注册成功',\n            icon: 'success'\n          })\n          \n          // 延迟跳转到登录页\n          setTimeout(() => {\n            uni.navigateBack()\n          }, 1500)\n        }\n      } catch (error) {\n        console.error('注册失败:', error)\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 跳转到登录页\n    toLogin() {\n      uni.navigateBack()\n    },\n    \n    // 跳转到隐私政策页\n    toPrivacy() {\n      uni.showToast({\n        title: '功能开发中',\n        icon: 'none'\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.register-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n  padding: 40rpx;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 60rpx;\n}\n\n.title {\n  display: block;\n  font-size: 48rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10rpx;\n}\n\n.subtitle {\n  display: block;\n  font-size: 28rpx;\n  color: #666;\n}\n\n.form-container {\n  background: #fff;\n  border-radius: 20rpx;\n  padding: 40rpx;\n}\n\n.form-item {\n  margin-bottom: 40rpx;\n}\n\n.label {\n  display: block;\n  font-size: 28rpx;\n  color: #333;\n  margin-bottom: 20rpx;\n  font-weight: 500;\n}\n\n.input-wrapper {\n  background: #f8f9fa;\n  border-radius: 12rpx;\n  padding: 0 24rpx;\n  display: flex;\n  align-items: center;\n  height: 88rpx;\n  border: 2rpx solid transparent;\n}\n\n.input-wrapper:focus-within {\n  border-color: #007aff;\n  background: #fff;\n}\n\n.input {\n  flex: 1;\n  font-size: 32rpx;\n  color: #333;\n}\n\n.role-selector {\n  display: flex;\n  gap: 20rpx;\n}\n\n.role-item {\n  flex: 1;\n  background: #f8f9fa;\n  border-radius: 12rpx;\n  padding: 30rpx 20rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  border: 2rpx solid transparent;\n}\n\n.role-item.active {\n  background: #e3f2fd;\n  border-color: #007aff;\n}\n\n.role-text {\n  font-size: 28rpx;\n  color: #333;\n  margin-top: 10rpx;\n}\n\n.agreement {\n  margin-bottom: 40rpx;\n}\n\n.agreement-item {\n  display: flex;\n  align-items: center;\n  font-size: 26rpx;\n}\n\n.agreement-text {\n  color: #666;\n  margin-left: 10rpx;\n}\n\n.agreement-link {\n  color: #007aff;\n  margin-left: 5rpx;\n}\n\n.register-btn {\n  width: 100%;\n  height: 88rpx;\n  background: linear-gradient(45deg, #007aff, #0056d3);\n  color: #fff;\n  border: none;\n  border-radius: 12rpx;\n  font-size: 32rpx;\n  font-weight: bold;\n  margin-bottom: 30rpx;\n}\n\n.register-btn:disabled {\n  background: #ccc;\n}\n\n.links {\n  text-align: center;\n}\n\n.link {\n  color: #007aff;\n  font-size: 28rpx;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=style&index=0&id=891c2434&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=style&index=0&id=891c2434&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751999915\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}