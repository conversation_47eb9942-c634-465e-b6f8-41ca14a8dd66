<view class="messages-container data-v-bcbfc132"><view class="type-tabs data-v-bcbfc132"><block wx:for="{{typeTabs}}" wx:for-item="tab" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['switchTab',[index]]]]]}}" class="{{['tab-item','data-v-bcbfc132',(currentTab===index)?'active':'']}}" bindtap="__e"><text class="tab-text data-v-bcbfc132">{{tab.label}}</text><block wx:if="{{tab.count>0}}"><text class="tab-count data-v-bcbfc132">{{tab.count}}</text></block></view></block></view><block wx:if="{{$root.g0>0}}"><view class="action-bar data-v-bcbfc132"><view class="action-left data-v-bcbfc132"><text class="unread-count data-v-bcbfc132">{{unreadCount+"条未读"}}</text></view><view class="action-right data-v-bcbfc132"><block wx:if="{{unreadCount>0}}"><text data-event-opts="{{[['tap',[['markAllAsRead',['$event']]]]]}}" class="mark-all-read data-v-bcbfc132" bindtap="__e">全部已读</text></block></view></view></block><scroll-view class="message-list data-v-bcbfc132" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><block wx:for="{{$root.l0}}" wx:for-item="message" wx:for-index="__i0__" wx:key="_id"><view data-event-opts="{{[['tap',[['readMessage',['$0'],[[['messageList','_id',message.$orig._id]]]]]]]}}" class="{{['message-item','data-v-bcbfc132',(!message.$orig.is_read)?'unread':'']}}" bindtap="__e"><view class="message-header data-v-bcbfc132"><view class="{{['message-type','data-v-bcbfc132',message.$orig.type]}}">{{''+message.m0+''}}</view><text class="message-time data-v-bcbfc132">{{message.m1}}</text></view><view class="message-content data-v-bcbfc132"><text class="message-title data-v-bcbfc132">{{message.$orig.title}}</text><text class="message-text data-v-bcbfc132">{{message.$orig.content}}</text></view><block wx:if="{{message.$orig.from_user}}"><view class="message-footer data-v-bcbfc132"><view class="sender-info data-v-bcbfc132"><image class="sender-avatar data-v-bcbfc132" src="{{message.$orig.from_user.avatar||'/static/default-avatar.png'}}" mode="aspectFill"></image><text class="sender-name data-v-bcbfc132">{{message.$orig.from_user.nickname||'系统消息'}}</text></view><view class="read-status data-v-bcbfc132"><block wx:if="{{!message.$orig.is_read}}"><uni-icons vue-id="{{'db376302-1-'+__i0__}}" type="circle" size="8" color="#ff4757" class="data-v-bcbfc132" bind:__l="__l"></uni-icons></block><block wx:else><text class="read-text data-v-bcbfc132">已读</text></block></view></view></block></view></block><block wx:if="{{$root.g1>0}}"><view class="load-status data-v-bcbfc132"><block wx:if="{{loading}}"><text class="data-v-bcbfc132">加载中...</text></block><block wx:else><block wx:if="{{noMore}}"><text class="data-v-bcbfc132">没有更多了</text></block></block></view></block><block wx:if="{{$root.g2}}"><view class="empty-state data-v-bcbfc132"><image src="/static/empty-message.png" mode="aspectFit" class="data-v-bcbfc132"></image><text class="empty-text data-v-bcbfc132">暂无消息</text><text class="empty-tip data-v-bcbfc132">消息通知会在这里显示</text></view></block></scroll-view></view>