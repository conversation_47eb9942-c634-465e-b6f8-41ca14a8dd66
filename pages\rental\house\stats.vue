<template>
  <view class="house-stats">
    <!-- 筛选条件 -->
    <view class="filter-section">
      <view class="filter-row">
        <view class="filter-item">
          <text class="filter-label">时间范围</text>
          <uni-datetime-picker 
            v-model="dateRange" 
            type="daterange"
            :clear-icon="false"
            @change="onDateRangeChange"
          />
        </view>
        <view class="filter-item">
          <text class="filter-label">房源类型</text>
          <uni-data-picker 
            v-model="houseType" 
            :localdata="typeOptions"
            @change="onTypeChange"
          />
        </view>
        <view class="filter-item">
          <button class="export-btn" @click="exportData">导出报表</button>
        </view>
      </view>
    </view>

    <!-- 概览统计 -->
    <view class="overview-stats">
      <view class="stat-card">
        <view class="stat-icon house-icon">
          <uni-icons type="home" size="24" color="#fff" />
        </view>
        <view class="stat-content">
          <text class="stat-value">{{ overviewData.totalHouses }}</text>
          <text class="stat-label">总房源数</text>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-icon verified-icon">
          <uni-icons type="checkmarkempty" size="24" color="#fff" />
        </view>
        <view class="stat-content">
          <text class="stat-value">{{ overviewData.verifiedHouses }}</text>
          <text class="stat-label">已审核</text>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-icon pending-icon">
          <uni-icons type="clock" size="24" color="#fff" />
        </view>
        <view class="stat-content">
          <text class="stat-value">{{ overviewData.pendingHouses }}</text>
          <text class="stat-label">待审核</text>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-icon rented-icon">
          <uni-icons type="person" size="24" color="#fff" />
        </view>
        <view class="stat-content">
          <text class="stat-value">{{ overviewData.rentedHouses }}</text>
          <text class="stat-label">已出租</text>
        </view>
      </view>
    </view>

    <!-- 图表区域 -->
    <view class="charts-section">
      <!-- 发布趋势图 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">房源发布趋势</text>
          <uni-data-picker 
            v-model="trendPeriod" 
            :localdata="periodOptions"
            @change="onPeriodChange"
          />
        </view>
        <view class="chart-content">
          <qiun-data-charts 
            type="line"
            :opts="trendChartOpts"
            :chartData="trendChartData"
            :loading="chartLoading"
          />
        </view>
      </view>

      <!-- 类型分布图 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">房源类型分布</text>
        </view>
        <view class="chart-content">
          <qiun-data-charts 
            type="pie"
            :opts="typeChartOpts"
            :chartData="typeChartData"
            :loading="chartLoading"
          />
        </view>
      </view>

      <!-- 价格分布图 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">房源价格分布</text>
        </view>
        <view class="chart-content">
          <qiun-data-charts 
            type="column"
            :opts="priceChartOpts"
            :chartData="priceChartData"
            :loading="chartLoading"
          />
        </view>
      </view>

      <!-- 地区分布图 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">房源地区分布</text>
        </view>
        <view class="chart-content">
          <qiun-data-charts 
            type="bar"
            :opts="areaChartOpts"
            :chartData="areaChartData"
            :loading="chartLoading"
          />
        </view>
      </view>

      <!-- 热门房源排行 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">热门房源排行</text>
        </view>
        <view class="ranking-list">
          <view 
            v-for="(house, index) in hotHouses" 
            :key="house._id"
            class="ranking-item"
            @click="viewHouse(house)"
          >
            <view class="ranking-number" :class="getRankingClass(index)">
              {{ index + 1 }}
            </view>
            <image 
              class="house-cover" 
              :src="house.images && house.images[0] || '/static/placeholder.png'"
              mode="aspectFill"
            />
            <view class="house-info">
              <text class="house-title">{{ house.title }}</text>
              <text class="house-price">¥{{ house.price }}/月</text>
              <text class="house-views">浏览 {{ house.view_count }} 次</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 详细数据表格 -->
    <view class="data-table-section">
      <view class="section-header">
        <text class="section-title">详细统计数据</text>
      </view>
      
      <uni-table 
        :loading="tableLoading"
        border
        stripe
        emptyText="暂无数据"
      >
        <uni-tr>
          <uni-th align="center">日期</uni-th>
          <uni-th align="center">新增房源</uni-th>
          <uni-th align="center">审核通过</uni-th>
          <uni-th align="center">审核拒绝</uni-th>
          <uni-th align="center">总浏览量</uni-th>
          <uni-th align="center">新增收藏</uni-th>
        </uni-tr>
        <uni-tr v-for="(item, index) in tableData" :key="index">
          <uni-td align="center">{{ item.date }}</uni-td>
          <uni-td align="center">{{ item.newHouses }}</uni-td>
          <uni-td align="center">{{ item.approved }}</uni-td>
          <uni-td align="center">{{ item.rejected }}</uni-td>
          <uni-td align="center">{{ item.totalViews }}</uni-td>
          <uni-td align="center">{{ item.newFavorites }}</uni-td>
        </uni-tr>
      </uni-table>
      
      <!-- 分页 -->
      <view class="pagination">
        <uni-pagination 
          :current="currentPage"
          :total="totalCount"
          :pageSize="pageSize"
          @change="onPageChange"
        />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'HouseStats',
  data() {
    return {
      chartLoading: true,
      tableLoading: true,
      
      // 筛选条件
      dateRange: [],
      houseType: '',
      trendPeriod: 'month',
      
      // 筛选选项
      typeOptions: [
        { value: '', text: '全部类型' },
        { value: '整租', text: '整租' },
        { value: '合租', text: '合租' },
        { value: '单间', text: '单间' }
      ],
      
      periodOptions: [
        { value: 'week', text: '最近7天' },
        { value: 'month', text: '最近30天' },
        { value: 'quarter', text: '最近3个月' }
      ],

      // 概览数据
      overviewData: {
        totalHouses: 0,
        verifiedHouses: 0,
        pendingHouses: 0,
        rentedHouses: 0
      },

      // 图表配置
      trendChartOpts: {
        color: ['#007AFF', '#34C759'],
        padding: [15, 15, 0, 15],
        enableScroll: false,
        legend: {
          show: true
        },
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2
        },
        extra: {
          line: {
            type: 'curve',
            width: 2
          }
        }
      },

      typeChartOpts: {
        color: ['#007AFF', '#34C759', '#FF9500'],
        padding: [5, 5, 5, 5],
        enableScroll: false,
        legend: {
          show: true,
          position: 'right'
        },
        extra: {
          pie: {
            activeOpacity: 0.5,
            activeRadius: 10,
            offsetAngle: 0,
            labelWidth: 15,
            border: true,
            borderWidth: 3,
            borderColor: '#FFFFFF'
          }
        }
      },

      priceChartOpts: {
        color: ['#FF9500'],
        padding: [15, 15, 0, 15],
        enableScroll: false,
        legend: {
          show: false
        },
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2
        }
      },

      areaChartOpts: {
        color: ['#34C759'],
        padding: [15, 15, 0, 15],
        enableScroll: false,
        legend: {
          show: false
        },
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2
        }
      },

      // 图表数据
      trendChartData: {},
      typeChartData: {},
      priceChartData: {},
      areaChartData: {},

      // 热门房源
      hotHouses: [],

      // 表格数据
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0
    }
  },

  onLoad() {
    this.initDateRange()
    this.loadStatsData()
  },

  methods: {
    // 初始化日期范围
    initDateRange() {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 30)
      
      this.dateRange = [
        start.toISOString().split('T')[0],
        end.toISOString().split('T')[0]
      ]
    },

    // 加载统计数据
    async loadStatsData() {
      try {
        this.chartLoading = true
        this.tableLoading = true

        // 并发加载所有数据
        await Promise.all([
          this.loadOverviewData(),
          this.loadChartData(),
          this.loadHotHouses(),
          this.loadTableData()
        ])

        this.chartLoading = false
        this.tableLoading = false
      } catch (error) {
        console.error('加载统计数据失败:', error)
        uni.showToast({
          title: '加载数据失败',
          icon: 'none'
        })
        this.chartLoading = false
        this.tableLoading = false
      }
    },

    // 加载概览数据
    async loadOverviewData() {
      try {
        const result = await uniCloud.callFunction({
          name: 'system-management',
          data: {
            action: 'getHouseOverview',
            dateRange: this.dateRange,
            houseType: this.houseType
          }
        })

        if (result.result.code === 0) {
          this.overviewData = result.result.data
        }
      } catch (error) {
        console.error('加载概览数据失败:', error)
      }
    },

    // 加载图表数据
    async loadChartData() {
      // 模拟数据，实际应该调用云函数
      this.trendChartData = {
        categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
        series: [
          {
            name: '发布数量',
            data: [35, 36, 31, 33, 13, 34]
          },
          {
            name: '审核通过',
            data: [28, 30, 25, 28, 10, 29]
          }
        ]
      }

      this.typeChartData = {
        series: [{
          name: '房源类型',
          data: [
            { name: '整租', value: 45 },
            { name: '合租', value: 35 },
            { name: '单间', value: 20 }
          ]
        }]
      }

      this.priceChartData = {
        categories: ['1000以下', '1000-2000', '2000-3000', '3000-4000', '4000以上'],
        series: [{
          name: '房源数量',
          data: [12, 25, 35, 18, 10]
        }]
      }

      this.areaChartData = {
        categories: ['朝阳区', '海淀区', '丰台区', '西城区', '东城区'],
        series: [{
          name: '房源数量',
          data: [45, 38, 32, 28, 25]
        }]
      }
    },

    // 加载热门房源
    async loadHotHouses() {
      // 模拟数据
      this.hotHouses = [
        {
          _id: '1',
          title: '阳光小区三室一厅',
          price: 3500,
          view_count: 156,
          images: ['/static/house1.jpg']
        },
        {
          _id: '2',
          title: '市中心精装公寓',
          price: 4200,
          view_count: 142,
          images: ['/static/house2.jpg']
        },
        {
          _id: '3',
          title: '学区房两室一厅',
          price: 2800,
          view_count: 128,
          images: ['/static/house3.jpg']
        }
      ]
    },

    // 加载表格数据
    async loadTableData() {
      // 模拟数据
      this.tableData = [
        {
          date: '2024-01-01',
          newHouses: 5,
          approved: 4,
          rejected: 1,
          totalViews: 156,
          newFavorites: 23
        },
        {
          date: '2024-01-02',
          newHouses: 3,
          approved: 3,
          rejected: 0,
          totalViews: 142,
          newFavorites: 18
        }
      ]
      this.totalCount = 100
    },

    // 日期范围改变
    onDateRangeChange() {
      this.loadStatsData()
    },

    // 房源类型改变
    onTypeChange() {
      this.loadStatsData()
    },

    // 趋势周期改变
    onPeriodChange() {
      this.loadChartData()
    },

    // 查看房源
    viewHouse(house) {
      uni.navigateTo({
        url: `/pages/rental/house/detail?id=${house._id}`
      })
    },

    // 获取排名样式
    getRankingClass(index) {
      if (index === 0) return 'first'
      if (index === 1) return 'second'
      if (index === 2) return 'third'
      return 'normal'
    },

    // 分页改变
    onPageChange(page) {
      this.currentPage = page
      this.loadTableData()
    },

    // 导出数据
    exportData() {
      uni.showToast({
        title: '导出功能开发中',
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.house-stats {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.filter-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 40rpx;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  
  .filter-label {
    font-size: 28rpx;
    color: #333;
    white-space: nowrap;
  }
  
  .export-btn {
    background: #007AFF;
    color: #fff;
    border: none;
    border-radius: 8rpx;
    padding: 16rpx 32rpx;
    font-size: 28rpx;
  }
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stat-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.house-icon {
    background: #007AFF;
  }
  
  &.verified-icon {
    background: #34C759;
  }
  
  &.pending-icon {
    background: #FF9500;
  }
  
  &.rented-icon {
    background: #8E8E93;
  }
}

.stat-content {
  .stat-value {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
  }
  
  .stat-label {
    font-size: 24rpx;
    color: #666;
  }
}

.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500rpx, 1fr));
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.chart-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  
  .chart-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
}

.chart-content {
  height: 400rpx;
}

.ranking-list {
  .ranking-item {
    display: flex;
    align-items: center;
    gap: 20rpx;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    cursor: pointer;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:hover {
      background: #f8f8f8;
    }
  }
  
  .ranking-number {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    font-weight: 600;
    color: #fff;
    
    &.first {
      background: #FFD700;
    }
    
    &.second {
      background: #C0C0C0;
    }
    
    &.third {
      background: #CD7F32;
    }
    
    &.normal {
      background: #8E8E93;
    }
  }
  
  .house-cover {
    width: 80rpx;
    height: 80rpx;
    border-radius: 8rpx;
  }
  
  .house-info {
    flex: 1;
    
    .house-title {
      display: block;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 8rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .house-price {
      display: block;
      font-size: 24rpx;
      color: #FF3B30;
      font-weight: 600;
      margin-bottom: 4rpx;
    }
    
    .house-views {
      font-size: 22rpx;
      color: #999;
    }
  }
}

.data-table-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  margin-bottom: 30rpx;
  
  .section-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
}

.pagination {
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .overview-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .charts-section {
    grid-template-columns: 1fr;
  }
}
</style>
