'use strict';

const uniID = require('uni-id-common');

// 通用 checkToken 函数
async function checkUserToken(token, context) {
  const uniIdIns = uniID.createInstance({ context });
  const payload = await uniIdIns.checkToken(token);

  if (payload.errCode === 0) {
    return {
      code: 0,
      uid: payload.uid,
      userInfo: payload.userInfo
    };
  } else {
    return {
      code: payload.errCode || 401,
      message: payload.errMsg || '身份验证失败'
    };
  }
}

exports.main = async (event, context) => {
  const { action, data } = event;
  
  try {
    switch (action) {
      case 'register':
        return await register(event, context);
      case 'login':
        return await login(event, context);
      case 'logout':
        return await logout(event, context);
      case 'getUserInfo':
        return await getUserInfo(event, context);
      case 'updateProfile':
        return await updateProfile(event, context);
      case 'uploadAvatar':
        return await uploadAvatar(event, context);

      case 'initTestUsers':
        return await initTestUsers(event, context);
      case 'initTestData':
        return await initTestData(event, context);
      case 'getUserListForAdmin':
        return await getUserListForAdmin(event, context);
      case 'verifyIdentity':
        return await verifyIdentity(event, context);
      default:
        return {
          code: 400,
          message: '无效的操作'
        };
    }
  } catch (error) {
    console.error('用户认证云函数执行错误:', error);
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message
    };
  }
};

// 用户注册
async function register(event, context) {
  const { username, password, mobile, email, role = 'student' } = event.data;
  
  // 参数验证
  if (!username || !password || !mobile) {
    return {
      code: 400,
      message: '用户名、密码和手机号不能为空'
    };
  }
  
  // 手机号格式验证
  const phoneReg = /^1[3-9]\d{9}$/;
  if (!phoneReg.test(mobile)) {
    return {
      code: 400,
      message: '手机号格式不正确'
    };
  }
  
  try {
    const uniIdIns = uniID.createInstance({
      context: context
    });

    // 检查用户名是否已存在
    const db = uniCloud.database();
    const existingUser = await db.collection('uni-id-users').where({
      username: username
    }).get();
    
    if (existingUser.data.length > 0) {
      return {
        code: 400,
        message: '用户名已存在'
      };
    }

    // 检查手机号是否已存在
    const existingMobile = await db.collection('uni-id-users').where({
      mobile: mobile
    }).get();
    
    if (existingMobile.data.length > 0) {
      return {
        code: 400,
        message: '手机号已被注册'
      };
    }

    // 创建用户记录
    const now = new Date();
    const userData = {
      username,
      password, // 保存密码
      mobile,
      email: email || '',
      role: [role],
      status: 0,
      register_date: now,
      last_login_date: now,
      student_info: role === 'student' ? {
        school: '',
        major: '',
        graduation_year: '',
        student_id: '',
        verified: false
      } : null,
      landlord_info: role === 'landlord' ? {
        real_name: '',
        id_card: '',
        verified: false
      } : null
    };

    // 添加用户到数据库
    const addResult = await db.collection('uni-id-users').add(userData);
    
    if (addResult.id) {
      // 生成 token
      const tokenResult = await uniIdIns.createToken({
        uid: addResult.id
      });
      
      if (tokenResult.errCode === 0) {
        return {
          code: 0,
          message: '注册成功',
          data: {
            uid: addResult.id,
            token: tokenResult.token,
            tokenExpired: tokenResult.tokenExpired
          }
        };
      } else {
        return {
          code: tokenResult.errCode,
          message: tokenResult.errMsg || '注册失败'
        };
      }
    } else {
      return {
        code: 500,
        message: '注册失败'
      };
    }
  } catch (error) {
    console.error('注册错误:', error);
    return {
      code: 500,
      message: '注册失败',
      error: error.message
    };
  }
}

// 用户登录
async function login(event, context) {
  const { username, password } = event.data;
  
  if (!username || !password) {
    return {
      code: 400,
      message: '用户名和密码不能为空'
    };
  }
  
  try {
    const uniIdIns = uniID.createInstance({
      context: context
    });

    // 查询用户
    const db = uniCloud.database();
    const userRes = await db.collection('uni-id-users').where({
      username: username
    }).get();
    
    if (userRes.data.length === 0) {
      return {
        code: 400,
        message: '用户名或密码错误'
      };
    }
    
    const user = userRes.data[0];
    
    // 简单的密码验证（实际项目中应该使用加密）
    if (user.password !== password) {
      return {
        code: 400,
        message: '用户名或密码错误'
      };
    }
    
    // 检查用户状态
    if (user.status !== 0) {
      return {
        code: 400,
        message: '账户已被禁用'
      };
    }

    // 生成 token
    const tokenResult = await uniIdIns.createToken({
      uid: user._id
    });
    
    if (tokenResult.errCode === 0) {
      // 更新最后登录时间
      await db.collection('uni-id-users').doc(user._id).update({
        last_login_date: new Date()
      });

      // 移除敏感信息
      delete user.password;
      delete user.token;

      return {
        code: 0,
        message: '登录成功',
        data: {
          uid: user._id,
          token: tokenResult.token,
          tokenExpired: tokenResult.tokenExpired,
          userInfo: user
        }
      };
    } else {
      return {
        code: tokenResult.errCode,
        message: tokenResult.errMsg || '登录失败'
      };
    }
  } catch (error) {
    console.error('登录错误:', error);
    return {
      code: 500,
      message: '登录失败',
      error: error.message
    };
  }
}

// 用户登出
async function logout(event, context) {
  const uniIdIns = uniID.createInstance({
    context: context
  });

  const payload = await uniIdIns.checkToken(event.uniIdToken);
  if (payload.errCode !== 0) {
    return {
      code: payload.errCode,
      message: payload.errMsg || '登出失败'
    };
  }

  try {
    // 清除用户token
    const db = uniCloud.database();
    await db.collection('uni-id-users').doc(payload.uid).update({
      token: []
    });
    
    return {
      code: 0,
      message: '登出成功'
    };
  } catch (error) {
    return {
      code: 500,
      message: '登出失败',
      error: error.message
    };
  }
}

// 获取用户信息
async function getUserInfo(event, context) {
  const uniIdIns = uniID.createInstance({
    context: context
  });

  const payload = await uniIdIns.checkToken(event.uniIdToken);
  if (payload.errCode !== 0) {
    return {
      code: payload.errCode,
      message: payload.errMsg || '获取用户信息失败'
    };
  }
  
  try {
    const db = uniCloud.database();
    const userRes = await db.collection('uni-id-users').doc(payload.uid).get();
    
    if (userRes.data.length === 0) {
      return {
        code: 404,
        message: '用户不存在'
      };
    }
    
    const user = userRes.data[0];
    // 移除敏感信息
    delete user.password;
    delete user.token;
    
    return {
      code: 0,
      message: '获取成功',
      data: user
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取用户信息失败',
      error: error.message
    };
  }
}

// 更新用户资料
async function updateProfile(event, context) {
  const uniIdIns = uniID.createInstance({
    context: context
  });

  const payload = await uniIdIns.checkToken(event.uniIdToken);
  if (payload.errCode !== 0) {
    return {
      code: payload.errCode,
      message: payload.errMsg || '更新失败'
    };
  }
  
  const { nickname, avatar, gender, student_info, landlord_info } = event.data;
  
  try {
    const db = uniCloud.database();
    const updateData = {};
    
    if (nickname !== undefined) updateData.nickname = nickname;
    if (avatar !== undefined) updateData.avatar = avatar;
    if (gender !== undefined) updateData.gender = gender;
    if (student_info !== undefined) updateData.student_info = student_info;
    if (landlord_info !== undefined) updateData.landlord_info = landlord_info;
    
    await db.collection('uni-id-users').doc(payload.uid).update(updateData);
    
    return {
      code: 0,
      message: '更新成功'
    };
  } catch (error) {
    return {
      code: 500,
      message: '更新失败',
      error: error.message
    };
  }
}

// 上传头像
async function uploadAvatar(event, context) {
  const uniIdIns = uniID.createInstance({
    context: context
  });

  const payload = await uniIdIns.checkToken(event.uniIdToken);
  if (payload.errCode !== 0) {
    return {
      code: payload.errCode,
      message: payload.errMsg || '上传失败'
    };
  }
  
  const { avatar } = event.data;
  
  if (!avatar) {
    return {
      code: 400,
      message: '头像不能为空'
    };
  }
  
  try {
    const db = uniCloud.database();
    await db.collection('uni-id-users').doc(payload.uid).update({
      avatar: avatar
    });
    
    return {
      code: 0,
      message: '头像上传成功',
      data: { avatar }
    };
  } catch (error) {
    return {
      code: 500,
      message: '头像上传失败',
      error: error.message
    };
  }
}

// 统一身份认证
async function verifyIdentity(event, context) {
  const uniIdIns = uniID.createInstance({
    context: context
  });

  const payload = await uniIdIns.checkToken(event.uniIdToken);
  if (payload.errCode !== 0) {
    return {
      code: payload.errCode,
      message: payload.errMsg || '认证失败'
    };
  }

  const { identity_type, identity_data } = event.data;

  if (!identity_type || !identity_data) {
    return {
      code: 400,
      message: '身份类型和身份信息不能为空'
    };
  }

  // 根据身份类型验证必填字段
  if (identity_type === 'student') {
    const { school, major, graduation_year, student_id } = identity_data;
    if (!school || !major || !graduation_year || !student_id) {
      return {
        code: 400,
        message: '请填写完整的学生信息'
      };
    }
  } else if (identity_type === 'landlord') {
    const { real_name, id_card } = identity_data;
    if (!real_name || !id_card) {
      return {
        code: 400,
        message: '请填写完整的房东信息'
      };
    }

    // 身份证号格式验证
    const idCardReg = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    if (!idCardReg.test(id_card)) {
      return {
        code: 400,
        message: '身份证号格式不正确'
      };
    }
  }

  try {
    const db = uniCloud.database();
    const updateData = {
      [`${identity_type}_info`]: {
        ...identity_data,
        verified: true,
        verify_date: Date.now()
      }
    };

    await db.collection('uni-id-users').doc(payload.uid).update(updateData);

    return {
      code: 0,
      message: '身份认证成功'
    };
  } catch (error) {
    return {
      code: 500,
      message: '身份认证失败',
      error: error.message
    };
  }
}

// 初始化测试用户数据
async function initTestUsers(event, context) {
  try {
    const { users } = event;
    const db = uniCloud.database();
    const userCollection = db.collection('uni-id-users');

    // 清空现有测试数据
    await userCollection.where({
      _id: db.command.in(['user_001', 'user_002', 'user_003'])
    }).remove();

    // 插入新的测试数据
    for (const user of users) {
      await userCollection.add(user);
    }

    // 同时创建一些测试房源和预约数据
    await initTestHouses();
    await initTestAppointments();

    return {
      code: 0,
      message: '测试数据初始化成功',
      data: {
        userCount: users.length
      }
    };
  } catch (error) {
    console.error('初始化测试用户失败:', error);
    return {
      code: 500,
      message: '初始化测试用户失败',
      error: error.message
    };
  }
}

// 初始化测试房源数据
async function initTestHouses() {
  const db = uniCloud.database();
  const houseCollection = db.collection('houses');

  const testHouses = [
    {
      _id: 'house_001',
      title: '阳光小区三室一厅',
      description: '房屋位于市中心，交通便利，周边配套设施齐全。',
      price: 3500,
      area: 120,
      room_count: 3,
      hall_count: 1,
      bathroom_count: 2,
      floor: 8,
      total_floors: 18,
      orientation: '南北',
      decoration: '精装修',
      images: ['/static/house1.jpg', '/static/house2.jpg'],
      facilities: ['空调', '洗衣机', '冰箱', '热水器', '宽带'],
      location: {
        province: '北京市',
        city: '北京市',
        district: '朝阳区',
        address: '阳光小区8号楼',
        longitude: 116.4074,
        latitude: 39.9042
      },
      contact: {
        name: '张先生',
        phone: '13800138001'
      },
      publisher_id: 'user_001',
      is_verified: true,
      status: 'published',
      view_count: 156,
      favorite_count: 23,
      appointment_count: 8,
      create_time: new Date('2024-01-15'),
      update_time: new Date('2024-01-20')
    },
    {
      _id: 'house_002',
      title: '市中心精装公寓',
      description: '现代化装修，家具家电齐全，拎包入住。',
      price: 4200,
      area: 85,
      room_count: 2,
      hall_count: 1,
      bathroom_count: 1,
      floor: 15,
      total_floors: 25,
      orientation: '南向',
      decoration: '精装修',
      images: ['/static/house3.jpg', '/static/house4.jpg'],
      facilities: ['空调', '洗衣机', '冰箱', '热水器', '宽带', '电视'],
      location: {
        province: '北京市',
        city: '北京市',
        district: '海淀区',
        address: '中关村大厦15层',
        longitude: 116.3074,
        latitude: 39.9842
      },
      contact: {
        name: '李女士',
        phone: '13800138002'
      },
      publisher_id: 'user_002',
      is_verified: true,
      status: 'published',
      view_count: 203,
      favorite_count: 31,
      appointment_count: 12,
      create_time: new Date('2024-01-16'),
      update_time: new Date('2024-01-21')
    },
    {
      _id: 'house_003',
      title: '学区房两室一厅',
      description: '临近重点学校，适合有孩子的家庭。',
      price: 2800,
      area: 95,
      room_count: 2,
      hall_count: 1,
      bathroom_count: 1,
      floor: 5,
      total_floors: 12,
      orientation: '东南',
      decoration: '简装修',
      images: ['/static/house5.jpg'],
      facilities: ['空调', '热水器', '宽带'],
      location: {
        province: '北京市',
        city: '北京市',
        district: '西城区',
        address: '学府路小区5号楼',
        longitude: 116.2074,
        latitude: 39.8842
      },
      contact: {
        name: '王老师',
        phone: '13800138003'
      },
      publisher_id: 'user_003',
      is_verified: true,
      status: 'published',
      view_count: 89,
      favorite_count: 15,
      appointment_count: 5,
      create_time: new Date('2024-01-17'),
      update_time: new Date('2024-01-22')
    }
  ];

  // 清空现有测试数据
  await houseCollection.where({
    _id: db.command.in(['house_001', 'house_002', 'house_003'])
  }).remove();

  // 插入新的测试数据
  for (const house of testHouses) {
    await houseCollection.add(house);
  }
}

// 初始化测试预约数据
async function initTestAppointments() {
  const db = uniCloud.database();
  const appointmentCollection = db.collection('appointments');

  const testAppointments = [
    {
      _id: 'appointment_001',
      house_id: 'house_001',
      user_id: 'user_002',
      contact_name: '李老师',
      contact_phone: '13800138002',
      appointment_time: new Date('2024-01-25 14:00:00'),
      message: '希望能看房，周末有时间。',
      status: 'confirmed',
      create_time: new Date('2024-01-20'),
      update_time: new Date('2024-01-21')
    },
    {
      _id: 'appointment_002',
      house_id: 'house_002',
      user_id: 'user_003',
      contact_name: '王先生',
      contact_phone: '13800138003',
      appointment_time: new Date('2024-01-26 10:00:00'),
      message: '想了解一下房屋详情。',
      status: 'pending',
      create_time: new Date('2024-01-21'),
      update_time: new Date('2024-01-21')
    },
    {
      _id: 'appointment_003',
      house_id: 'house_001',
      user_id: 'user_003',
      contact_name: '王先生',
      contact_phone: '13800138003',
      appointment_time: new Date('2024-01-24 16:00:00'),
      message: '下班后看房。',
      status: 'completed',
      create_time: new Date('2024-01-19'),
      update_time: new Date('2024-01-24')
    }
  ];

  // 清空现有测试数据
  await appointmentCollection.where({
    _id: db.command.in(['appointment_001', 'appointment_002', 'appointment_003'])
  }).remove();

  // 插入新的测试数据
  for (const appointment of testAppointments) {
    await appointmentCollection.add(appointment);
  }
}

// 一键初始化所有测试数据
async function initTestData(event, context) {
  try {
    const db = uniCloud.database();

    // 创建测试用户
    const testUsers = [
      {
        _id: 'user_001',
        username: 'test_user_1',
        nickname: '张同学',
        avatar: '/static/default-avatar.png',
        mobile: '13800138001',
        email: '<EMAIL>',
        status: 0,
        register_date: new Date('2024-01-15'),
        last_login_date: new Date('2024-01-20'),
        create_date: new Date('2024-01-15'),
        update_date: new Date('2024-01-20')
      },
      {
        _id: 'user_002',
        username: 'test_user_2',
        nickname: '李老师',
        avatar: '/static/default-avatar.png',
        mobile: '13800138002',
        email: '<EMAIL>',
        status: 0,
        register_date: new Date('2024-01-16'),
        last_login_date: new Date('2024-01-21'),
        create_date: new Date('2024-01-16'),
        update_date: new Date('2024-01-21')
      },
      {
        _id: 'user_003',
        username: 'test_user_3',
        nickname: '王先生',
        avatar: '/static/default-avatar.png',
        mobile: '13800138003',
        email: '<EMAIL>',
        status: 0,
        register_date: new Date('2024-01-17'),
        last_login_date: new Date('2024-01-22'),
        create_date: new Date('2024-01-17'),
        update_date: new Date('2024-01-22')
      }
    ];

    // 清空并插入用户数据
    const userCollection = db.collection('uni-id-users');
    await userCollection.where({
      _id: db.command.in(['user_001', 'user_002', 'user_003'])
    }).remove();

    for (const user of testUsers) {
      await userCollection.add(user);
    }

    // 初始化房源和预约数据
    await initTestHouses();
    await initTestAppointments();

    return {
      code: 0,
      message: '所有测试数据初始化成功',
      data: {
        userCount: testUsers.length,
        houseCount: 3,
        appointmentCount: 3
      }
    };
  } catch (error) {
    console.error('初始化测试数据失败:', error);
    return {
      code: 500,
      message: '初始化测试数据失败',
      error: error.message
    };
  }
}

// 管理员获取用户列表
async function getUserListForAdmin(event, context) {
  try {
    const { page = 1, pageSize = 10, keyword = '', filters = {} } = event;
    const db = uniCloud.database();

    // 构建查询条件
    let query = db.collection('uni-id-users');

    // 搜索条件
    if (keyword) {
      query = query.where(db.command.or([
        {
          username: new RegExp(keyword, 'i')
        },
        {
          nickname: new RegExp(keyword, 'i')
        },
        {
          mobile: new RegExp(keyword, 'i')
        }
      ]));
    }

    // 状态筛选
    if (filters.status !== undefined && filters.status !== '') {
      query = query.where({
        status: filters.status
      });
    }

    // 日期范围筛选
    if (filters.dateRange && filters.dateRange.length === 2) {
      const startDate = new Date(filters.dateRange[0]);
      const endDate = new Date(filters.dateRange[1]);
      endDate.setHours(23, 59, 59, 999); // 设置为当天结束时间

      query = query.where({
        register_date: db.command.gte(startDate).and(db.command.lte(endDate))
      });
    }

    // 获取总数
    const countResult = await query.count();
    const total = countResult.result.total;

    // 分页查询
    const skip = (page - 1) * pageSize;
    const result = await query
      .skip(skip)
      .limit(pageSize)
      .orderBy('register_date', 'desc')
      .get();

    return {
      code: 0,
      message: '获取成功',
      data: {
        list: result.data || [],
        total: total,
        page: page,
        pageSize: pageSize
      }
    };
  } catch (error) {
    console.error('获取用户列表失败:', error);
    return {
      code: 500,
      message: '获取用户列表失败',
      error: error.message
    };
  }
}
