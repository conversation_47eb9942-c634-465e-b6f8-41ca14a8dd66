
.appointments-container.data-v-45ecc94f {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}
.status-tabs.data-v-45ecc94f {
  background: #fff;
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}
.tab-item.data-v-45ecc94f {
  flex: 1;
  padding: 30rpx 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  position: relative;
}
.tab-item.active.data-v-45ecc94f {
  color: #007aff;
}
.tab-item.active.data-v-45ecc94f::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #007aff;
  border-radius: 2rpx;
}
.tab-text.data-v-45ecc94f {
  font-size: 26rpx;
  color: #333;
}
.tab-item.active .tab-text.data-v-45ecc94f {
  color: #007aff;
  font-weight: 500;
}
.tab-count.data-v-45ecc94f {
  background: #ff4757;
  color: #fff;
  font-size: 18rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 28rpx;
  text-align: center;
}
.appointment-list.data-v-45ecc94f {
  flex: 1;
  padding: 20rpx;
}
.appointment-item.data-v-45ecc94f {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.appointment-header.data-v-45ecc94f {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.status-badge.data-v-45ecc94f {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #fff;
}
.status-badge.pending.data-v-45ecc94f {
  background: #ff9800;
}
.status-badge.confirmed.data-v-45ecc94f {
  background: #10c560;
}
.status-badge.completed.data-v-45ecc94f {
  background: #007aff;
}
.status-badge.cancelled.data-v-45ecc94f {
  background: #999;
}
.appointment-time.data-v-45ecc94f {
  font-size: 24rpx;
  color: #999;
}
.house-info.data-v-45ecc94f {
  display: flex;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}
.house-image.data-v-45ecc94f {
  width: 120rpx;
  height: 90rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
  flex-shrink: 0;
}
.house-image image.data-v-45ecc94f {
  width: 100%;
  height: 100%;
}
.house-details.data-v-45ecc94f {
  flex: 1;
}
.house-title.data-v-45ecc94f {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.house-tags.data-v-45ecc94f {
  display: flex;
  gap: 8rpx;
  margin-bottom: 8rpx;
}
.tag.data-v-45ecc94f {
  background: #e0e0e0;
  color: #666;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  font-size: 20rpx;
}
.house-location.data-v-45ecc94f {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.location-text.data-v-45ecc94f {
  font-size: 22rpx;
  color: #999;
  margin-left: 6rpx;
}
.house-price.data-v-45ecc94f {
  display: flex;
  align-items: baseline;
}
.price.data-v-45ecc94f {
  font-size: 26rpx;
  font-weight: bold;
  color: #ff6b6b;
}
.price-unit.data-v-45ecc94f {
  font-size: 18rpx;
  color: #999;
  margin-left: 4rpx;
}
.appointment-details.data-v-45ecc94f {
  margin-bottom: 20rpx;
}
.detail-row.data-v-45ecc94f {
  display: flex;
  margin-bottom: 10rpx;
}
.detail-label.data-v-45ecc94f {
  font-size: 26rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}
.detail-value.data-v-45ecc94f {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}
.appointment-actions.data-v-45ecc94f {
  display: flex;
  gap: 20rpx;
}
.action-btn.data-v-45ecc94f {
  flex: 1;
  padding: 15rpx 20rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  border: none;
}
.action-btn.cancel.data-v-45ecc94f {
  background: #ffebee;
  color: #f44336;
}
.action-btn.contact.data-v-45ecc94f {
  background: #e3f2fd;
  color: #007aff;
}
.action-btn.complete.data-v-45ecc94f {
  background: #e8f5e8;
  color: #10c560;
}
.load-status.data-v-45ecc94f {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}
.empty-state.data-v-45ecc94f {
  text-align: center;
  padding: 100rpx 40rpx;
}
.empty-state image.data-v-45ecc94f {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}
.empty-text.data-v-45ecc94f {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.empty-tip.data-v-45ecc94f {
  display: block;
  font-size: 24rpx;
  color: #ccc;
  margin-bottom: 40rpx;
}
.browse-btn.data-v-45ecc94f {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

