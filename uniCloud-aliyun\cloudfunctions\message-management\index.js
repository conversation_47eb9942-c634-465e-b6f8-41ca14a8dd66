'use strict';

const uniID = require('uni-id-common');

// 通用 checkToken 函数
async function checkUserToken(token, context) {
  const uniIdIns = uniID.createInstance({ context });
  const payload = await uniIdIns.checkToken(token);

  if (payload.errCode === 0) {
    return {
      code: 0,
      uid: payload.uid,
      userInfo: payload.userInfo
    };
  } else {
    return {
      code: payload.errCode || 401,
      message: payload.errMsg || '身份验证失败'
    };
  }
}

exports.main = async (event, context) => {
  const { action, data } = event;
  
  try {
    switch (action) {
      case 'sendMessage':
        return await sendMessage(event, context);
      case 'getMessageList':
        return await getMessageList(event, context);
      case 'markAsRead':
        return await markAsRead(event, context);
      case 'getUnreadCount':
        return await getUnreadCount(event, context);
      default:
        return {
          code: 400,
          message: '无效的操作'
        };
    }
  } catch (error) {
    console.error('消息管理云函数执行错误:', error);
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message
    };
  }
};

// 发送消息
async function sendMessage(event, context) {
  const payload = await checkUserToken(event.uniIdToken, context);
  if (payload.code !== 0) {
    return payload;
  }
  
  const { to_user_id, type, title, content, related_id } = event.data;
  
  if (!to_user_id || !type || !title || !content) {
    return {
      code: 400,
      message: '接收者、消息类型、标题和内容不能为空'
    };
  }
  
  const validTypes = ['system', 'appointment', 'contact'];
  if (!validTypes.includes(type)) {
    return {
      code: 400,
      message: '无效的消息类型'
    };
  }
  
  try {
    const db = uniCloud.database();
    
    // 检查接收者是否存在
    const userRes = await db.collection('uni-id-users').doc(to_user_id).get();
    if (userRes.data.length === 0) {
      return {
        code: 404,
        message: '接收者不存在'
      };
    }
    
    // 创建消息记录
    const message = {
      from_user_id: payload.uid,
      to_user_id: to_user_id,
      type: type,
      title: title,
      content: content,
      related_id: related_id || '',
      is_read: false,
      create_date: new Date()
    };
    
    const result = await db.collection('messages').add(message);
    
    return {
      code: 0,
      message: '发送成功',
      data: {
        message_id: result.id
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '发送失败',
      error: error.message
    };
  }
}

// 获取消息列表
async function getMessageList(event, context) {
  const payload = await checkUserToken(event.uniIdToken, context);
  if (payload.code !== 0) {
    return payload;
  }
  
  const { page = 1, pageSize = 10, type, is_read } = event.data;
  
  try {
    const db = uniCloud.database();
    
    let query = db.collection('messages').where({
      to_user_id: payload.uid
    });
    
    // 按类型筛选
    if (type) {
      query = query.where({
        type: type
      });
    }
    
    // 按已读状态筛选
    if (is_read !== undefined) {
      query = query.where({
        is_read: is_read
      });
    }
    
    const skip = (page - 1) * pageSize;
    const result = await query.skip(skip).limit(pageSize).orderBy('create_date', 'desc').get();
    
    // 获取发送者信息
    const fromUserIds = result.data.map(item => item.from_user_id).filter(id => id);
    let fromUsers = [];
    
    if (fromUserIds.length > 0) {
      const userRes = await db.collection('uni-id-users').where({
        _id: db.command.in(fromUserIds)
      }).field({
        nickname: true,
        avatar: true
      }).get();
      fromUsers = userRes.data;
    }
    
    // 组合数据
    const list = result.data.map(message => {
      const fromUser = fromUsers.find(u => u._id === message.from_user_id);
      return {
        ...message,
        from_user: fromUser || null
      };
    });
    
    // 获取总数
    const countResult = await query.count();
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        list: list,
        total: countResult.total,
        page: page,
        pageSize: pageSize
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取失败',
      error: error.message
    };
  }
}

// 标记消息为已读
async function markAsRead(event, context) {
  const payload = await checkUserToken(event.uniIdToken, context);
  if (payload.code !== 0) {
    return payload;
  }
  
  const { message_ids } = event.data;
  
  if (!message_ids || !Array.isArray(message_ids) || message_ids.length === 0) {
    return {
      code: 400,
      message: '消息ID列表不能为空'
    };
  }
  
  try {
    const db = uniCloud.database();
    
    // 批量更新消息状态
    await db.collection('messages').where({
      _id: db.command.in(message_ids),
      to_user_id: payload.uid
    }).update({
      is_read: true
    });
    
    return {
      code: 0,
      message: '标记成功'
    };
  } catch (error) {
    return {
      code: 500,
      message: '标记失败',
      error: error.message
    };
  }
}

// 获取未读消息数量
async function getUnreadCount(event, context) {
  const payload = await checkUserToken(event.uniIdToken, context);
  if (payload.code !== 0) {
    return payload;
  }
  
  try {
    const db = uniCloud.database();
    
    // 获取各类型未读消息数量
    const totalResult = await db.collection('messages').where({
      to_user_id: payload.uid,
      is_read: false
    }).count();
    
    const systemResult = await db.collection('messages').where({
      to_user_id: payload.uid,
      is_read: false,
      type: 'system'
    }).count();
    
    const appointmentResult = await db.collection('messages').where({
      to_user_id: payload.uid,
      is_read: false,
      type: 'appointment'
    }).count();
    
    const contactResult = await db.collection('messages').where({
      to_user_id: payload.uid,
      is_read: false,
      type: 'contact'
    }).count();
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        total: totalResult.total,
        system: systemResult.total,
        appointment: appointmentResult.total,
        contact: contactResult.total
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取失败',
      error: error.message
    };
  }
}
