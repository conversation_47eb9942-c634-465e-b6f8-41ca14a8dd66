{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端/pages/user/my-houses.vue?5c5d", "webpack:///D:/web/project/前端/pages/user/my-houses.vue?3e71", "webpack:///D:/web/project/前端/pages/user/my-houses.vue?b402", "webpack:///D:/web/project/前端/pages/user/my-houses.vue?c0a0", "uni-app:///pages/user/my-houses.vue", "webpack:///D:/web/project/前端/pages/user/my-houses.vue?2099", "webpack:///D:/web/project/前端/pages/user/my-houses.vue?7650"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "currentTab", "statusTabs", "label", "value", "count", "houseList", "loading", "refreshing", "noMore", "page", "pageSize", "methods", "formatTime", "getTypeText", "getStatusText", "switchTab", "loadHouseList", "refresh", "params", "action", "currentStatus", "request", "result", "console", "uni", "title", "icon", "loadStatistics", "promises", "countOnly", "showLoading", "showError", "status", "Promise", "results", "tab", "loadMore", "onRefresh", "editHouse", "url", "toggleStatus", "newStatus", "statusText", "content", "success", "res", "house_id", "house", "deleteHouse", "index", "toHouseDetail", "toPublish", "onLoad", "onShow", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACqC;;;AAG7F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChCA;AAAA;AAAA;AAAA;AAAooB,CAAgB,koBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC8FxpB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC,aACA;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IAEA;IACAC;MACA;QAAA;MAAA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAEA;kBACA;kBACA;gBACA;gBAAA;gBAGAC;kBACAC;kBACApB;oBACAU;oBACAC;kBACA;gBACA,GAEA;gBACAU;gBACA;kBACAF;gBACA;gBAAA;gBAAA,OAEAG;cAAA;gBAAAC;gBAEA;kBAAA,eACAA;kBAEA;oBACA;kBACA;oBACA;kBACA;kBAEA;kBACA;gBACA;kBACAC;kBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAH;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;gBACA;gBACAP;kBACAF;kBACApB;oBAAAU;oBAAAC;oBAAAmB;kBAAA;gBACA;kBAAAC;kBAAAC;gBAAA;gBACA;gBACAV;kBACAF;kBACApB;oBAAAU;oBAAAC;oBAAAsB;oBAAAH;kBAAA;gBACA;kBAAAC;kBAAAC;gBAAA;gBACA;gBACAV;kBACAF;kBACApB;oBAAAU;oBAAAC;oBAAAsB;oBAAAH;kBAAA;gBACA;kBAAAC;kBAAAC;gBAAA;gBACA;gBACAV;kBACAF;kBACApB;oBAAAU;oBAAAC;oBAAAsB;oBAAAH;kBAAA;gBACA;kBAAAC;kBAAAC;gBAAA,GACA;gBAAA;gBAAA,OAEAE;cAAA;gBAAAC;gBAEAA;kBACA;oBACA;kBACA;oBACAX;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBACA;kBACAY;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACAd;QACAe;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAC;gBAEAlB;kBACAC;kBACAkB;kBACAC;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAC;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA;8BAAA,OAEAxB;gCACAF;gCACApB;kCACA+C;kCACAd;gCACA;8BACA;4BAAA;8BANAV;8BAQA;gCACAE;kCACAC;kCACAC;gCACA;;gCAEA;gCACAqB;;gCAEA;gCACA;8BACA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAEAxB;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAGA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAyB;MAAA;MACAxB;QACAC;QACAkB;QACAC;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,KACAC;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA;oBAAA,OAEAxB;sBACAF;sBACApB;wBACA+C;sBACA;oBACA;kBAAA;oBALAxB;oBAOA;sBACAE;wBACAC;wBACAC;sBACA;;sBAEA;sBACAuB;wBAAA;sBAAA;sBACA;wBACA;sBACA;;sBAEA;sBACA;oBACA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAEA1B;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAGA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACA2B;MACA1B;QACAe;MACA;IACA;IAEA;IACAY;MACA3B;QACAe;MACA;IACA;EACA;EAEAa;IACA;IACA;EACA;EAEAC;IACA;IACA;IACA;EACA;EAEAC;IACA;IACA9B;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5WA;AAAA;AAAA;AAAA;AAA27B,CAAgB,q5BAAG,EAAC,C;;;;;;;;;;;ACA/8B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/my-houses.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/user/my-houses.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./my-houses.vue?vue&type=template&id=ddfcefee&scoped=true&\"\nvar renderjs\nimport script from \"./my-houses.vue?vue&type=script&lang=js&\"\nexport * from \"./my-houses.vue?vue&type=script&lang=js&\"\nimport style0 from \"./my-houses.vue?vue&type=style&index=0&id=ddfcefee&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ddfcefee\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/my-houses.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my-houses.vue?vue&type=template&id=ddfcefee&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.houseList, function (house, __i0__) {\n    var $orig = _vm.__get_orig(house)\n    var m0 = _vm.getStatusText(house.status)\n    var m1 = _vm.getTypeText(house.type)\n    var m2 = _vm.formatTime(house.publish_date)\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n      m2: m2,\n    }\n  })\n  var g0 = _vm.houseList.length\n  var g1 = !_vm.loading && _vm.houseList.length === 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my-houses.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my-houses.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"my-houses-container\">\n    <!-- 状态筛选 -->\n    <view class=\"status-tabs\">\n      <view\n        class=\"tab-item\"\n        v-for=\"(tab, index) in statusTabs\"\n        :key=\"index\"\n        :class=\"{ active: currentTab === index }\"\n        @click=\"switchTab(index)\"\n      >\n        <text class=\"tab-text\">{{ tab.label }}</text>\n        <text class=\"tab-count\" v-if=\"tab.count > 0\">{{ tab.count }}</text>\n      </view>\n    </view>\n    \n    <!-- 房源列表 -->\n    <scroll-view \n      class=\"house-list\" \n      scroll-y \n      @scrolltolower=\"loadMore\"\n      refresher-enabled\n      @refresherrefresh=\"onRefresh\"\n      :refresher-triggered=\"refreshing\"\n    >\n      <view class=\"house-item\" v-for=\"house in houseList\" :key=\"house._id\">\n        <view class=\"house-image\" @click=\"toHouseDetail(house._id)\">\n          <image \n            :src=\"house.images && house.images[0] || '/static/default-house.png'\" \n            mode=\"aspectFill\"\n          ></image>\n          <view class=\"house-status\" :class=\"house.status\">{{ getStatusText(house.status) }}</view>\n        </view>\n        <view class=\"house-info\" @click=\"toHouseDetail(house._id)\">\n          <text class=\"house-title\">{{ house.title }}</text>\n          <view class=\"house-tags\">\n            <text class=\"tag\">{{ getTypeText(house.type) }}</text>\n            <text class=\"tag\" v-if=\"house.room_count\">{{ house.room_count }}室</text>\n            <text class=\"tag\" v-if=\"house.hall_count\">{{ house.hall_count }}厅</text>\n            <text class=\"tag\" v-if=\"house.area\">{{ house.area }}㎡</text>\n          </view>\n          <view class=\"house-location\">\n            <uni-icons type=\"location\" size=\"12\" color=\"#999\"></uni-icons>\n            <text class=\"location-text\">{{ house.location.district }} {{ house.location.address }}</text>\n          </view>\n          <view class=\"house-bottom\">\n            <view class=\"price-info\">\n              <text class=\"price\">¥{{ house.price }}</text>\n              <text class=\"price-unit\">/月</text>\n            </view>\n            <view class=\"house-stats\">\n              <text class=\"stat-item\">\n                <uni-icons type=\"eye\" size=\"12\" color=\"#999\"></uni-icons>\n                {{ house.view_count || 0 }}\n              </text>\n              <text class=\"stat-item\">\n                <uni-icons type=\"heart\" size=\"12\" color=\"#999\"></uni-icons>\n                {{ house.favorite_count || 0 }}\n              </text>\n            </view>\n          </view>\n          <text class=\"publish-time\">发布时间：{{ formatTime(house.publish_date) }}</text>\n        </view>\n        <view class=\"house-actions\">\n          <button class=\"action-btn edit\" @click=\"editHouse(house._id)\" v-if=\"house.status !== 'rented'\">编辑</button>\n          <button class=\"action-btn status\" @click=\"toggleStatus(house)\" v-if=\"house.status !== 'rented'\">\n            {{ house.status === 'available' ? '下线' : '上线' }}\n          </button>\n          <button class=\"action-btn delete\" @click=\"deleteHouse(house._id)\">删除</button>\n        </view>\n      </view>\n      \n      <!-- 加载状态 -->\n      <view class=\"load-status\" v-if=\"houseList.length > 0\">\n        <text v-if=\"loading\">加载中...</text>\n        <text v-else-if=\"noMore\">没有更多了</text>\n      </view>\n      \n      <!-- 空状态 -->\n      <view class=\"empty-state\" v-if=\"!loading && houseList.length === 0\">\n        <image src=\"/static/empty-house.png\" mode=\"aspectFit\"></image>\n        <text class=\"empty-text\">暂无房源发布</text>\n        <button class=\"publish-btn\" @click=\"toPublish\">立即发布</button>\n      </view>\n    </scroll-view>\n    \n    <!-- 发布按钮 -->\n    <view class=\"floating-btn\" @click=\"toPublish\">\n      <uni-icons type=\"plus\" size=\"24\" color=\"#fff\"></uni-icons>\n    </view>\n  </view>\n</template>\n\n<script>\nimport request from '@/utils/request.js'\nimport { formatTime } from '@/utils/common.js'\nimport { HOUSE_TYPES, HOUSE_STATUS } from '@/common/config.js'\n\nexport default {\n  data() {\n    return {\n      currentTab: 0,\n      statusTabs: [\n        { label: '全部', value: '', count: 0 },\n        { label: '可租', value: 'available', count: 0 },\n        { label: '已租', value: 'rented', count: 0 },\n        { label: '下线', value: 'offline', count: 0 }\n      ],\n      houseList: [],\n      loading: false,\n      refreshing: false,\n      noMore: false,\n      page: 1,\n      pageSize: 10\n    }\n  },\n  methods: {\n    formatTime,\n    \n    // 获取房源类型文本\n    getTypeText(type) {\n      const typeItem = HOUSE_TYPES.find(item => item.value === type)\n      return typeItem ? typeItem.label : type\n    },\n    \n    // 获取状态文本\n    getStatusText(status) {\n      return HOUSE_STATUS[status] || status\n    },\n    \n    // 切换标签\n    switchTab(index) {\n      this.currentTab = index\n      this.loadHouseList(true)\n    },\n    \n    // 加载房源列表\n    async loadHouseList(refresh = false) {\n      if (this.loading) return\n      \n      this.loading = true\n      \n      if (refresh) {\n        this.page = 1\n        this.noMore = false\n      }\n      \n      try {\n        const params = {\n          action: 'getMyHouses',\n          data: {\n            page: this.page,\n            pageSize: this.pageSize\n          }\n        }\n        \n        // 添加状态筛选\n        const currentStatus = this.statusTabs[this.currentTab].value\n        if (currentStatus) {\n          params.data.status = currentStatus\n        }\n        \n        const result = await request.callFunction('house-management', params)\n\n        if (result.code === 0) {\n          const { list, total } = result.data\n\n          if (refresh) {\n            this.houseList = list\n          } else {\n            this.houseList.push(...list)\n          }\n\n          this.page++\n          this.noMore = this.houseList.length >= total\n        } else {\n          console.error('获取房源列表失败:', result)\n          uni.showToast({\n            title: result.message || '获取房源列表失败',\n            icon: 'none'\n          })\n        }\n      } catch (error) {\n        console.error('加载房源列表失败:', error)\n      } finally {\n        this.loading = false\n        this.refreshing = false\n      }\n    },\n    \n    // 加载统计数据\n    async loadStatistics() {\n      try {\n        // 并发请求各状态的房源数量，禁用单个请求的loading\n        const promises = [\n          // 全部房源\n          request.callFunction('house-management', {\n            action: 'getMyHouses',\n            data: { page: 1, pageSize: 1, countOnly: true }\n          }, { showLoading: false, showError: false }),\n          // 可租房源\n          request.callFunction('house-management', {\n            action: 'getMyHouses',\n            data: { page: 1, pageSize: 1, status: 'available', countOnly: true }\n          }, { showLoading: false, showError: false }),\n          // 已租房源\n          request.callFunction('house-management', {\n            action: 'getMyHouses',\n            data: { page: 1, pageSize: 1, status: 'rented', countOnly: true }\n          }, { showLoading: false, showError: false }),\n          // 下线房源\n          request.callFunction('house-management', {\n            action: 'getMyHouses',\n            data: { page: 1, pageSize: 1, status: 'offline', countOnly: true }\n          }, { showLoading: false, showError: false })\n        ]\n\n        const results = await Promise.allSettled(promises)\n\n        results.forEach((result, index) => {\n          if (result.status === 'fulfilled' && result.value.code === 0) {\n            this.statusTabs[index].count = result.value.data.total || 0\n          } else {\n            console.error(`获取状态 ${this.statusTabs[index].label} 数量失败:`, result.reason || result.value)\n            this.statusTabs[index].count = 0\n          }\n        })\n      } catch (error) {\n        console.error('加载统计数据失败:', error)\n        // 发生错误时重置为0\n        this.statusTabs.forEach(tab => {\n          tab.count = 0\n        })\n      }\n    },\n    \n    // 加载更多\n    loadMore() {\n      if (!this.noMore && !this.loading) {\n        this.loadHouseList()\n      }\n    },\n    \n    // 下拉刷新\n    onRefresh() {\n      this.refreshing = true\n      this.loadHouseList(true)\n      this.loadStatistics()\n    },\n    \n    // 编辑房源\n    editHouse(houseId) {\n      uni.navigateTo({\n        url: `/pages/house/edit?id=${houseId}`\n      })\n    },\n    \n    // 切换房源状态\n    async toggleStatus(house) {\n      const newStatus = house.status === 'available' ? 'offline' : 'available'\n      const statusText = newStatus === 'available' ? '上线' : '下线'\n      \n      uni.showModal({\n        title: '提示',\n        content: `确定要${statusText}此房源吗？`,\n        success: async (res) => {\n          if (res.confirm) {\n            try {\n              const result = await request.callFunction('house-management', {\n                action: 'updateHouseStatus',\n                data: {\n                  house_id: house._id,\n                  status: newStatus\n                }\n              })\n              \n              if (result.code === 0) {\n                uni.showToast({\n                  title: `${statusText}成功`,\n                  icon: 'success'\n                })\n                \n                // 更新本地数据\n                house.status = newStatus\n                \n                // 重新加载统计数据\n                this.loadStatistics()\n              }\n            } catch (error) {\n              console.error(`${statusText}失败:`, error)\n            }\n          }\n        }\n      })\n    },\n    \n    // 删除房源\n    deleteHouse(houseId) {\n      uni.showModal({\n        title: '提示',\n        content: '确定要删除此房源吗？删除后无法恢复。',\n        success: async (res) => {\n          if (res.confirm) {\n            try {\n              const result = await request.callFunction('house-management', {\n                action: 'deleteHouse',\n                data: {\n                  house_id: houseId\n                }\n              })\n              \n              if (result.code === 0) {\n                uni.showToast({\n                  title: '删除成功',\n                  icon: 'success'\n                })\n                \n                // 从列表中移除\n                const index = this.houseList.findIndex(item => item._id === houseId)\n                if (index > -1) {\n                  this.houseList.splice(index, 1)\n                }\n                \n                // 重新加载统计数据\n                this.loadStatistics()\n              }\n            } catch (error) {\n              console.error('删除失败:', error)\n            }\n          }\n        }\n      })\n    },\n    \n    // 跳转到房源详情\n    toHouseDetail(houseId) {\n      uni.navigateTo({\n        url: `/pages/house/detail?id=${houseId}`\n      })\n    },\n    \n    // 跳转到发布页\n    toPublish() {\n      uni.navigateTo({\n        url: '/pages/house/publish'\n      })\n    }\n  },\n  \n  onLoad() {\n    this.loadHouseList(true)\n    this.loadStatistics()\n  },\n\n  onShow() {\n    // 从编辑页返回时刷新列表\n    this.loadHouseList(true)\n    this.loadStatistics()\n  },\n  \n  onPullDownRefresh() {\n    this.onRefresh()\n    uni.stopPullDownRefresh()\n  }\n}\n</script>\n\n<style scoped>\n.my-houses-container {\n  height: 100vh;\n  background: #f8f9fa;\n  display: flex;\n  flex-direction: column;\n}\n\n.status-tabs {\n  background: #fff;\n  display: flex;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.tab-item {\n  flex: 1;\n  padding: 30rpx 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 10rpx;\n  position: relative;\n}\n\n.tab-item.active {\n  color: #007aff;\n}\n\n.tab-item.active::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 60rpx;\n  height: 4rpx;\n  background: #007aff;\n  border-radius: 2rpx;\n}\n\n.tab-text {\n  font-size: 28rpx;\n  color: #333;\n}\n\n.tab-item.active .tab-text {\n  color: #007aff;\n  font-weight: 500;\n}\n\n.tab-count {\n  background: #ff4757;\n  color: #fff;\n  font-size: 20rpx;\n  padding: 4rpx 8rpx;\n  border-radius: 10rpx;\n  min-width: 32rpx;\n  text-align: center;\n}\n\n.house-list {\n  flex: 1;\n  padding: 20rpx;\n}\n\n.house-item {\n  background: #fff;\n  border-radius: 20rpx;\n  margin-bottom: 20rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n  display: flex;\n}\n\n.house-image {\n  position: relative;\n  width: 240rpx;\n  height: 200rpx;\n  flex-shrink: 0;\n}\n\n.house-image image {\n  width: 100%;\n  height: 100%;\n}\n\n.house-status {\n  position: absolute;\n  top: 15rpx;\n  left: 15rpx;\n  padding: 6rpx 12rpx;\n  border-radius: 12rpx;\n  font-size: 22rpx;\n  color: #fff;\n}\n\n.house-status.available {\n  background: #10c560;\n}\n\n.house-status.rented {\n  background: #ff6b6b;\n}\n\n.house-status.offline {\n  background: #999;\n}\n\n.house-info {\n  flex: 1;\n  padding: 20rpx;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  min-width: 0; /* 防止flex子项溢出 */\n}\n\n.house-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 10rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.house-tags {\n  display: flex;\n  gap: 8rpx;\n  margin-bottom: 10rpx;\n  flex-wrap: wrap;\n}\n\n.tag {\n  background: #f0f0f0;\n  color: #666;\n  padding: 4rpx 8rpx;\n  border-radius: 6rpx;\n  font-size: 22rpx;\n}\n\n.house-location {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10rpx;\n  min-width: 0; /* 防止溢出 */\n}\n\n.location-text {\n  font-size: 24rpx;\n  color: #999;\n  margin-left: 6rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  flex: 1; /* 占用剩余空间 */\n  min-width: 0; /* 防止溢出 */\n}\n\n.house-bottom {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 10rpx;\n}\n\n.price-info {\n  display: flex;\n  align-items: baseline;\n}\n\n.price {\n  font-size: 30rpx;\n  font-weight: bold;\n  color: #ff6b6b;\n}\n\n.price-unit {\n  font-size: 20rpx;\n  color: #999;\n  margin-left: 4rpx;\n}\n\n.house-stats {\n  display: flex;\n  gap: 15rpx;\n}\n\n.stat-item {\n  font-size: 20rpx;\n  color: #999;\n  display: flex;\n  align-items: center;\n  gap: 4rpx;\n}\n\n.publish-time {\n  font-size: 22rpx;\n  color: #ccc;\n}\n\n.house-actions {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  padding: 20rpx;\n  gap: 15rpx;\n  border-left: 1rpx solid #f0f0f0;\n  width: 160rpx; /* 固定宽度，确保按钮可见 */\n  flex-shrink: 0; /* 防止被压缩 */\n}\n\n.action-btn {\n  padding: 16rpx 24rpx;\n  border-radius: 20rpx;\n  font-size: 24rpx;\n  border: none;\n  width: 100%; /* 占满容器宽度 */\n  text-align: center;\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n.action-btn:active {\n  transform: scale(0.95);\n}\n\n.action-btn.edit {\n  background: #e3f2fd;\n  color: #007aff;\n  border: 1rpx solid #007aff;\n}\n\n.action-btn.status {\n  background: #fff3e0;\n  color: #ff9800;\n  border: 1rpx solid #ff9800;\n}\n\n.action-btn.delete {\n  background: #ffebee;\n  color: #f44336;\n  border: 1rpx solid #f44336;\n}\n\n.load-status {\n  text-align: center;\n  padding: 40rpx;\n  color: #999;\n  font-size: 28rpx;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 100rpx 40rpx;\n}\n\n.empty-state image {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 40rpx;\n}\n\n.empty-text {\n  display: block;\n  font-size: 28rpx;\n  color: #999;\n  margin-bottom: 40rpx;\n}\n\n.publish-btn {\n  background: #007aff;\n  color: #fff;\n  border: none;\n  border-radius: 25rpx;\n  padding: 20rpx 40rpx;\n  font-size: 28rpx;\n}\n\n.floating-btn {\n  position: fixed;\n  right: 40rpx;\n  bottom: 100rpx;\n  width: 100rpx;\n  height: 100rpx;\n  background: linear-gradient(45deg, #007aff, #0056d3);\n  border-radius: 50rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 8rpx 30rpx rgba(0, 122, 255, 0.3);\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my-houses.vue?vue&type=style&index=0&id=ddfcefee&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my-houses.vue?vue&type=style&index=0&id=ddfcefee&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751999949\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}