'use strict';

const uniID = require('uni-id-common');

// 通用 checkToken 函数
async function checkUserToken(token, context) {
  const uniIdIns = uniID.createInstance({ context });
  const payload = await uniIdIns.checkToken(token);

  if (payload.errCode === 0) {
    return {
      code: 0,
      uid: payload.uid,
      userInfo: payload.userInfo
    };
  } else {
    return {
      code: payload.errCode || 401,
      message: payload.errMsg || '身份验证失败'
    };
  }
}

exports.main = async (event, context) => {
  const { action, data } = event;
  
  try {
    switch (action) {
      case 'uploadImage':
        return await uploadImage(event, context);
      case 'deleteImage':
        return await deleteImage(event, context);
      case 'getUploadToken':
        return await getUploadToken(event, context);
      default:
        return {
          code: 400,
          message: '无效的操作'
        };
    }
  } catch (error) {
    console.error('文件上传云函数执行错误:', error);
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message
    };
  }
};

// 上传图片
async function uploadImage(event, context) {
  const payload = await checkUserToken(event.uniIdToken, context);
  if (payload.code !== 0) {
    return payload;
  }
  
  const { file_data, file_name, file_type, folder = 'images' } = event.data;
  
  if (!file_data || !file_name) {
    return {
      code: 400,
      message: '文件数据和文件名不能为空'
    };
  }
  
  // 验证文件类型
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  if (file_type && !allowedTypes.includes(file_type)) {
    return {
      code: 400,
      message: '不支持的文件类型'
    };
  }
  
  // 验证文件大小（base64解码后的大小）
  const fileSize = Buffer.byteLength(file_data, 'base64');
  const maxSize = 5 * 1024 * 1024; // 5MB
  if (fileSize > maxSize) {
    return {
      code: 400,
      message: '文件大小不能超过5MB'
    };
  }
  
  try {
    // 生成唯一文件名
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2);
    const fileExtension = file_name.split('.').pop();
    const uniqueFileName = `${timestamp}_${randomStr}.${fileExtension}`;
    const filePath = `${folder}/${payload.uid}/${uniqueFileName}`;
    
    // 上传到云存储
    const uploadResult = await uniCloud.uploadFile({
      cloudPath: filePath,
      fileContent: Buffer.from(file_data, 'base64')
    });
    
    if (uploadResult.fileID) {
      return {
        code: 0,
        message: '上传成功',
        data: {
          file_id: uploadResult.fileID,
          file_url: uploadResult.fileID,
          file_name: uniqueFileName,
          file_path: filePath
        }
      };
    } else {
      return {
        code: 500,
        message: '上传失败'
      };
    }
  } catch (error) {
    return {
      code: 500,
      message: '上传失败',
      error: error.message
    };
  }
}

// 删除图片
async function deleteImage(event, context) {
  const payload = await checkUserToken(event.uniIdToken, context);
  if (payload.code !== 0) {
    return payload;
  }
  
  const { file_id } = event.data;
  
  if (!file_id) {
    return {
      code: 400,
      message: '文件ID不能为空'
    };
  }
  
  try {
    // 从云存储删除文件
    const deleteResult = await uniCloud.deleteFile({
      fileList: [file_id]
    });
    
    if (deleteResult.fileList && deleteResult.fileList[0] && deleteResult.fileList[0].code === 'SUCCESS') {
      return {
        code: 0,
        message: '删除成功'
      };
    } else {
      return {
        code: 500,
        message: '删除失败'
      };
    }
  } catch (error) {
    return {
      code: 500,
      message: '删除失败',
      error: error.message
    };
  }
}

// 获取上传凭证
async function getUploadToken(event, context) {
  const payload = await checkUserToken(event.uniIdToken, context);
  if (payload.code !== 0) {
    return payload;
  }
  
  try {
    // 生成临时上传凭证
    const timestamp = Date.now();
    const token = `${payload.uid}_${timestamp}_${Math.random().toString(36).substring(2)}`;
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        upload_token: token,
        expire_time: timestamp + 3600000, // 1小时后过期
        max_file_size: 5 * 1024 * 1024, // 5MB
        allowed_types: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取失败',
      error: error.message
    };
  }
}
