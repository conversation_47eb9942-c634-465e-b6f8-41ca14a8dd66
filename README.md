# 毕业租房信息平台

基于 UniApp + Vue2 + uniCloud 开发的微信小程序毕业租房信息平台

## 项目简介

这是一个专为毕业生打造的租房信息平台，提供房源发布、搜索、收藏、预约看房等功能。采用前后端分离架构，前端使用 UniApp 开发，后端使用 uniCloud 云开发。

## 技术栈

### 前端
- **框架**: UniApp (基于 Vue2)
- **开发工具**: HBuilderX
- **UI组件**: uni-ui
- **状态管理**: Vuex (可选)
- **网络请求**: uniCloud.callFunction

### 后端
- **云服务**: uniCloud (阿里云版本)
- **数据库**: 云数据库 (MongoDB)
- **云函数**: Node.js
- **云存储**: 云存储服务
- **用户认证**: uni-id-common

## 项目结构

```
前端/
├── App.vue                     # 应用主组件
├── main.js                     # 应用入口文件
├── manifest.json               # 应用配置文件
├── pages.json                  # 页面路由配置
├── pages/                      # 页面目录
│   ├── index/                  # 首页
│   ├── login/                  # 登录页
│   ├── register/               # 注册页
│   ├── house/                  # 房源相关页面
│   │   ├── list.vue           # 房源列表
│   │   ├── detail.vue         # 房源详情
│   │   ├── publish.vue        # 发布房源
│   │   └── edit.vue           # 编辑房源
│   ├── search/                 # 搜索页面
│   └── user/                   # 用户中心
│       ├── profile.vue        # 个人中心
│       ├── my-houses.vue      # 我的发布
│       ├── favorites.vue      # 我的收藏
│       ├── appointments.vue   # 看房记录
│       └── messages.vue       # 消息通知
├── components/                 # 组件目录
│   ├── house-card/            # 房源卡片组件
│   ├── loading/               # 加载组件
│   └── empty-state/           # 空状态组件
├── static/                     # 静态资源
├── utils/                      # 工具函数
│   ├── request.js             # 网络请求工具
│   └── common.js              # 通用工具函数
├── common/                     # 公共配置
│   └── config.js              # 应用配置
├── uni_modules/               # uni插件模块
└── uniCloud-aliyun/           # uniCloud配置
    ├── cloudfunctions/        # 云函数目录
    │   ├── user-auth/         # 用户认证
    │   ├── house-management/  # 房源管理
    │   ├── favorite-management/ # 收藏管理
    │   ├── appointment-management/ # 预约管理
    │   ├── message-management/ # 消息管理
    │   ├── file-upload/       # 文件上传
    │   └── system-management/ # 系统管理
    └── database/              # 数据库配置
```

## 功能特性

### 用户功能
- ✅ 用户注册/登录
- ✅ 个人信息管理
- ✅ 身份认证（学生/房东）
- ✅ 房源浏览与搜索
- ✅ 房源收藏
- ✅ 预约看房
- ✅ 消息通知

### 房东功能
- ✅ 房源发布
- ✅ 房源管理（编辑/删除/上下线）
- ✅ 预约管理
- ✅ 房源数据统计

### 管理员功能
- ✅ 用户管理
- ✅ 房源审核
- ✅ 数据统计
- ✅ 举报处理
- ✅ 系统配置

## 数据库设计

### 主要数据表
- `uni-id-users`: 用户表（扩展字段）
- `houses`: 房源表
- `favorites`: 收藏表
- `appointments`: 预约表
- `messages`: 消息表
- `system_config`: 系统配置表
- `reports`: 举报表

详细的数据库设计请参考 `docs/database-design.md`

## 云函数设计

### 云函数列表
- `user-auth`: 用户认证相关
- `house-management`: 房源管理
- `favorite-management`: 收藏管理
- `appointment-management`: 预约管理
- `message-management`: 消息管理
- `file-upload`: 文件上传
- `system-management`: 系统管理

详细的云函数设计请参考 `docs/cloudfunctions-design.md`

## 开发环境搭建

### 前置要求
- HBuilderX 3.0+
- 微信开发者工具
- uniCloud 账号

### 安装步骤

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd 前端
   ```

2. **配置 uniCloud**
   - 在 HBuilderX 中打开项目
   - 右键 `uniCloud-aliyun` 目录
   - 选择"关联云服务空间"
   - 创建或选择已有的云服务空间

3. **上传云函数**
   - 右键各个云函数目录
   - 选择"上传并运行"

4. **初始化数据库**
   - 在 uniCloud 控制台创建数据库集合
   - 导入初始数据（可选）

5. **配置小程序**
   - 在微信公众平台注册小程序
   - 配置服务器域名
   - 在 `manifest.json` 中配置 AppID

6. **运行项目**
   - 在 HBuilderX 中运行到微信开发者工具
   - 或运行到浏览器进行调试

## 部署说明

### 云函数部署
1. 在 HBuilderX 中右键云函数目录
2. 选择"上传并运行"
3. 等待部署完成

### 小程序发布
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 在微信公众平台提交审核
4. 审核通过后发布

## 注意事项

1. **权限配置**: 确保云函数和数据库权限配置正确
2. **域名配置**: 小程序需要配置合法域名
3. **图片上传**: 需要配置云存储域名
4. **地图功能**: 需要申请地图服务密钥
5. **支付功能**: 如需支付功能，需要配置微信支付

## 开发规范

### 代码规范
- 使用 ES6+ 语法
- 组件名使用 PascalCase
- 文件名使用 kebab-case
- 变量名使用 camelCase

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 常见问题

### Q: 云函数调用失败
A: 检查云函数是否正确上传，参数是否正确，权限是否配置

### Q: 图片上传失败
A: 检查云存储配置，文件大小是否超限，格式是否支持

### Q: 小程序无法预览
A: 检查 AppID 配置，域名是否在白名单中

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础的房源发布和浏览功能
- 实现用户注册登录
- 实现收藏和预约功能

## 联系方式

如有问题或建议，请联系开发团队。

## 许可证

本项目采用 MIT 许可证，详情请参考 LICENSE 文件。
