<template>
  <view class="house-list">
    <!-- 搜索和筛选 -->
    <view class="search-section">
      <view class="search-row">
        <view class="search-item">
          <uni-easyinput 
            v-model="searchKeyword" 
            placeholder="搜索房源标题、地址、发布者"
            @confirm="onSearch"
            clearable
          >
            <template #right>
              <uni-icons type="search" size="18" color="#999" @click="onSearch" />
            </template>
          </uni-easyinput>
        </view>
        
        <view class="filter-item">
          <uni-data-picker 
            v-model="filters.status" 
            :localdata="statusOptions"
            placeholder="房源状态"
            @change="onFilterChange"
          />
        </view>
        
        <view class="filter-item">
          <uni-data-picker 
            v-model="filters.type" 
            :localdata="typeOptions"
            placeholder="房源类型"
            @change="onFilterChange"
          />
        </view>
        
        <view class="filter-item">
          <uni-data-picker 
            v-model="filters.verified" 
            :localdata="verifiedOptions"
            placeholder="审核状态"
            @change="onFilterChange"
          />
        </view>
        
        <button class="reset-btn" @click="resetFilters">重置</button>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-bar">
      <view class="stat-item">
        <text class="stat-label">总房源</text>
        <text class="stat-value">{{ statistics.total }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">已审核</text>
        <text class="stat-value">{{ statistics.verified }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">待审核</text>
        <text class="stat-value">{{ statistics.pending }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">已下线</text>
        <text class="stat-value">{{ statistics.offline }}</text>
      </view>
    </view>

    <!-- 房源列表 -->
    <view class="list-section">
      <uni-table 
        :loading="loading"
        border
        stripe
        emptyText="暂无房源数据"
      >
        <uni-tr>
          <uni-th width="80" align="center">
            <checkbox @change="onSelectAll" :checked="isAllSelected" />
          </uni-th>
          <uni-th width="120" align="center">封面图</uni-th>
          <uni-th width="200" align="center">房源信息</uni-th>
          <uni-th width="120" align="center">价格</uni-th>
          <uni-th width="100" align="center">类型</uni-th>
          <uni-th width="120" align="center">发布者</uni-th>
          <uni-th width="100" align="center">状态</uni-th>
          <uni-th width="100" align="center">审核状态</uni-th>
          <uni-th width="120" align="center">发布时间</uni-th>
          <uni-th width="200" align="center">操作</uni-th>
        </uni-tr>
        
        <uni-tr v-for="(house, index) in houseList" :key="house._id">
          <uni-td align="center">
            <checkbox 
              :checked="selectedIds.includes(house._id)"
              @change="onSelectItem(house._id)"
            />
          </uni-td>
          
          <uni-td align="center">
            <image 
              class="house-cover" 
              :src="house.images && house.images[0] || '/static/placeholder.png'"
              mode="aspectFill"
              @click="previewImage(house.images)"
            />
          </uni-td>
          
          <uni-td>
            <view class="house-info">
              <text class="house-title">{{ house.title }}</text>
              <text class="house-desc">{{ house.description }}</text>
              <text class="house-address">{{ house.location?.address }}</text>
            </view>
          </uni-td>
          
          <uni-td align="center">
            <view class="price-info">
              <text class="price">¥{{ house.price }}</text>
              <text class="price-unit">/月</text>
            </view>
          </uni-td>
          
          <uni-td align="center">
            <uni-tag :text="getTypeText(house.type)" type="primary" />
          </uni-td>
          
          <uni-td align="center">
            <view class="publisher-info" @click="viewPublisher(house.publisher_id)">
              <text class="publisher-name">{{ house.publisher_info?.nickname || '未知' }}</text>
            </view>
          </uni-td>
          
          <uni-td align="center">
            <uni-tag 
              :text="getStatusText(house.status)" 
              :type="getStatusType(house.status)"
            />
          </uni-td>
          
          <uni-td align="center">
            <uni-tag 
              :text="house.is_verified ? '已审核' : '待审核'" 
              :type="house.is_verified ? 'success' : 'warning'"
            />
          </uni-td>
          
          <uni-td align="center">
            <text class="date-text">{{ formatDate(house.publish_date) }}</text>
          </uni-td>
          
          <uni-td align="center">
            <view class="action-buttons">
              <button 
                class="action-btn view-btn" 
                @click="viewHouse(house)"
              >
                查看
              </button>
              <button 
                class="action-btn edit-btn" 
                @click="editHouse(house)"
              >
                编辑
              </button>
              <button 
                v-if="!house.is_verified"
                class="action-btn approve-btn" 
                @click="approveHouse(house)"
              >
                审核
              </button>
              <button 
                class="action-btn delete-btn" 
                @click="deleteHouse(house)"
              >
                删除
              </button>
            </view>
          </uni-td>
        </uni-tr>
      </uni-table>
      
      <!-- 批量操作 -->
      <view class="batch-actions" v-if="selectedIds.length > 0">
        <text class="selected-count">已选择 {{ selectedIds.length }} 项</text>
        <button class="batch-btn approve-btn" @click="batchApprove">批量审核</button>
        <button class="batch-btn offline-btn" @click="batchOffline">批量下线</button>
        <button class="batch-btn delete-btn" @click="batchDelete">批量删除</button>
      </view>
      
      <!-- 分页 -->
      <view class="pagination">
        <uni-pagination 
          :current="currentPage"
          :total="totalCount"
          :pageSize="pageSize"
          @change="onPageChange"
        />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'HouseList',
  data() {
    return {
      loading: false,
      searchKeyword: '',
      
      // 筛选条件
      filters: {
        status: '',
        type: '',
        verified: ''
      },
      
      // 筛选选项
      statusOptions: [
        { value: '', text: '全部状态' },
        { value: 'available', text: '可租' },
        { value: 'rented', text: '已租' },
        { value: 'offline', text: '下线' }
      ],
      
      typeOptions: [
        { value: '', text: '全部类型' },
        { value: '整租', text: '整租' },
        { value: '合租', text: '合租' },
        { value: '单间', text: '单间' }
      ],
      
      verifiedOptions: [
        { value: '', text: '全部' },
        { value: 'true', text: '已审核' },
        { value: 'false', text: '待审核' }
      ],
      
      // 统计数据
      statistics: {
        total: 0,
        verified: 0,
        pending: 0,
        offline: 0
      },
      
      // 列表数据
      houseList: [],
      selectedIds: [],
      isAllSelected: false,
      
      // 分页
      currentPage: 1,
      pageSize: 10,
      totalCount: 0
    }
  },

  onLoad() {
    this.loadHouseList()
    this.loadStatistics()
  },

  methods: {
    // 加载房源列表
    async loadHouseList() {
      try {
        this.loading = true
        
        const result = await uniCloud.callFunction({
          name: 'house-management',
          data: {
            action: 'getHouseListForAdmin',
            page: this.currentPage,
            pageSize: this.pageSize,
            keyword: this.searchKeyword,
            filters: this.filters
          }
        })

        if (result.result.code === 0) {
          this.houseList = result.result.data.list
          this.totalCount = result.result.data.total
        } else {
          uni.showToast({
            title: result.result.message,
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('加载房源列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 加载统计数据
    async loadStatistics() {
      try {
        const result = await uniCloud.callFunction({
          name: 'system-management',
          data: {
            action: 'getHouseStatistics'
          }
        })

        if (result.result.code === 0) {
          this.statistics = result.result.data
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    // 搜索
    onSearch() {
      this.currentPage = 1
      this.loadHouseList()
    },

    // 筛选改变
    onFilterChange() {
      this.currentPage = 1
      this.loadHouseList()
    },

    // 重置筛选
    resetFilters() {
      this.searchKeyword = ''
      this.filters = {
        status: '',
        type: '',
        verified: ''
      }
      this.currentPage = 1
      this.loadHouseList()
    },

    // 全选/取消全选
    onSelectAll(e) {
      this.isAllSelected = e.detail.value
      if (this.isAllSelected) {
        this.selectedIds = this.houseList.map(item => item._id)
      } else {
        this.selectedIds = []
      }
    },

    // 选择单项
    onSelectItem(id) {
      const index = this.selectedIds.indexOf(id)
      if (index > -1) {
        this.selectedIds.splice(index, 1)
      } else {
        this.selectedIds.push(id)
      }
      
      this.isAllSelected = this.selectedIds.length === this.houseList.length
    },

    // 查看房源
    viewHouse(house) {
      uni.navigateTo({
        url: `/pages/rental/house/detail?id=${house._id}`
      })
    },

    // 编辑房源
    editHouse(house) {
      uni.showToast({
        title: '编辑功能开发中',
        icon: 'none'
      })
    },

    // 审核房源
    async approveHouse(house) {
      try {
        const result = await uniCloud.callFunction({
          name: 'house-management',
          data: {
            action: 'approveHouse',
            house_id: house._id
          }
        })

        if (result.result.code === 0) {
          uni.showToast({
            title: '审核成功',
            icon: 'success'
          })
          this.loadHouseList()
          this.loadStatistics()
        } else {
          uni.showToast({
            title: result.result.message,
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('审核房源失败:', error)
        uni.showToast({
          title: '审核失败',
          icon: 'none'
        })
      }
    },

    // 删除房源
    deleteHouse(house) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除房源"${house.title}"吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await uniCloud.callFunction({
                name: 'house-management',
                data: {
                  action: 'deleteHouse',
                  house_id: house._id
                }
              })

              if (result.result.code === 0) {
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                })
                this.loadHouseList()
                this.loadStatistics()
              } else {
                uni.showToast({
                  title: result.result.message,
                  icon: 'none'
                })
              }
            } catch (error) {
              console.error('删除房源失败:', error)
              uni.showToast({
                title: '删除失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    // 查看发布者
    viewPublisher(publisherId) {
      uni.navigateTo({
        url: `/pages/rental/user/detail?id=${publisherId}`
      })
    },

    // 预览图片
    previewImage(images) {
      if (images && images.length > 0) {
        uni.previewImage({
          urls: images,
          current: 0
        })
      }
    },

    // 批量审核
    batchApprove() {
      uni.showModal({
        title: '批量审核',
        content: `确定要审核选中的 ${this.selectedIds.length} 个房源吗？`,
        success: async (res) => {
          if (res.confirm) {
            // 实现批量审核逻辑
            uni.showToast({
              title: '批量审核功能开发中',
              icon: 'none'
            })
          }
        }
      })
    },

    // 批量下线
    batchOffline() {
      uni.showModal({
        title: '批量下线',
        content: `确定要下线选中的 ${this.selectedIds.length} 个房源吗？`,
        success: async (res) => {
          if (res.confirm) {
            // 实现批量下线逻辑
            uni.showToast({
              title: '批量下线功能开发中',
              icon: 'none'
            })
          }
        }
      })
    },

    // 批量删除
    batchDelete() {
      uni.showModal({
        title: '批量删除',
        content: `确定要删除选中的 ${this.selectedIds.length} 个房源吗？此操作不可恢复！`,
        success: async (res) => {
          if (res.confirm) {
            // 实现批量删除逻辑
            uni.showToast({
              title: '批量删除功能开发中',
              icon: 'none'
            })
          }
        }
      })
    },

    // 分页改变
    onPageChange(page) {
      this.currentPage = page
      this.loadHouseList()
    },

    // 获取类型文本
    getTypeText(type) {
      return type || '未知'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'available': '可租',
        'rented': '已租',
        'offline': '下线'
      }
      return statusMap[status] || '未知'
    },

    // 获取状态类型
    getStatusType(status) {
      const typeMap = {
        'available': 'success',
        'rented': 'warning',
        'offline': 'error'
      }
      return typeMap[status] || 'default'
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '-'
      const d = new Date(date)
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
    }
  }
}
</script>

<style lang="scss" scoped>
.house-list {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.search-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.search-row {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex-wrap: wrap;
}

.search-item {
  flex: 1;
  min-width: 300rpx;
}

.filter-item {
  min-width: 200rpx;
}

.reset-btn {
  background: #8E8E93;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.stats-bar {
  display: flex;
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  gap: 60rpx;
}

.stat-item {
  text-align: center;
  
  .stat-label {
    display: block;
    font-size: 28rpx;
    color: #666;
    margin-bottom: 10rpx;
  }
  
  .stat-value {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
}

.list-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.house-cover {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  cursor: pointer;
}

.house-info {
  .house-title {
    display: block;
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .house-desc {
    display: block;
    font-size: 24rpx;
    color: #666;
    margin-bottom: 8rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .house-address {
    display: block;
    font-size: 24rpx;
    color: #999;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.price-info {
  .price {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #FF3B30;
  }
  
  .price-unit {
    font-size: 24rpx;
    color: #666;
  }
}

.publisher-info {
  cursor: pointer;
  
  .publisher-name {
    color: #007AFF;
    text-decoration: underline;
  }
}

.date-text {
  font-size: 24rpx;
  color: #666;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.action-btn {
  padding: 8rpx 16rpx;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
  cursor: pointer;
  
  &.view-btn {
    background: #007AFF;
    color: #fff;
  }
  
  &.edit-btn {
    background: #34C759;
    color: #fff;
  }
  
  &.approve-btn {
    background: #FF9500;
    color: #fff;
  }
  
  &.delete-btn {
    background: #FF3B30;
    color: #fff;
  }
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-top: 30rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
}

.selected-count {
  font-size: 28rpx;
  color: #333;
}

.batch-btn {
  padding: 12rpx 24rpx;
  border: none;
  border-radius: 6rpx;
  font-size: 26rpx;
  cursor: pointer;
  
  &.approve-btn {
    background: #FF9500;
    color: #fff;
  }
  
  &.offline-btn {
    background: #8E8E93;
    color: #fff;
  }
  
  &.delete-btn {
    background: #FF3B30;
    color: #fff;
  }
}

.pagination {
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .search-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .stats-bar {
    flex-wrap: wrap;
    gap: 30rpx;
  }
  
  .action-buttons {
    flex-direction: row;
    flex-wrap: wrap;
  }
}
</style>
