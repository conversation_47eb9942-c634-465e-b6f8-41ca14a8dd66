<template>
  <view class="dashboard">
    <!-- 顶部统计卡片 -->
    <view class="stats-cards">
      <uni-stat-panel 
        :items="statsData" 
        :loading="loading"
        @click="onStatsClick"
      />
    </view>

    <!-- 图表区域 -->
    <view class="charts-section">
      <view class="chart-row">
        <!-- 房源趋势图 -->
        <view class="chart-card">
          <view class="chart-header">
            <text class="chart-title">房源发布趋势</text>
            <uni-data-picker 
              v-model="dateRange" 
              :localdata="dateOptions"
              @change="onDateChange"
            />
          </view>
          <view class="chart-content">
            <qiun-data-charts 
              type="line"
              :opts="houseChartOpts"
              :chartData="houseChartData"
              :loading="chartLoading"
            />
          </view>
        </view>

        <!-- 用户注册趋势图 -->
        <view class="chart-card">
          <view class="chart-header">
            <text class="chart-title">用户注册趋势</text>
          </view>
          <view class="chart-content">
            <qiun-data-charts 
              type="line"
              :opts="userChartOpts"
              :chartData="userChartData"
              :loading="chartLoading"
            />
          </view>
        </view>
      </view>

      <view class="chart-row">
        <!-- 房源类型分布 -->
        <view class="chart-card">
          <view class="chart-header">
            <text class="chart-title">房源类型分布</text>
          </view>
          <view class="chart-content">
            <qiun-data-charts 
              type="pie"
              :opts="typeChartOpts"
              :chartData="typeChartData"
              :loading="chartLoading"
            />
          </view>
        </view>

        <!-- 预约状态分布 -->
        <view class="chart-card">
          <view class="chart-header">
            <text class="chart-title">预约状态分布</text>
          </view>
          <view class="chart-content">
            <qiun-data-charts 
              type="pie"
              :opts="appointmentChartOpts"
              :chartData="appointmentChartData"
              :loading="chartLoading"
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 最新动态 -->
    <view class="recent-activities">
      <view class="section-header">
        <text class="section-title">最新动态</text>
        <text class="section-more" @click="viewAllActivities">查看全部</text>
      </view>
      <view class="activity-list">
        <view 
          class="activity-item" 
          v-for="(activity, index) in recentActivities" 
          :key="index"
        >
          <view class="activity-icon" :class="activity.type">
            <uni-icons :type="activity.icon" size="16" color="#fff" />
          </view>
          <view class="activity-content">
            <text class="activity-text">{{ activity.text }}</text>
            <text class="activity-time">{{ formatTime(activity.time) }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'RentalDashboard',
  data() {
    return {
      loading: true,
      chartLoading: true,
      dateRange: 'week',
      
      // 统计数据
      statsData: [
        {
          title: '总用户数',
          value: 0,
          icon: 'admin-icons-user',
          color: '#007AFF'
        },
        {
          title: '总房源数',
          value: 0,
          icon: 'admin-icons-house',
          color: '#34C759'
        },
        {
          title: '今日预约',
          value: 0,
          icon: 'admin-icons-calendar',
          color: '#FF9500'
        },
        {
          title: '待审核房源',
          value: 0,
          icon: 'admin-icons-audit',
          color: '#FF3B30'
        }
      ],

      // 日期选项
      dateOptions: [
        { value: 'week', text: '最近7天' },
        { value: 'month', text: '最近30天' },
        { value: 'quarter', text: '最近3个月' }
      ],

      // 图表配置
      houseChartOpts: {
        color: ['#007AFF'],
        padding: [15, 15, 0, 15],
        enableScroll: false,
        legend: {
          show: false
        },
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2
        },
        extra: {
          line: {
            type: 'curve',
            width: 2
          }
        }
      },

      userChartOpts: {
        color: ['#34C759'],
        padding: [15, 15, 0, 15],
        enableScroll: false,
        legend: {
          show: false
        },
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2
        },
        extra: {
          line: {
            type: 'curve',
            width: 2
          }
        }
      },

      typeChartOpts: {
        color: ['#007AFF', '#34C759', '#FF9500'],
        padding: [5, 5, 5, 5],
        enableScroll: false,
        legend: {
          show: true,
          position: 'right'
        },
        extra: {
          pie: {
            activeOpacity: 0.5,
            activeRadius: 10,
            offsetAngle: 0,
            labelWidth: 15,
            border: true,
            borderWidth: 3,
            borderColor: '#FFFFFF'
          }
        }
      },

      appointmentChartOpts: {
        color: ['#FF9500', '#34C759', '#FF3B30', '#8E8E93'],
        padding: [5, 5, 5, 5],
        enableScroll: false,
        legend: {
          show: true,
          position: 'right'
        },
        extra: {
          pie: {
            activeOpacity: 0.5,
            activeRadius: 10,
            offsetAngle: 0,
            labelWidth: 15,
            border: true,
            borderWidth: 3,
            borderColor: '#FFFFFF'
          }
        }
      },

      // 图表数据
      houseChartData: {},
      userChartData: {},
      typeChartData: {},
      appointmentChartData: {},

      // 最新动态
      recentActivities: []
    }
  },

  onLoad() {
    this.loadDashboardData()
  },

  methods: {
    // 加载仪表板数据
    async loadDashboardData() {
      try {
        this.loading = true
        this.chartLoading = true

        // 并发加载所有数据
        const [statsResult, chartResult, activitiesResult] = await Promise.all([
          this.loadStatsData(),
          this.loadChartData(),
          this.loadRecentActivities()
        ])

        this.loading = false
        this.chartLoading = false
      } catch (error) {
        console.error('加载仪表板数据失败:', error)
        uni.showToast({
          title: '加载数据失败',
          icon: 'none'
        })
        this.loading = false
        this.chartLoading = false
      }
    },

    // 加载统计数据
    async loadStatsData() {
      try {
        const result = await uniCloud.callFunction({
          name: 'system-management',
          data: {
            action: 'getDashboardStats'
          }
        })

        if (result.result.code === 0) {
          const data = result.result.data
          this.statsData[0].value = data.users.total
          this.statsData[1].value = data.houses.total
          this.statsData[2].value = data.appointments.todayNew || 0
          this.statsData[3].value = data.houses.pending
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    // 加载图表数据
    async loadChartData() {
      // 这里应该调用云函数获取图表数据
      // 暂时使用模拟数据
      this.houseChartData = {
        categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
        series: [{
          name: '房源发布',
          data: [35, 36, 31, 33, 13, 34]
        }]
      }

      this.userChartData = {
        categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
        series: [{
          name: '用户注册',
          data: [18, 25, 22, 28, 15, 32]
        }]
      }

      this.typeChartData = {
        series: [{
          name: '房源类型',
          data: [
            { name: '整租', value: 35 },
            { name: '合租', value: 26 },
            { name: '单间', value: 18 }
          ]
        }]
      }

      this.appointmentChartData = {
        series: [{
          name: '预约状态',
          data: [
            { name: '待确认', value: 15 },
            { name: '已确认', value: 25 },
            { name: '已取消', value: 8 },
            { name: '已完成', value: 32 }
          ]
        }]
      }
    },

    // 加载最新动态
    async loadRecentActivities() {
      // 模拟数据
      this.recentActivities = [
        {
          type: 'user',
          icon: 'person-add',
          text: '新用户 张三 注册成功',
          time: new Date(Date.now() - 5 * 60 * 1000)
        },
        {
          type: 'house',
          icon: 'home',
          text: '新房源《阳光小区三室一厅》发布',
          time: new Date(Date.now() - 15 * 60 * 1000)
        },
        {
          type: 'appointment',
          icon: 'calendar',
          text: '用户 李四 预约看房',
          time: new Date(Date.now() - 30 * 60 * 1000)
        }
      ]
    },

    // 统计卡片点击事件
    onStatsClick(item) {
      console.log('点击统计卡片:', item)
      // 根据点击的卡片跳转到对应页面
    },

    // 日期范围改变
    onDateChange(value) {
      this.dateRange = value
      this.loadChartData()
    },

    // 查看全部动态
    viewAllActivities() {
      // 跳转到活动日志页面
    },

    // 格式化时间
    formatTime(time) {
      const now = new Date()
      const diff = now - time
      const minutes = Math.floor(diff / (1000 * 60))
      
      if (minutes < 1) return '刚刚'
      if (minutes < 60) return `${minutes}分钟前`
      
      const hours = Math.floor(minutes / 60)
      if (hours < 24) return `${hours}小时前`
      
      const days = Math.floor(hours / 24)
      return `${days}天前`
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.stats-cards {
  margin-bottom: 30rpx;
}

.charts-section {
  margin-bottom: 30rpx;
}

.chart-row {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
  
  .chart-card {
    flex: 1;
    background: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  }
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  
  .chart-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
}

.chart-content {
  height: 400rpx;
}

.recent-activities {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
  
  .section-more {
    font-size: 28rpx;
    color: #007AFF;
    cursor: pointer;
  }
}

.activity-list {
  .activity-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .activity-icon {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20rpx;
    
    &.user {
      background-color: #007AFF;
    }
    
    &.house {
      background-color: #34C759;
    }
    
    &.appointment {
      background-color: #FF9500;
    }
  }
  
  .activity-content {
    flex: 1;
    
    .activity-text {
      display: block;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .activity-time {
      font-size: 24rpx;
      color: #999;
    }
  }
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .chart-row {
    flex-direction: column;
  }
}
</style>
