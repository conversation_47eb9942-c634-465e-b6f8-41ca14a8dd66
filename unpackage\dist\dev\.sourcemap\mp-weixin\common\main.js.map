{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端/App.vue?1fdf", "webpack:///D:/web/project/前端/App.vue?93b4", "uni-app:///App.vue", "webpack:///D:/web/project/前端/App.vue?7301", "webpack:///D:/web/project/前端/App.vue?9f9a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "config", "productionTip", "App", "mpType", "app", "$mount", "onLaunch", "console", "onShow", "onHide", "methods", "checkUpdate", "updateManager", "uni", "title", "content", "success", "initGlobalConfig", "frontColor", "backgroundColor", "globalData", "systemInfo", "statusBarHeight", "navBarHeight", "userInfo"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAAuE;AAGlI;AACA;AAAgC;AAAA;AALhC;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAK1DC,YAAG,CAACC,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCC,YAAG,CAACC,MAAM,GAAG,KAAK;AAClB,IAAMC,GAAG,GAAG,IAAIL,YAAG,mBACdG,YAAG,EACN;AACF,UAAAE,GAAG,EAACC,MAAM,EAAE,C;;;;;;;;;;;;;ACZZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACa;;;AAG/D;AACmK;AACnK,gBAAgB,gLAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAgmB,CAAgB,4nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;eCCpnB;EACAC;IACAC;;IAEA;IACA;;IAEA;IACA;EACA;EACAC;IACAD;EACA;EACAE;IACAF;EACA;EACAG;IACA;IACAC;MAEA;MAEAC;QACAL;MACA;MAEAK;QACAC;UACAC;UACAC;UACAC;YACA;cACAJ;YACA;UACA;QACA;MACA;MAEAA;QACAL;MACA;IAEA;IAEA;IACAU;MAAA;MACA;MACAJ;QACAK;QACAC;MACA;;MAEA;MACAN;QACAG;UACA;UACAH;;UAEA;UACA;UACA;UACA;QACA;MACA;IACA;EACA;EACAO;IACAC;IACAC;IACAC;IACAC;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzEA;AAAA;AAAA;AAAA;AAAm3B,CAAgB,u3BAAG,EAAC,C;;;;;;;;;;;ACAv4B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';import App from './App'\n\n\nimport Vue from 'vue'\nimport './uni.promisify.adaptor'\nVue.config.productionTip = false\nApp.mpType = 'app'\nconst app = new Vue({\n  ...App\n})\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\nexport default {\r\n  onLaunch: function() {\r\n    console.log('毕业租房信息平台启动')\r\n\r\n    // 检查更新\r\n    this.checkUpdate()\r\n\r\n    // 初始化全局配置\r\n    this.initGlobalConfig()\r\n  },\r\n  onShow: function() {\r\n    console.log('App Show')\r\n  },\r\n  onHide: function() {\r\n    console.log('App Hide')\r\n  },\r\n  methods: {\r\n    // 检查小程序更新\r\n    checkUpdate() {\r\n      // #ifdef MP-WEIXIN\r\n      const updateManager = uni.getUpdateManager()\r\n\r\n      updateManager.onCheckForUpdate(function (res) {\r\n        console.log('检查更新结果:', res.hasUpdate)\r\n      })\r\n\r\n      updateManager.onUpdateReady(function () {\r\n        uni.showModal({\r\n          title: '更新提示',\r\n          content: '新版本已经准备好，是否重启应用？',\r\n          success: function (res) {\r\n            if (res.confirm) {\r\n              updateManager.applyUpdate()\r\n            }\r\n          }\r\n        })\r\n      })\r\n\r\n      updateManager.onUpdateFailed(function () {\r\n        console.log('新版本下载失败')\r\n      })\r\n      // #endif\r\n    },\r\n\r\n    // 初始化全局配置\r\n    initGlobalConfig() {\r\n      // 设置全局导航栏样式\r\n      uni.setNavigationBarColor({\r\n        frontColor: '#000000',\r\n        backgroundColor: '#F8F8F8'\r\n      })\r\n\r\n      // 获取系统信息\r\n      uni.getSystemInfo({\r\n        success: (res) => {\r\n          // 存储系统信息\r\n          uni.setStorageSync('systemInfo', res)\r\n\r\n          // 设置全局变量\r\n          this.globalData.systemInfo = res\r\n          this.globalData.statusBarHeight = res.statusBarHeight\r\n          this.globalData.navBarHeight = res.statusBarHeight + 44\r\n        }\r\n      })\r\n    }\r\n  },\r\n  globalData: {\r\n    systemInfo: null,\r\n    statusBarHeight: 0,\r\n    navBarHeight: 0,\r\n    userInfo: null\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n/* 全局样式 */\r\npage {\r\n  background-color: #f8f9fa;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;\r\n}\r\n\r\n/* 通用样式 */\r\n.container {\r\n  padding: 20rpx;\r\n}\r\n\r\n.section {\r\n  background: #fff;\r\n  border-radius: 20rpx;\r\n  margin-bottom: 20rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.section-header {\r\n  padding: 30rpx;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.section-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n\r\n.section-content {\r\n  padding: 30rpx;\r\n}\r\n\r\n/* 按钮样式 */\r\n.btn {\r\n  border-radius: 12rpx;\r\n  font-size: 28rpx;\r\n  padding: 20rpx 40rpx;\r\n  border: none;\r\n}\r\n\r\n.btn-primary {\r\n  background: linear-gradient(45deg, #007aff, #0056d3);\r\n  color: #fff;\r\n}\r\n\r\n.btn-secondary {\r\n  background: #f8f9fa;\r\n  color: #666;\r\n  border: 1rpx solid #e0e0e0;\r\n}\r\n\r\n.btn-success {\r\n  background: #10c560;\r\n  color: #fff;\r\n}\r\n\r\n.btn-warning {\r\n  background: #ff9800;\r\n  color: #fff;\r\n}\r\n\r\n.btn-danger {\r\n  background: #f44336;\r\n  color: #fff;\r\n}\r\n\r\n.btn-small {\r\n  padding: 12rpx 24rpx;\r\n  font-size: 24rpx;\r\n}\r\n\r\n.btn-large {\r\n  padding: 30rpx 60rpx;\r\n  font-size: 32rpx;\r\n}\r\n\r\n/* 文本样式 */\r\n.text-primary {\r\n  color: #007aff;\r\n}\r\n\r\n.text-secondary {\r\n  color: #666;\r\n}\r\n\r\n.text-success {\r\n  color: #10c560;\r\n}\r\n\r\n.text-warning {\r\n  color: #ff9800;\r\n}\r\n\r\n.text-danger {\r\n  color: #f44336;\r\n}\r\n\r\n.text-muted {\r\n  color: #999;\r\n}\r\n\r\n.text-center {\r\n  text-align: center;\r\n}\r\n\r\n.text-left {\r\n  text-align: left;\r\n}\r\n\r\n.text-right {\r\n  text-align: right;\r\n}\r\n\r\n/* 间距样式 */\r\n.m-0 { margin: 0; }\r\n.m-1 { margin: 10rpx; }\r\n.m-2 { margin: 20rpx; }\r\n.m-3 { margin: 30rpx; }\r\n.m-4 { margin: 40rpx; }\r\n\r\n.mt-0 { margin-top: 0; }\r\n.mt-1 { margin-top: 10rpx; }\r\n.mt-2 { margin-top: 20rpx; }\r\n.mt-3 { margin-top: 30rpx; }\r\n.mt-4 { margin-top: 40rpx; }\r\n\r\n.mb-0 { margin-bottom: 0; }\r\n.mb-1 { margin-bottom: 10rpx; }\r\n.mb-2 { margin-bottom: 20rpx; }\r\n.mb-3 { margin-bottom: 30rpx; }\r\n.mb-4 { margin-bottom: 40rpx; }\r\n\r\n.p-0 { padding: 0; }\r\n.p-1 { padding: 10rpx; }\r\n.p-2 { padding: 20rpx; }\r\n.p-3 { padding: 30rpx; }\r\n.p-4 { padding: 40rpx; }\r\n\r\n.pt-0 { padding-top: 0; }\r\n.pt-1 { padding-top: 10rpx; }\r\n.pt-2 { padding-top: 20rpx; }\r\n.pt-3 { padding-top: 30rpx; }\r\n.pt-4 { padding-top: 40rpx; }\r\n\r\n.pb-0 { padding-bottom: 0; }\r\n.pb-1 { padding-bottom: 10rpx; }\r\n.pb-2 { padding-bottom: 20rpx; }\r\n.pb-3 { padding-bottom: 30rpx; }\r\n.pb-4 { padding-bottom: 40rpx; }\r\n\r\n/* 布局样式 */\r\n.flex {\r\n  display: flex;\r\n}\r\n\r\n.flex-column {\r\n  flex-direction: column;\r\n}\r\n\r\n.flex-row {\r\n  flex-direction: row;\r\n}\r\n\r\n.flex-wrap {\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.justify-start {\r\n  justify-content: flex-start;\r\n}\r\n\r\n.justify-center {\r\n  justify-content: center;\r\n}\r\n\r\n.justify-end {\r\n  justify-content: flex-end;\r\n}\r\n\r\n.justify-between {\r\n  justify-content: space-between;\r\n}\r\n\r\n.justify-around {\r\n  justify-content: space-around;\r\n}\r\n\r\n.align-start {\r\n  align-items: flex-start;\r\n}\r\n\r\n.align-center {\r\n  align-items: center;\r\n}\r\n\r\n.align-end {\r\n  align-items: flex-end;\r\n}\r\n\r\n.flex-1 {\r\n  flex: 1;\r\n}\r\n\r\n/* 边框样式 */\r\n.border {\r\n  border: 1rpx solid #e0e0e0;\r\n}\r\n\r\n.border-top {\r\n  border-top: 1rpx solid #e0e0e0;\r\n}\r\n\r\n.border-bottom {\r\n  border-bottom: 1rpx solid #e0e0e0;\r\n}\r\n\r\n.border-radius {\r\n  border-radius: 12rpx;\r\n}\r\n\r\n.border-radius-large {\r\n  border-radius: 20rpx;\r\n}\r\n\r\n/* 阴影样式 */\r\n.shadow {\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.shadow-large {\r\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 动画样式 */\r\n.fade-in {\r\n  animation: fadeIn 0.3s ease-in-out;\r\n}\r\n\r\n.slide-up {\r\n  animation: slideUp 0.3s ease-in-out;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes slideUp {\r\n  from {\r\n    transform: translateY(100rpx);\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    transform: translateY(0);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n/* 响应式样式 */\r\n@media screen and (max-width: 750rpx) {\r\n  .container {\r\n    padding: 15rpx;\r\n  }\r\n\r\n  .section {\r\n    margin-bottom: 15rpx;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751999906\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}