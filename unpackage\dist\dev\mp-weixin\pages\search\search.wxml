<view class="search-container data-v-4cedc0c6"><view class="search-bar data-v-4cedc0c6" style="{{'padding-top:'+(statusBarHeight*2+60+'rpx')+';'}}"><view class="search-input-wrapper data-v-4cedc0c6"><uni-icons vue-id="50cad900-1" type="search" size="18" color="#999" class="data-v-4cedc0c6" bind:__l="__l"></uni-icons><input class="search-input data-v-4cedc0c6" type="text" placeholder="搜索房源、地址、关键词" focus="{{true}}" data-event-opts="{{[['input',[['__set_model',['','keyword','$event',[]]],['onInput',['$event']]]],['confirm',[['handleSearch',['$event']]]]]}}" value="{{keyword}}" bindinput="__e" bindconfirm="__e"/><block wx:if="{{keyword}}"><view data-event-opts="{{[['tap',[['clearKeyword',['$event']]]]]}}" class="clear-btn data-v-4cedc0c6" bindtap="__e"><uni-icons vue-id="50cad900-2" type="clear" size="16" color="#999" class="data-v-4cedc0c6" bind:__l="__l"></uni-icons></view></block></view><text data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="cancel-btn data-v-4cedc0c6" bindtap="__e">取消</text></view><block wx:if="{{$root.g0}}"><view class="suggestions data-v-4cedc0c6"><block wx:for="{{suggestions}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectSuggestion',['$0'],[[['suggestions','',index]]]]]]]}}" class="suggestion-item data-v-4cedc0c6" bindtap="__e"><uni-icons vue-id="{{'50cad900-3-'+index}}" type="search" size="16" color="#999" class="data-v-4cedc0c6" bind:__l="__l"></uni-icons><text class="suggestion-text data-v-4cedc0c6">{{item}}</text></view></block></view></block><block wx:if="{{$root.g1}}"><view class="search-history data-v-4cedc0c6"><view class="history-header data-v-4cedc0c6"><text class="history-title data-v-4cedc0c6">搜索历史</text><text data-event-opts="{{[['tap',[['clearHistory',['$event']]]]]}}" class="clear-history data-v-4cedc0c6" bindtap="__e">清空</text></view><view class="history-tags data-v-4cedc0c6"><block wx:for="{{searchHistory}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectHistory',['$0'],[[['searchHistory','',index]]]]]]]}}" class="history-tag data-v-4cedc0c6" bindtap="__e"><text class="tag-text data-v-4cedc0c6">{{item}}</text><uni-icons vue-id="{{'50cad900-4-'+index}}" type="close" size="14" color="#999" data-event-opts="{{[['^click',[['removeHistory',[index]]]]]}}" catch:click="__e" class="data-v-4cedc0c6" bind:__l="__l"></uni-icons></view></block></view></view></block><block wx:if="{{!keyword}}"><view class="hot-search data-v-4cedc0c6"><view class="hot-header data-v-4cedc0c6"><text class="hot-title data-v-4cedc0c6">热门搜索</text></view><view class="hot-tags data-v-4cedc0c6"><block wx:for="{{hotKeywords}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectHot',['$0'],[[['hotKeywords','',index]]]]]]]}}" class="hot-tag data-v-4cedc0c6" bindtap="__e"><text class="tag-text data-v-4cedc0c6">{{item}}</text></view></block></view></view></block><block wx:if="{{showResults}}"><view class="search-results data-v-4cedc0c6"><view class="filter-bar data-v-4cedc0c6"><view data-event-opts="{{[['tap',[['showLocationFilter',['$event']]]]]}}" class="filter-item data-v-4cedc0c6" bindtap="__e"><text class="filter-text data-v-4cedc0c6">{{selectedLocation||'位置'}}</text><uni-icons vue-id="50cad900-5" type="arrowdown" size="12" color="#666" class="data-v-4cedc0c6" bind:__l="__l"></uni-icons></view><view data-event-opts="{{[['tap',[['showPriceFilter',['$event']]]]]}}" class="filter-item data-v-4cedc0c6" bindtap="__e"><text class="filter-text data-v-4cedc0c6">{{selectedPrice.label||'价格'}}</text><uni-icons vue-id="50cad900-6" type="arrowdown" size="12" color="#666" class="data-v-4cedc0c6" bind:__l="__l"></uni-icons></view><view data-event-opts="{{[['tap',[['showTypeFilter',['$event']]]]]}}" class="filter-item data-v-4cedc0c6" bindtap="__e"><text class="filter-text data-v-4cedc0c6">{{selectedType.label||'类型'}}</text><uni-icons vue-id="50cad900-7" type="arrowdown" size="12" color="#666" class="data-v-4cedc0c6" bind:__l="__l"></uni-icons></view><view data-event-opts="{{[['tap',[['showMoreFilter',['$event']]]]]}}" class="filter-item data-v-4cedc0c6" bindtap="__e"><text class="filter-text data-v-4cedc0c6">筛选</text><uni-icons vue-id="50cad900-8" type="tune" size="14" color="#666" class="data-v-4cedc0c6" bind:__l="__l"></uni-icons></view></view><scroll-view class="result-list data-v-4cedc0c6" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><block wx:if="{{$root.g2>0}}"><view class="result-header data-v-4cedc0c6"><text class="result-count data-v-4cedc0c6">{{"找到 "+totalCount+" 套房源"}}</text><view data-event-opts="{{[['tap',[['showSortFilter',['$event']]]]]}}" class="sort-btn data-v-4cedc0c6" bindtap="__e"><text class="sort-text data-v-4cedc0c6">{{selectedSort.label}}</text><uni-icons vue-id="50cad900-9" type="arrowdown" size="12" color="#666" class="data-v-4cedc0c6" bind:__l="__l"></uni-icons></view></view></block><block wx:for="{{$root.l0}}" wx:for-item="house" wx:for-index="__i0__" wx:key="_id"><view data-event-opts="{{[['tap',[['toHouseDetail',['$0'],[[['searchResults','_id',house.$orig._id,'_id']]]]]]]}}" class="house-item data-v-4cedc0c6" bindtap="__e"><view class="house-image data-v-4cedc0c6"><image src="{{house.$orig.images&&house.$orig.images[0]||'/static/default-house.png'}}" mode="aspectFill" class="data-v-4cedc0c6"></image><view class="house-type data-v-4cedc0c6">{{house.m0}}</view></view><view class="house-info data-v-4cedc0c6"><text class="house-title data-v-4cedc0c6">{{house.$orig.title}}</text><view class="house-tags data-v-4cedc0c6"><block wx:if="{{house.$orig.room_count}}"><text class="tag data-v-4cedc0c6">{{house.$orig.room_count+"室"}}</text></block><block wx:if="{{house.$orig.hall_count}}"><text class="tag data-v-4cedc0c6">{{house.$orig.hall_count+"厅"}}</text></block><block wx:if="{{house.$orig.area}}"><text class="tag data-v-4cedc0c6">{{house.$orig.area+"㎡"}}</text></block></view><view class="house-location data-v-4cedc0c6"><uni-icons vue-id="{{'50cad900-10-'+__i0__}}" type="location" size="12" color="#999" class="data-v-4cedc0c6" bind:__l="__l"></uni-icons><text class="location-text data-v-4cedc0c6">{{house.$orig.location.district+" "+house.$orig.location.address}}</text></view><view class="house-bottom data-v-4cedc0c6"><view class="price-info data-v-4cedc0c6"><text class="price data-v-4cedc0c6">{{"¥"+house.$orig.price}}</text><text class="price-unit data-v-4cedc0c6">/月</text></view><view class="house-stats data-v-4cedc0c6"><text class="stat-item data-v-4cedc0c6"><uni-icons vue-id="{{'50cad900-11-'+__i0__}}" type="eye" size="12" color="#999" class="data-v-4cedc0c6" bind:__l="__l"></uni-icons>{{''+(house.$orig.view_count||0)+''}}</text><text class="stat-item data-v-4cedc0c6"><uni-icons vue-id="{{'50cad900-12-'+__i0__}}" type="heart" size="12" color="#999" class="data-v-4cedc0c6" bind:__l="__l"></uni-icons>{{''+(house.$orig.favorite_count||0)+''}}</text></view></view></view></view></block><block wx:if="{{$root.g3>0}}"><view class="load-status data-v-4cedc0c6"><block wx:if="{{loading}}"><text class="data-v-4cedc0c6">加载中...</text></block><block wx:else><block wx:if="{{noMore}}"><text class="data-v-4cedc0c6">没有更多了</text></block></block></view></block><block wx:if="{{$root.g4}}"><view class="empty-state data-v-4cedc0c6"><image src="/static/empty-search.png" mode="aspectFit" class="data-v-4cedc0c6"></image><text class="empty-text data-v-4cedc0c6">没有找到相关房源</text><text class="empty-tip data-v-4cedc0c6">试试其他关键词或调整筛选条件</text></view></block></scroll-view></view></block></view>