
.favorites-container.data-v-edb94f68 {
  height: 100vh;
  background: #f8f9fa;
}
.house-list.data-v-edb94f68 {
  height: 100%;
  padding: 20rpx;
}
.house-item.data-v-edb94f68 {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  display: flex;
}
.house-image.data-v-edb94f68 {
  position: relative;
  width: 240rpx;
  height: 200rpx;
  flex-shrink: 0;
}
.house-image image.data-v-edb94f68 {
  width: 100%;
  height: 100%;
}
.house-type.data-v-edb94f68 {
  position: absolute;
  top: 15rpx;
  left: 15rpx;
  background: rgba(0, 122, 255, 0.9);
  color: #fff;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}
.house-info.data-v-edb94f68 {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.house-title.data-v-edb94f68 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.house-desc.data-v-edb94f68 {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.house-tags.data-v-edb94f68 {
  display: flex;
  gap: 8rpx;
  margin-bottom: 10rpx;
  flex-wrap: wrap;
}
.tag.data-v-edb94f68 {
  background: #f0f0f0;
  color: #666;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  font-size: 22rpx;
}
.house-location.data-v-edb94f68 {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}
.location-text.data-v-edb94f68 {
  font-size: 24rpx;
  color: #999;
  margin-left: 6rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.house-bottom.data-v-edb94f68 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.price-info.data-v-edb94f68 {
  display: flex;
  align-items: baseline;
}
.price.data-v-edb94f68 {
  font-size: 30rpx;
  font-weight: bold;
  color: #ff6b6b;
}
.price-unit.data-v-edb94f68 {
  font-size: 20rpx;
  color: #999;
  margin-left: 4rpx;
}
.favorite-time.data-v-edb94f68 {
  font-size: 22rpx;
  color: #ccc;
}
.house-actions.data-v-edb94f68 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20rpx;
  gap: 20rpx;
  border-left: 1rpx solid #f0f0f0;
}
.action-btn.data-v-edb94f68 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-btn.unfavorite.data-v-edb94f68 {
  background: #ffebee;
}
.action-btn.contact.data-v-edb94f68 {
  background: #e3f2fd;
}
.load-status.data-v-edb94f68 {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}
.empty-state.data-v-edb94f68 {
  text-align: center;
  padding: 100rpx 40rpx;
}
.empty-state image.data-v-edb94f68 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}
.empty-text.data-v-edb94f68 {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.empty-tip.data-v-edb94f68 {
  display: block;
  font-size: 24rpx;
  color: #ccc;
  margin-bottom: 40rpx;
}
.browse-btn.data-v-edb94f68 {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

