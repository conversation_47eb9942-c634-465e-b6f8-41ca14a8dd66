<view class="publish-container data-v-62878d77"><scroll-view class="content data-v-62878d77" scroll-y="{{true}}"><form data-event-opts="{{[['submit',[['handleSubmit',['$event']]]]]}}" bindsubmit="__e" class="data-v-62878d77"><view class="form-section data-v-62878d77"><view class="section-title data-v-62878d77">基本信息</view><view class="form-item data-v-62878d77"><text class="label data-v-62878d77">房源标题 *</text><input class="input data-v-62878d77" type="text" placeholder="请输入房源标题" maxlength="50" data-event-opts="{{[['input',[['__set_model',['$0','title','$event',[]],['form']]]]]}}" value="{{form.title}}" bindinput="__e"/></view><view class="form-item data-v-62878d77"><text class="label data-v-62878d77">房源描述</text><textarea class="textarea data-v-62878d77" placeholder="请详细描述房源情况，如周边环境、交通等" maxlength="500" data-event-opts="{{[['input',[['__set_model',['$0','description','$event',[]],['form']]]]]}}" value="{{form.description}}" bindinput="__e"></textarea></view><view class="form-item data-v-62878d77"><text class="label data-v-62878d77">房源类型 *</text><picker mode="selector" range="{{houseTypes}}" range-key="label" value="{{typeIndex}}" data-event-opts="{{[['change',[['onTypeChange',['$event']]]]]}}" bindchange="__e" class="data-v-62878d77"><view class="picker data-v-62878d77">{{''+(form.type?$root.m0:'请选择房源类型')+''}}<uni-icons vue-id="232abb22-1" type="arrowright" size="16" color="#ccc" class="data-v-62878d77" bind:__l="__l"></uni-icons></view></picker></view></view><view class="form-section data-v-62878d77"><view class="section-title data-v-62878d77">房屋信息</view><view class="form-row data-v-62878d77"><view class="form-item half data-v-62878d77"><text class="label data-v-62878d77">租金(元/月) *</text><input class="input data-v-62878d77" type="number" placeholder="租金" data-event-opts="{{[['input',[['__set_model',['$0','price','$event',[]],['form']]]]]}}" value="{{form.price}}" bindinput="__e"/></view><view class="form-item half data-v-62878d77"><text class="label data-v-62878d77">押金(元)</text><input class="input data-v-62878d77" type="number" placeholder="押金" data-event-opts="{{[['input',[['__set_model',['$0','deposit','$event',[]],['form']]]]]}}" value="{{form.deposit}}" bindinput="__e"/></view></view><view class="form-row data-v-62878d77"><view class="form-item half data-v-62878d77"><text class="label data-v-62878d77">面积(㎡)</text><input class="input data-v-62878d77" type="number" placeholder="面积" data-event-opts="{{[['input',[['__set_model',['$0','area','$event',[]],['form']]]]]}}" value="{{form.area}}" bindinput="__e"/></view><view class="form-item half data-v-62878d77"><text class="label data-v-62878d77">朝向</text><picker mode="selector" range="{{orientations}}" value="{{orientationIndex}}" data-event-opts="{{[['change',[['onOrientationChange',['$event']]]]]}}" bindchange="__e" class="data-v-62878d77"><view class="picker data-v-62878d77">{{''+(form.orientation||'请选择')+''}}<uni-icons vue-id="232abb22-2" type="arrowright" size="16" color="#ccc" class="data-v-62878d77" bind:__l="__l"></uni-icons></view></picker></view></view><view class="form-row data-v-62878d77"><view class="form-item third data-v-62878d77"><text class="label data-v-62878d77">房间数</text><input class="input data-v-62878d77" type="number" placeholder="房间" data-event-opts="{{[['input',[['__set_model',['$0','room_count','$event',[]],['form']]]]]}}" value="{{form.room_count}}" bindinput="__e"/></view><view class="form-item third data-v-62878d77"><text class="label data-v-62878d77">客厅数</text><input class="input data-v-62878d77" type="number" placeholder="客厅" data-event-opts="{{[['input',[['__set_model',['$0','hall_count','$event',[]],['form']]]]]}}" value="{{form.hall_count}}" bindinput="__e"/></view><view class="form-item third data-v-62878d77"><text class="label data-v-62878d77">卫生间</text><input class="input data-v-62878d77" type="number" placeholder="卫生间" data-event-opts="{{[['input',[['__set_model',['$0','bathroom_count','$event',[]],['form']]]]]}}" value="{{form.bathroom_count}}" bindinput="__e"/></view></view><view class="form-row data-v-62878d77"><view class="form-item half data-v-62878d77"><text class="label data-v-62878d77">楼层</text><input class="input data-v-62878d77" type="number" placeholder="楼层" data-event-opts="{{[['input',[['__set_model',['$0','floor','$event',[]],['form']]]]]}}" value="{{form.floor}}" bindinput="__e"/></view><view class="form-item half data-v-62878d77"><text class="label data-v-62878d77">总楼层</text><input class="input data-v-62878d77" type="number" placeholder="总楼层" data-event-opts="{{[['input',[['__set_model',['$0','total_floors','$event',[]],['form']]]]]}}" value="{{form.total_floors}}" bindinput="__e"/></view></view><view class="form-item data-v-62878d77"><text class="label data-v-62878d77">装修情况</text><picker mode="selector" range="{{decorationTypes}}" value="{{decorationIndex}}" data-event-opts="{{[['change',[['onDecorationChange',['$event']]]]]}}" bindchange="__e" class="data-v-62878d77"><view class="picker data-v-62878d77">{{''+(form.decoration||'请选择装修情况')+''}}<uni-icons vue-id="232abb22-3" type="arrowright" size="16" color="#ccc" class="data-v-62878d77" bind:__l="__l"></uni-icons></view></picker></view></view><view class="form-section data-v-62878d77"><view class="section-title data-v-62878d77">房屋设施</view><view class="facilities-grid data-v-62878d77"><block wx:for="{{$root.l0}}" wx:for-item="facility" wx:for-index="__i0__" wx:key="value"><view data-event-opts="{{[['tap',[['toggleFacility',['$0'],[[['facilitiesOptions','value',facility.$orig.value,'value']]]]]]]}}" class="{{['facility-item','data-v-62878d77',(facility.g0)?'active':'']}}" bindtap="__e"><view class="facility-icon data-v-62878d77">{{facility.$orig.icon||'📦'}}</view><text class="facility-text data-v-62878d77">{{facility.$orig.label}}</text><block wx:if="{{facility.g1}}"><view class="facility-check data-v-62878d77"><uni-icons vue-id="{{'232abb22-4-'+__i0__}}" type="checkmarkempty" size="16" color="#fff" class="data-v-62878d77" bind:__l="__l"></uni-icons></view></block></view></block></view></view><view class="form-section data-v-62878d77"><view class="section-title data-v-62878d77">房源图片</view><view class="image-upload data-v-62878d77"><view class="image-list data-v-62878d77"><block wx:for="{{form.images}}" wx:for-item="image" wx:for-index="index" wx:key="index"><view class="image-item data-v-62878d77"><image src="{{image}}" mode="aspectFill" class="data-v-62878d77"></image><view data-event-opts="{{[['tap',[['removeImage',[index]]]]]}}" class="image-delete data-v-62878d77" bindtap="__e"><uni-icons vue-id="{{'232abb22-5-'+index}}" type="close" size="16" color="#fff" class="data-v-62878d77" bind:__l="__l"></uni-icons></view></view></block><block wx:if="{{$root.g2<9}}"><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="image-add data-v-62878d77" bindtap="__e"><uni-icons vue-id="232abb22-6" type="plus" size="32" color="#ccc" class="data-v-62878d77" bind:__l="__l"></uni-icons><text class="add-text data-v-62878d77">添加图片</text></view></block></view><text class="image-tip data-v-62878d77">最多上传9张图片，第一张为封面图</text></view></view><view class="form-section data-v-62878d77"><view class="section-title data-v-62878d77">位置信息</view><view class="form-item data-v-62878d77"><text class="label data-v-62878d77">详细地址 *</text><input class="input data-v-62878d77" type="text" placeholder="请输入详细地址" data-event-opts="{{[['input',[['__set_model',['$0','address','$event',[]],['form.location']]]]]}}" value="{{form.location.address}}" bindinput="__e"/></view><view class="form-item data-v-62878d77"><text class="label data-v-62878d77">选择位置</text><view data-event-opts="{{[['tap',[['chooseLocation',['$event']]]]]}}" class="location-picker data-v-62878d77" bindtap="__e"><view class="location-content data-v-62878d77"><text class="{{['location-text','data-v-62878d77',(!form.location.address)?'placeholder':'']}}">{{''+(form.location.address||'点击选择位置')+''}}</text><block wx:if="{{form.location.name}}"><text class="location-name data-v-62878d77">{{form.location.name}}</text></block></view><uni-icons vue-id="232abb22-7" type="location" size="16" color="#007aff" class="data-v-62878d77" bind:__l="__l"></uni-icons></view></view></view><view class="form-section data-v-62878d77"><view class="section-title data-v-62878d77">联系方式</view><view class="form-item data-v-62878d77"><text class="label data-v-62878d77">联系人 *</text><input class="input data-v-62878d77" type="text" placeholder="请输入联系人姓名" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['form.contact']]]]]}}" value="{{form.contact.name}}" bindinput="__e"/></view><view class="form-item data-v-62878d77"><text class="label data-v-62878d77">联系电话 *</text><input class="input data-v-62878d77" type="number" placeholder="请输入联系电话" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['form.contact']]]]]}}" value="{{form.contact.phone}}" bindinput="__e"/></view><view class="form-item data-v-62878d77"><text class="label data-v-62878d77">微信号</text><input class="input data-v-62878d77" type="text" placeholder="请输入微信号" data-event-opts="{{[['input',[['__set_model',['$0','wechat','$event',[]],['form.contact']]]]]}}" value="{{form.contact.wechat}}" bindinput="__e"/></view></view><view class="submit-section data-v-62878d77"><button class="submit-btn data-v-62878d77" disabled="{{!canSubmit}}" data-event-opts="{{[['tap',[['handleSubmit',['$event']]]]]}}" bindtap="__e">{{''+(loading?'发布中...':'发布房源')+''}}</button><view class="debug-info data-v-62878d77" style="margin-top:20rpx;font-size:24rpx;color:#666;">{{'调试信息: canSubmit='+canSubmit+", loading="+loading+''}}</view></view></form></scroll-view></view>